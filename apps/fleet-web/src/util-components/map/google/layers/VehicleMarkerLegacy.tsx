import { memo, useEffect, useState, type CSSProperties } from 'react'
import type * as React from 'react'
import { round } from 'lodash'
import { styled } from '@karoo-ui/core'

import type { GPSFixType, PositionDescription, VehicleId, VehicleType } from 'api/types'
import type { FetchVehicles } from 'api/vehicles/types'
import {
  getCarpoolSetting,
  getIsSomeMapVehicleLabelAvailable,
  getMapVehicleLabels,
} from 'duxs/user'
import { METERS_PER_TA } from 'duxs/vehicles'
import ReVehicleMarkerInfo from 'src/components/_map/_markers/ReVehicle/Label'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import { useUserFormattedClock } from 'src/modules/components/connected/UserFormattedClock'
import { useUserFormatLengthInKmOrMiles } from 'src/modules/components/connected/UserFormattedLengthInKmOrMiles'
import { useTypedSelector } from 'src/redux-hooks'
import type { FixMeAny } from 'src/types'
import type { ExcludeStrict } from 'src/types/utils'
import VehicleCarpoolStatusIcon from 'src/util-components/vehicle-carpool-status-icon'

import { VehicleMarkerBaseHeadless } from './VehicleMarkerBaseHeadless'

type Props = {
  vehicle: {
    id: VehicleId
    type: VehicleType
    isWifi?: boolean
    gpsFixType?: GPSFixType
    iconColor?: string
    statusClassName?: FetchVehicles.Vehicle['statusClassName']
    carpoolStatus?: FetchVehicles.Vehicle['carpool_status']

    // Optional
    name?: string
    defaultDriver?: string | null
    driverName?: FixMeAny
    unitRawClock?: string | null
    registration?: string
    description?: string
    description1?: string
    description2?: string
    odometer?: number | null
    positionDescription?: string | PositionDescription
  }
  position: {
    lat: number
    lng: number
  }
  statusClassName: string
  bearing?: number
  isFocused?: boolean
  onVehicleClick?: (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    info: { vehicleId: VehicleId },
  ) => void
  isHovered: boolean
  showLabel: boolean
  ghost?: boolean
  timelineSliderPlaying?: boolean
  useIconColor?: boolean
  onVehicleHover?: {
    onMouseEnter?: (args: { vehicleId: VehicleId }) => void
    onMouseLeave?: (args: { vehicleId: VehicleId }) => void
  }
  positionType?: string | null
  width?: number
  radius?: number
  zoom?: number
  showBearing?: boolean
}

/**
 * @deprecated Only here because of old Delivery code. Remove when old delivery is removed.
 */
const VehicleMarkerLegacy = ({
  vehicle,
  position,
  statusClassName,
  isFocused = false,
  onVehicleClick = () => {},
  isHovered,
  showLabel,
  ghost = false,
  timelineSliderPlaying = false,
  useIconColor = false,
  onVehicleHover = {
    onMouseEnter: undefined,
    onMouseLeave: undefined,
  },
  positionType = null,
  showBearing = false,
  bearing = 0,
  width = 0,
  radius = 0,
  zoom = 0,
}: Props) => {
  const [isMarkerHovered, setIsMarkerHovered] = useState(false)

  const isSomeMapVehicleLabelAvailable = useTypedSelector(
    getIsSomeMapVehicleLabelAvailable,
  )

  const mapVehicleLabels = useTypedSelector(getMapVehicleLabels)
  const userHasCarpool = useTypedSelector(getCarpoolSetting)
  const { formatLengthInKmOrMiles } = useUserFormatLengthInKmOrMiles()
  const { formatClock } = useUserFormattedClock()

  const onEffectMouseLeave = useEffectEvent(() => {
    onVehicleHover.onMouseLeave?.({ vehicleId: vehicle.id })
  })
  useEffect(
    () => () => {
      if (isMarkerHovered) {
        onEffectMouseLeave()
      }
    },
    [isMarkerHovered],
  )

  const polarToCartesian = (
    centerX: number,
    centerY: number,
    radius: number,
    angleInDegrees: number,
  ) => {
    const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180

    return {
      x: centerX + radius * Math.cos(angleInRadians),
      y: centerY + radius * Math.sin(angleInRadians),
    }
  }

  const describeArc = (x = 58, y = 58, radius = 58, startAngle = 0, endAngle = 30) => {
    const start = polarToCartesian(x, y, radius, endAngle)
    const end = polarToCartesian(x, y, radius, startAngle)

    const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1'

    return [
      'M',
      x,
      y,
      'L',
      start.x,
      start.y,
      'A',
      radius,
      radius,
      0,
      largeArcFlag,
      0,
      end.x,
      end.y,
    ].join(' ')
  }

  const renderARC = () => {
    const readTimeAcceleration = radius === 0 ? 1 : (radius ?? 1)
    const startDegree = bearing - (width ?? 0) / 2
    const endDegree = bearing + (width ?? 0) / 2
    const pixelPerUnit =
      METERS_PER_TA /
      ((156543.03392 * Math.cos((position.lat * Math.PI) / 180)) / 2 ** zoom)
    const computedRadius = round(pixelPerUnit * readTimeAcceleration)
    const containerStyle: CSSProperties = {
      position: 'absolute',
      top: `-${computedRadius}px`,
      left: `-${computedRadius}px`,
    }
    const svgStyle = {
      width: `${computedRadius * 2}px`,
      height: `${computedRadius * 2}px`,
    }

    const d = describeArc(
      computedRadius,
      computedRadius,
      computedRadius,
      startDegree,
      endDegree,
    )

    return (
      <div style={containerStyle}>
        <svg style={svgStyle}>
          {width >= 360 ? (
            <circle
              className="VehicleMarker-arc-path"
              cx={computedRadius}
              cy={computedRadius}
              r={computedRadius}
            />
          ) : (
            <path
              className="VehicleMarker-arc-path"
              d={d}
            />
          )}
        </svg>
      </div>
    )
  }

  return (
    <VehicleMarkerBaseHeadless
      statusClassName={statusClassName as FixMeAny}
      vehicleGpsFixType={vehicle.gpsFixType}
      vehicleId={vehicle.id}
      vehicleType={vehicle.type}
      onVehicleClick={onVehicleClick}
      vehicleIsWifi={vehicle.isWifi}
      vehicleIconColor={useIconColor ? vehicle.iconColor : undefined}
      onMouseEnter={() => {
        setIsMarkerHovered(true)
        onVehicleHover.onMouseEnter?.({ vehicleId: vehicle.id })
      }}
      onMouseLeave={() =>
        onVehicleHover.onMouseLeave
          ? onVehicleHover.onMouseLeave({ vehicleId: vehicle.id })
          : null
      }
    >
      {({ renderBearing, renderGlowAnimation, renderMarkerContent }) => (
        <>
          {isHovered ? renderGlowAnimation() : null}
          {!ghost && (showBearing || isFocused) && (
            <>
              {positionType === 'ARC'
                ? renderARC()
                : showBearing && renderBearing({ bearing })}
            </>
          )}
          {renderMarkerContent({
            extraClassName: timelineSliderPlaying ? 'fade' : '',
            isFocused,
            children: (
              <>
                {!isFocused && showLabel && isSomeMapVehicleLabelAvailable && (
                  <ReVehicleMarkerInfo
                    vehicleLabels={{
                      name: mapVehicleLabels.name
                        ? { value: vehicle.name as FixMeAny }
                        : false,
                      registration: mapVehicleLabels.registration
                        ? { value: vehicle.registration as FixMeAny }
                        : false,
                      description: mapVehicleLabels.description
                        ? { value: vehicle.description as FixMeAny }
                        : false,
                      description1: mapVehicleLabels.description1
                        ? { value: vehicle.description1 as FixMeAny }
                        : false,
                      description2: mapVehicleLabels.description2
                        ? { value: vehicle.description2 as FixMeAny }
                        : false,
                      driver: mapVehicleLabels.driver
                        ? {
                            defaultDriver: vehicle.defaultDriver as FixMeAny,
                            driverName: vehicle.driverName as FixMeAny,
                          }
                        : false,
                      location: mapVehicleLabels.location
                        ? {
                            gpsFixType: vehicle.gpsFixType as FixMeAny,
                            positionDescription:
                              vehicle.positionDescription as FixMeAny,
                          }
                        : false,
                      odometer: mapVehicleLabels.odometer
                        ? {
                            value: vehicle.odometer as FixMeAny,
                            formatLengthInKmOrMiles,
                          }
                        : false,
                      unitRawClock: mapVehicleLabels.unitRawClock
                        ? { value: vehicle.unitRawClock as FixMeAny, formatClock }
                        : false,
                    }}
                  />
                )}
                {userHasCarpool && (
                  <IconBox>
                    <VehicleCarpoolStatusIcon
                      statusClassName={
                        vehicle.statusClassName as ExcludeStrict<
                          typeof vehicle.statusClassName,
                          undefined
                        >
                      }
                      carpoolStatus={vehicle.carpoolStatus}
                    />
                  </IconBox>
                )}
              </>
            ),
          })}
        </>
      )}
    </VehicleMarkerBaseHeadless>
  )
}

export default memo(VehicleMarkerLegacy)

const IconBox = styled('div')`
  position: absolute;
  left: 21px;
  top: 20px;
`
