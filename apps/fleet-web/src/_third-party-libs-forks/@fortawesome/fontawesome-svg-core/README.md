# @fortawesome/fontawesome-svg-core - SVG with JavaScript version

> "I came here to chew bubblegum and install Font Awesome 5 - and I'm all out of bubblegum"

[![npm](https://img.shields.io/npm/v/@fortawesome/fontawesome-svg-core.svg?style=flat-square)](https://www.npmjs.com/package/@fortawesome/fontawesome-svg-core)

## Installation

```
$ npm i --save @fortawesome/fontawesome-svg-core
```

Or

```
$ yarn add @fortawesome/fontawesome-svg-core
```

## Documentation

Get started [here](https://fontawesome.com/how-to-use/on-the-web/setup/getting-started). Continue your journey [here](https://fontawesome.com/how-to-use/on-the-web/advanced).

Or go straight to the [API documentation](https://fontawesome.com/how-to-use/with-the-api).

## Issues and support

Start with [GitHub issues](https://github.com/FortAwesome/Font-Awesome/issues) and ping us on [Twitter](https://twitter.com/fontawesome) if you need to.
