# @fortawesome/free-regular-svg-icons - SVG with JavaScript version

> "I came here to chew bubblegum and install Font Awesome 5 - and I'm all out of bubblegum"

[![npm](https://img.shields.io/npm/v/@fortawesome/free-regular-svg-icons.svg?style=flat-square)](https://www.npmjs.com/package/@fortawesome/free-regular-svg-icons)

## Installation

```
$ npm i --save @fortawesome/free-regular-svg-icons
```

Or

```
$ yarn add @fortawesome/free-regular-svg-icons
```

## Documentation

Get started [here](https://fontawesome.com/how-to-use/on-the-web/setup/getting-started). Continue your journey [here](https://fontawesome.com/how-to-use/on-the-web/advanced).

Or go straight to the [API documentation](https://fontawesome.com/how-to-use/with-the-api).

## Issues and support

Start with [GitHub issues](https://github.com/FortAwesome/Font-Awesome/issues) and ping us on [Twitter](https://twitter.com/fontawesome) if you need to.
