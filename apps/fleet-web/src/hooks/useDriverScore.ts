import { useCallback, useMemo } from 'react'
import { useTheme } from '@karoo-ui/core'
import { DateTime } from 'luxon'
import { useHistory } from 'react-router'
import { match } from 'ts-pattern'

import type { DriverId } from 'api/types'
import { getSingleDriverPagePath } from 'src/modules/scorecards/DriverScores/utils'
import {
  useScorecardConfigurationWeightageQuery,
  useScorecardDefaultConfigurationWeightageQuery,
} from 'src/modules/scorecards/Settings/api/queries'
import { getColorForScore, getScoreRanges } from 'src/modules/scorecards/utils'

export default function useDriverScore() {
  const theme = useTheme()
  const history = useHistory()

  // get the score cards configuration setting for driver score
  const configQuery = useScorecardConfigurationWeightageQuery()
  const defaultConfigQuery = useScorecardDefaultConfigurationWeightageQuery()

  // get the score weightage range values
  const weightageRange = useMemo(
    () =>
      configQuery.data?.configurations?.weightageCustomize.on
        ? configQuery.data?.configurations?.weightageCustomize.range
        : (defaultConfigQuery.data?.configurationRules.weightageCustomize.range ?? []),
    [
      configQuery.data?.configurations?.weightageCustomize.on,
      configQuery.data?.configurations?.weightageCustomize.range,
      defaultConfigQuery.data?.configurationRules.weightageCustomize.range,
    ],
  )

  const getDriverScoreColor = useCallback(
    (score: number) => {
      const driverScore = getColorForScore(score, theme, weightageRange)
      return {
        textColor: driverScore.textColor,
        backgroundColor: driverScore.light,
      }
    },
    [theme, weightageRange],
  )

  const scoreRange = getScoreRanges({ theme, weightageRange })
  const driverScoreColorArr = useMemo(
    () =>
      [
        scoreRange[0].tooltipColor,
        scoreRange[1].tooltipColor,
        scoreRange[2].tooltipColor,
      ] satisfies [string, string, string],
    [scoreRange],
  )

  const driverScorePeriod = configQuery.data?.configurations?.scorePeriod

  const onDriverScoreClick = useCallback(
    (driverId: DriverId) => {
      const now = DateTime.now()
      history.push(
        getSingleDriverPagePath(history.location, {
          type: 'driver',
          id: driverId,
          start: now
            .minus(
              match(driverScorePeriod as string)
                .with('1', () => ({ day: 1 }))
                .with('2', () => ({ week: 1 }))
                .with('3', () => ({ month: 1 }))
                .otherwise(() => ({ month: 1 })),
            )
            .startOf('day')
            .toISO(),
          end: now.endOf('day').toISO(),
        }),
      )
    },
    [driverScorePeriod, history],
  )

  return useMemo(
    () => ({
      onDriverScoreClick,
      driverScoreColorArr,
      getDriverScoreColor,
    }),
    [driverScoreColorArr, getDriverScoreColor, onDriverScoreClick],
  )
}
