import { use<PERSON><PERSON><PERSON>, useContext, useMemo, useState } from 'react'
import {
  Button,
  Chip,
  CircularProgressDelayedCentered,
  DataGridAsTabItem,
  GRID_CHECKBOX_SELECTION_COL_DEF as GRID_CHECKBOX_SELECTION_COL_DEF_UNSAFE,
  GridActionsCellItem,
  LinearProgress,
  Tooltip,
  useCallbackBranded,
  useDataGridColumnHelper,
  type ChipProps,
  type GridColDef,
  type GridRowModel,
  type GridRowParams,
} from '@karoo-ui/core'
import TaskAlt from '@mui/icons-material/TaskAlt'
import type {
  QueryObserverPlaceholderResult,
  QueryObserverSuccessResult,
} from '@tanstack/react-query'
import { match } from 'ts-pattern'

import { getUser } from 'duxs/user-sensitive-selectors'
import { useUsersQuery, type UseUsersQueryData } from 'src/modules/api/useUsersQuery'
import useIntl from 'src/modules/components/connected/useIntl'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { createApplyQuickFilterFn } from 'src/shared/data-grid/utils'
import { ctIntl } from 'src/util-components/ctIntl'
import { useMapFormatMeasuredDistance } from 'src/util-components/map/shared/useMapFormatMeasuredDistance'

import useRemindersListQuery, {
  type Ct_fleet_get_fleet_reminders_events,
} from '../../api/useRemindersListQuery'
import { useUpdateReminderEventMutation } from '../../api/useUpdateRemindersCellMutation'
import type { ReminderInfo } from '../../components/CompleteReminderWizard/types'
import { ReminderCustomColumnMenu } from '../../components/List/ReminderCustomColumnMenu'
import { ReminderTablePaginationActions } from '../../components/List/ReminderTablePaginationActions'
import type { AdminReminderTypes } from '../../types'
import {
  formatCategoryNameStringMetaObject,
  remindersListFormatCellValue,
} from '../../utils'
import { RemindersContext } from '../RemindersContext'

function RemindersListOverview() {
  const remindersListQuery = useRemindersListQuery()
  const usersQuery = useUsersQuery()

  if (!usersQuery.data) {
    return <CircularProgressDelayedCentered />
  }

  return match(remindersListQuery)
    .with({ status: 'pending' }, () => <CircularProgressDelayedCentered />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, (successQuery) => (
      <Content
        remindersListSuccessQuery={successQuery}
        usersQueryData={usersQuery.data}
      />
    ))
    .exhaustive()
}

type DataGridRow = Ct_fleet_get_fleet_reminders_events.Return['list'][number]

type DataGridRowId = DataGridRow['reminderCriteriaId']

const GRID_CHECKBOX_SELECTION_COL_DEF =
  GRID_CHECKBOX_SELECTION_COL_DEF_UNSAFE as GridColDef<DataGridRow>

function Content({
  remindersListSuccessQuery,
  usersQueryData,
}: {
  remindersListSuccessQuery:
    | QueryObserverSuccessResult<Ct_fleet_get_fleet_reminders_events.Return>
    | QueryObserverPlaceholderResult<Ct_fleet_get_fleet_reminders_events.Return, Error>
  usersQueryData: UseUsersQueryData
}) {
  const [selectedRemindersCriteriaIds, setSelectedRemindersCriteriaIds] = useState<
    ReadonlyArray<DataGridRowId>
  >([])
  const { modalState } = useContext(RemindersContext)

  const isLoading = remindersListSuccessQuery.fetchStatus === 'fetching'

  const { formatMapDistance } = useMapFormatMeasuredDistance()
  const { formatNumber } = useIntl()
  const updateReminderEventMutation = useUpdateReminderEventMutation()

  const getCellValue = useCallback(
    ({
      row,
      intervalTypeId,
      dateFormat,
      value,
    }: {
      row: DataGridRow
      value: string | number | null
      intervalTypeId?:
        | AdminReminderTypes.IntervalType['reminder_interval_type_id']
        | null
      dateFormat: 'unit' | 'short'
    }) =>
      remindersListFormatCellValue({
        value,
        criteriaId: row.criteriaTypeId,
        intervalTypeId: intervalTypeId ?? null,
        formatMapDistance,
        formatNumber,
        dateFormat,
      }),
    [formatMapDistance, formatNumber],
  )

  const loggedInUser = useTypedSelector(getUser)

  const columnHelper = useDataGridColumnHelper<DataGridRow>({
    filterMode: 'client',
  })

  const getRowDisabledMeta = useCallback(
    (
      row: DataGridRow,
    ):
      | {
          disabled: true
          reason: 'subUser_reminder_can_not_be_edited'
          defaultTooltipText: string
        }
      | {
          disabled: true
          reason: 'reminder_is_completed_already'
        }
      | {
          disabled: false
        } => {
      if (row.clientUserId) {
        return {
          disabled: true,
          reason: 'subUser_reminder_can_not_be_edited',
          defaultTooltipText: ctIntl.formatMessage({
            id: 'admin.reminder.disabledActionTooltipText.youCanNotEditSubUsersReminders',
          }),
        }
      }

      return row.status === 'COMPLETE'
        ? {
            disabled: true,
            reason: 'reminder_is_completed_already',
          }
        : { disabled: false }
    },
    [],
  )

  const columns = useMemo((): Array<GridColDef<DataGridRow>> => {
    const cols: Array<GridColDef<DataGridRow> | false> = [
      {
        ...GRID_CHECKBOX_SELECTION_COL_DEF,
        renderCell: (params) => {
          const disabledMeta = getRowDisabledMeta(params.row)
          return match(disabledMeta)
            .with(
              { disabled: true, reason: 'subUser_reminder_can_not_be_edited' },
              ({ defaultTooltipText }) => (
                <Tooltip
                  title={defaultTooltipText}
                  disableInteractive
                >
                  <span>{GRID_CHECKBOX_SELECTION_COL_DEF.renderCell?.(params)}</span>
                </Tooltip>
              ),
            )
            .with(
              { disabled: false },
              { disabled: true, reason: 'reminder_is_completed_already' },
              () => GRID_CHECKBOX_SELECTION_COL_DEF.renderCell?.(params),
            )
            .exhaustive()
        },
      },
      {
        field: 'reminder',
        headerName: ctIntl.formatMessage({ id: 'Reminder' }),
        valueGetter: (_, row) => formatCategoryNameStringMetaObject(row.categoryName),
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      {
        field: 'entity',
        headerName: ctIntl.formatMessage({ id: 'Vehicle / Driver' }),
        valueGetter: (_, row) =>
          row.entity === 'VEHICLE' ? row.vehicle.name : row.driver.name,
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      loggedInUser?.cuid
        ? false // do not show owner column if logged in user is sub user
        : columnHelper.string(
            (_, row) =>
              row.clientUserId
                ? (usersQueryData.usersById.get(row.clientUserId)?.username ?? '')
                : (loggedInUser?.username ?? ''),
            {
              minWidth: 100,
              field: 'owner',
              headerName: ctIntl.formatMessage({ id: 'Owner' }),
              flex: 1,
            },
          ),
      {
        field: 'type',
        headerName: ctIntl.formatMessage({ id: 'Type' }),
        valueGetter: (_, row) => ctIntl.formatMessage({ id: row.criteriaTypeName }),
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
        type: 'singleSelect',
        valueOptions: [
          {
            label: ctIntl.formatMessage({ id: 'backEnd.admin.reminder.type.distance' }),
            value: ctIntl.formatMessage({ id: 'backEnd.admin.reminder.type.distance' }),
          },
          {
            label: ctIntl.formatMessage({
              id: 'backEnd.admin.reminder.type.hours_of_operation',
            }),
            value: ctIntl.formatMessage({
              id: 'backEnd.admin.reminder.type.hours_of_operation',
            }),
          },
          {
            label: ctIntl.formatMessage({ id: 'backEnd.admin.reminder.type.date' }),
            value: ctIntl.formatMessage({ id: 'backEnd.admin.reminder.type.date' }),
          },
          {
            label: ctIntl.formatMessage({ id: 'backEnd.admin.reminder.type.pto' }),
            value: ctIntl.formatMessage({ id: 'backEnd.admin.reminder.type.pto' }),
          },
        ],
      },
      {
        field: 'settings',
        headerName: ctIntl.formatMessage({ id: 'admin.reminder.input.repeatEvery' }),
        valueGetter: (_, row) =>
          getCellValue({
            row,
            value: row.repeatInterval,
            intervalTypeId: row.repeatIntervalTypeId,
            dateFormat: 'unit',
          }),
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      {
        field: 'reminderTime',
        headerName: ctIntl.formatMessage({ id: 'admin.reminder.input.alertBefore' }),
        valueGetter: (_, row) =>
          getCellValue({
            row,
            value: row.advanceNotifyInterval,
            intervalTypeId: row.advanceNotifyIntervalTypeId,
            dateFormat: 'unit',
          }),
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      {
        field: 'currentValue',
        headerName: ctIntl.formatMessage({ id: 'Current Value' }),
        valueGetter: (_, row) =>
          getCellValue({
            row,
            value: row.currentValue,
            dateFormat: 'short',
          }),
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      {
        field: 'nextReminderValue',
        headerName: ctIntl.formatMessage({ id: 'global.reminder.nextAt' }),
        valueGetter: (_, row) =>
          getCellValue({
            row,
            value: row.nextReminderValue,
            dateFormat: 'short',
          }),
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      {
        field: 'validUntil',
        headerName: ctIntl.formatMessage({ id: 'Valid Until' }),
        valueGetter: (_, row) =>
          getCellValue({
            row,
            value: row.validUntil,
            intervalTypeId: row.advanceNotifyIntervalTypeId,
            dateFormat: 'short',
          }),
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      {
        field: 'lastCompleted',
        headerName: ctIntl.formatMessage({ id: 'Last Completed' }),
        valueGetter: (_, row) =>
          getCellValue({
            row,
            value: row.lastCompletion,
            dateFormat: 'short',
          }),

        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      {
        field: 'completionValue',
        headerName: ctIntl.formatMessage({ id: 'Completion' }),
        valueGetter: (_, row) =>
          getCellValue({
            row,
            value: row.completionValue,
            dateFormat: 'short',
          }),
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      columnHelper.singleSelect((_, row) => row.status, {
        field: 'status',
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        renderCell: ({ row }) => <ReminderStatus status={row.status} />,
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
        valueOptions: [
          { label: ctIntl.formatMessage({ id: 'Valid' }), value: 'VALID' },
          { label: ctIntl.formatMessage({ id: 'Complete' }), value: 'COMPLETE' },
          { label: ctIntl.formatMessage({ id: 'Expiring' }), value: 'EXPIRING' },
          { label: ctIntl.formatMessage({ id: 'Expired' }), value: 'EXPIRED' },
        ],
      }),
      {
        field: 'comment',
        headerName: ctIntl.formatMessage({ id: 'Comment' }),
        valueGetter: (_, row) => row.comment,
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      {
        field: 'completionComment',
        headerName: ctIntl.formatMessage({ id: 'Completion Comment' }),
        valueGetter: (_, row) =>
          row.completionComment ===
          'backEnd.admin.reminder.complete.reason.categoryDeleted'
            ? ctIntl.formatMessage({
                id: 'backEnd.admin.reminder.complete.reason.categoryDeleted',
              })
            : row.completionComment,
        flex: 1,
        ...createApplyQuickFilterFn((params) =>
          params.formattedValue
            ? String(params.formattedValue).replaceAll(/\s+/g, '')
            : params.formattedValue,
        ),
      },
      {
        field: 'actions',
        type: 'actions',
        align: 'center',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => {
          const disabledMeta = match(getRowDisabledMeta(row))
            .with(
              { disabled: true, reason: 'subUser_reminder_can_not_be_edited' },
              () => ({
                disabled: true,
                tooltipText: ctIntl.formatMessage({
                  id: 'admin.reminder.disabledActionTooltipText.youCanNotEditSubUsersReminders',
                }),
              }),
            )
            .with(
              { disabled: true, reason: 'reminder_is_completed_already' },
              { disabled: false },
              ({ disabled }) => ({
                disabled,
                tooltipText: ctIntl.formatMessage({ id: 'Mark as Complete' }),
              }),
            )
            .exhaustive()

          return [
            <Tooltip
              title={disabledMeta.tooltipText}
              key="edit"
            >
              <span>
                <GridActionsCellItem
                  key="markAsComplete"
                  icon={<TaskAlt />}
                  disabled={disabledMeta.disabled}
                  label={ctIntl.formatMessage({ id: 'Mark as Complete' })}
                  onClick={(event) => {
                    event.stopPropagation()
                    modalState?.setModalParams({
                      type: 'markAsCompleteWizard',
                      wizardStep: 'MARK_AS_COMPLETE',
                      remindersInfo: [
                        {
                          reminderId: row.reminderId,
                          reminderEventId: row.reminderEventId,
                          reminderCriteriaId: row.reminderCriteriaId,
                          currentValue: row.currentValue,
                          criteriaTypeId: row.criteriaTypeId,
                          category: {
                            id: row.categoryId,
                            name: row.categoryName,
                            isCustom: row.categoryUserId !== null,
                          },
                          criteriaTypeName: {
                            type: 'translationId',
                            translationId: row.criteriaTypeName,
                          },
                          firstReminder: row.firstReminder,
                          repeatInterval: row.repeatInterval,
                          intervalTypeId: row.repeatIntervalTypeId,
                          entityData:
                            row.entity === 'DRIVER'
                              ? {
                                  type: 'DRIVER',
                                  driverId: row.driver.id,
                                  name: row.driver.name,
                                }
                              : {
                                  type: 'VEHICLE',
                                  vehicleId: row.vehicle.id,
                                  name: row.vehicle.name,
                                },
                        },
                      ],
                      onMarkSuccess: () => setSelectedRemindersCriteriaIds([]),
                    })
                  }}
                />
              </span>
            </Tooltip>,
          ]
        },
      },
    ]

    return cols.filter((col) => !!col)
  }, [
    getCellValue,
    modalState,
    usersQueryData,
    columnHelper,
    loggedInUser,
    getRowDisabledMeta,
  ])

  const updateReminderEventMutationMutate = updateReminderEventMutation.mutate
  const processRowUpdate = useCallback(
    (
      newRow: GridRowModel<Ct_fleet_get_fleet_reminders_events.Return['list'][number]>,
    ) => {
      updateReminderEventMutationMutate([
        {
          completion_value: newRow.completionValue,
          completion_comment: newRow.completionComment ?? '',
          reminder_id: newRow.reminderId,
          reminder_criteria_id: newRow.reminderCriteriaId,
          reminder_event_id: newRow.reminderEventId,
        },
      ])
      return newRow
    },
    [updateReminderEventMutationMutate],
  )

  const rows: Array<DataGridRow> = remindersListSuccessQuery.data.list

  return (
    <UserDataGridWithSavedSettingsOnIDB<DataGridRow>
      Component={DataGridAsTabItem}
      dataGridId="remindersListOverview_v1"
      disableRowSelectionOnClick
      autoPageSize
      pagination
      getRowId={useCallbackBranded(
        (row: DataGridRow): DataGridRowId => row.reminderCriteriaId,
        [],
      )}
      rows={rows}
      columns={columns}
      processRowUpdate={processRowUpdate}
      checkboxSelection
      loading={isLoading}
      rowSelectionModel={selectedRemindersCriteriaIds}
      onRowSelectionModelChange={(rowSelectionModel) => {
        setSelectedRemindersCriteriaIds(
          rowSelectionModel as ReadonlyArray<DataGridRowId>,
        )
      }}
      isRowSelectable={useCallback(
        ({ row }: GridRowParams<DataGridRow>) => !getRowDisabledMeta(row).disabled,
        [getRowDisabledMeta],
      )}
      initialState={{
        pinnedColumns: {
          right: ['actions'],
        },
        pagination: {
          paginationModel: {
            pageSize: 25,
            page: 0,
          },
        },
      }}
      pageSizeOptions={[5, 10, 25]}
      slots={{
        toolbar: KarooToolbar,
        loadingOverlay: LinearProgress,
        columnMenu: ReminderCustomColumnMenu,
      }}
      slotProps={{
        toolbar: KarooToolbar.createProps({
          slots: {
            settingsButton: { show: true },
            filterButton: { show: true },
            searchFilter: { show: true },
          },
          extraContent: {
            right: (
              <Tooltip
                title={ctIntl.formatMessage({
                  id: 'Mark the selected reminders as complete',
                })}
              >
                <span>
                  <Button
                    variant="outlined"
                    size="small"
                    color="secondary"
                    disabled={selectedRemindersCriteriaIds.length === 0}
                    onClick={() => {
                      modalState?.setModalParams({
                        type: 'markAsCompleteWizard',
                        wizardStep: 'MARK_AS_COMPLETE',
                        remindersInfo: selectedRemindersCriteriaIds.map(
                          (reminderCriteriaId): ReminderInfo => {
                            const selectedReminder =
                              remindersListSuccessQuery.data.byId[
                                reminderCriteriaId as Ct_fleet_get_fleet_reminders_events.ReminderReturn['reminderCriteriaId']
                              ]

                            return {
                              reminderId: selectedReminder.reminderId,
                              reminderEventId: selectedReminder.reminderEventId,
                              reminderCriteriaId: selectedReminder.reminderCriteriaId,
                              currentValue: selectedReminder.currentValue,
                              criteriaTypeId: selectedReminder.criteriaTypeId,
                              criteriaTypeName: {
                                type: 'translationId',
                                translationId: selectedReminder.criteriaTypeName,
                              },
                              firstReminder: selectedReminder.firstReminder,
                              repeatInterval: selectedReminder.repeatInterval,
                              intervalTypeId: selectedReminder.repeatIntervalTypeId,
                              category: {
                                id: selectedReminder.categoryId,
                                name: selectedReminder.categoryName,
                                isCustom: selectedReminder.categoryUserId !== null,
                              },
                              entityData:
                                selectedReminder.entity === 'DRIVER'
                                  ? {
                                      type: 'DRIVER',
                                      driverId: selectedReminder.driver.id,
                                      name: selectedReminder.driver.name,
                                    }
                                  : {
                                      type: 'VEHICLE',
                                      vehicleId: selectedReminder.vehicle.id,
                                      name: selectedReminder.vehicle.name,
                                    },
                            }
                          },
                        ),
                        onMarkSuccess: () => setSelectedRemindersCriteriaIds([]),
                      })
                    }}
                    startIcon={<TaskAlt />}
                  >
                    {ctIntl.formatMessage({ id: 'Mark as Complete' })}
                  </Button>
                </span>
              </Tooltip>
            ),
          },
        }),
        pagination: {
          ActionsComponent: ReminderTablePaginationActions,
        },
      }}
      getRowHeight={useCallbackBranded(() => 'auto', [])}
      getEstimatedRowHeight={useCallback(() => 30, [])}
      sx={{
        '&.MuiDataGrid-root--densityCompact .MuiDataGrid-cell': {
          py: 0,
        },
        '&.MuiDataGrid-root--densityStandard .MuiDataGrid-cell': {
          py: 1,
        },
        '&.MuiDataGrid-root--densityComfortable .MuiDataGrid-cell': {
          py: 2,
        },
      }}
    />
  )
}

export default RemindersListOverview

const ReminderStatus = ({
  status,
}: {
  status: Ct_fleet_get_fleet_reminders_events.Return['list'][number]['status']
}) => {
  const chipProps: Record<
    typeof status,
    {
      color: ChipProps['color']
      label: string
    }
  > = {
    VALID: {
      color: 'success',
      label: ctIntl.formatMessage({ id: 'Valid' }),
    },
    EXPIRED: {
      color: 'error',
      label: ctIntl.formatMessage({ id: 'Expired' }),
    },
    EXPIRING: {
      color: 'warning',
      label: ctIntl.formatMessage({ id: 'Expiring' }),
    },
    COMPLETE: {
      color: 'info',
      label: ctIntl.formatMessage({ id: 'Complete' }),
    },
  }

  return (
    <Chip
      size="small"
      variant="filled"
      {...chipProps[status]}
    ></Chip>
  )
}
