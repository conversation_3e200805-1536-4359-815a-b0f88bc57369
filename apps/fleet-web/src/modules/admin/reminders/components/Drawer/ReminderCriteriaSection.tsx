import {
  Box,
  Button,
  DatePicker,
  FormHelperText,
  Grid,
  IconButton,
  MenuItem,
  Paper,
  Stack,
  TextField,
  Tooltip,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteIcon from '@mui/icons-material/Delete'
import InfoIcon from '@mui/icons-material/Info'
import { DateTime } from 'luxon'
import {
  Controller,
  type Control,
  type FieldArrayPath,
  type FieldError,
  type FieldPathValue,
  type FieldValues,
  type Path,
  type UseFieldArrayReturn,
  type UseFormClearErrors,
} from 'react-hook-form'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import { getIsUserDistanceInMiles } from 'duxs/user'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'

import type { FetchAdminRemindersPrerequisites } from '../../api/useAdminRemindersPrerequisitesQuery'
import { reminderDefaultCriteriaField } from '../../Drawer'
import type { MultipleReminderSchema, ReminderSchemaPossibleValues } from '../../types'
import { createSubNameControllerPropsGetter, SubNameController } from './utils'

const PlaceholderInputs = () => (
  <Grid
    container
    rowSpacing={2}
    columnSpacing={2}
  >
    <Grid size={6}>
      <TextField
        label={ctIntl.formatMessage({
          id: 'admin.reminder.input.firstReminder',
        })}
        disabled
        fullWidth
      />
    </Grid>

    <Grid size={6}>
      <TextField
        label={ctIntl.formatMessage({
          id: 'admin.reminder.input.alertBefore',
        })}
        disabled
        fullWidth
      />
    </Grid>
    <Grid size={6}>
      <TextField
        label={ctIntl.formatMessage({
          id: 'admin.reminder.input.repeatEvery',
        })}
        disabled
        fullWidth
      />
    </Grid>
    <Grid size={6}>
      <TextField
        label={ctIntl.formatMessage({
          id: 'admin.reminder.input.stopRepeatingAt',
        })}
        disabled
        fullWidth
      />
    </Grid>
  </Grid>
)

type KilometerInputFormPossibleValue = CriteriaSectionFormValue
function KilometersInputs<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
>({
  control,
  name,
  index,
}: {
  control: Control<TFieldValues>
  name: FieldPathValue<TFieldValues, TName> extends KilometerInputFormPossibleValue
    ? TName
    : never
  index: number
}) {
  const shouldUseMiles = useTypedSelector(getIsUserDistanceInMiles)

  const { getSubNameControllerProps } =
    createSubNameControllerPropsGetter<KilometerInputFormPossibleValue>({
      name,
      control,
    })
  return (
    <Grid
      container
      rowSpacing={2}
      columnSpacing={2}
    >
      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.first`)}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={`${ctIntl.formatMessage({
                id: 'admin.reminder.input.firstReminder',
              })} ${shouldUseMiles ? '(mi)' : '(km)'}`}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? 0 : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
            />
          )}
        />
      </Grid>

      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.advance`)}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={`${ctIntl.formatMessage({
                id: 'admin.reminder.input.alertBefore',
              })} ${shouldUseMiles ? '(mi)' : '(km)'}`}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? null : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
            />
          )}
        />
      </Grid>

      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.repeat`)}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={`${ctIntl.formatMessage({
                id: 'admin.reminder.input.repeatEvery',
              })} ${shouldUseMiles ? '(mi)' : '(km)'}`}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? null : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
            />
          )}
        />
      </Grid>
      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.stop`)}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={`${ctIntl.formatMessage({
                id: 'admin.reminder.input.stopRepeatingAt',
              })} ${shouldUseMiles ? '(mi)' : '(km)'}`}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? null : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
            />
          )}
        />
      </Grid>
    </Grid>
  )
}

type HoursInputsPossibleFormValue = CriteriaSectionFormValue
function HoursInputs<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
>({
  control,
  name,
  index,
}: {
  control: Control<TFieldValues>
  name: FieldPathValue<TFieldValues, TName> extends HoursInputsPossibleFormValue
    ? TName
    : never
  index: number
}) {
  const { getSubNameControllerProps } =
    createSubNameControllerPropsGetter<HoursInputsPossibleFormValue>({
      name,
      control,
    })

  return (
    <Grid
      container
      rowSpacing={2}
      columnSpacing={2}
    >
      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.first`, {
            rules: {
              deps: [`${index}.repeat`],
            },
          })}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={`${ctIntl.formatMessage({
                id: 'admin.reminder.input.firstReminder',
              })} (${ctIntl.formatMessage({
                id: 'hours',
              })})`}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? 0 : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
            />
          )}
        />
      </Grid>

      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.advance`)}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={`${ctIntl.formatMessage({
                id: 'admin.reminder.input.alertBefore',
              })} (${ctIntl.formatMessage({
                id: 'hours',
              })})`}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? null : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
            />
          )}
        />
      </Grid>

      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.repeat`)}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={`${ctIntl.formatMessage({
                id: 'admin.reminder.input.repeatEvery',
              })} (${ctIntl.formatMessage({
                id: 'hours',
              })})`}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? null : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
            />
          )}
        />
      </Grid>
      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.stop`)}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={`${ctIntl.formatMessage({
                id: 'admin.reminder.input.stopRepeatingAt',
              })} (${ctIntl.formatMessage({
                id: 'hours',
              })})`}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? null : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
            />
          )}
        />
      </Grid>
    </Grid>
  )
}

type DateInputsPossibleFormValue = CriteriaSectionFormValue
function DateInputs<
  TFieldValues extends {
    [key: string]: Array<{
      first: string
    }>
  },
  TName extends Path<TFieldValues>,
>({
  control,
  name,
  index,
  criteriaIntervalType,
}: {
  control: Control<TFieldValues>
  name: FieldPathValue<TFieldValues, TName> extends DateInputsPossibleFormValue
    ? TName
    : never
  index: number
  criteriaIntervalType: FetchAdminRemindersPrerequisites.Return['criteriaIntervalType']
}) {
  const defaultDateValue = DateTime.local().plus({ days: 1 })

  const { getSubNameControllerProps } =
    createSubNameControllerPropsGetter<DateInputsPossibleFormValue>({
      name,
      control,
    })

  return (
    <Grid
      container
      rowSpacing={2}
      columnSpacing={2}
    >
      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.first`)}
          render={({ field, fieldState }) => (
            <DatePicker
              label={ctIntl.formatMessage({
                id: 'admin.reminder.input.firstReminder',
              })}
              defaultValue={defaultDateValue}
              value={(() =>
                // eslint-disable-next-line no-nested-ternary
                typeof field.value === 'number'
                  ? defaultDateValue
                  : field.value
                    ? DateTime.fromJSDate(field.value as Date)
                    : defaultDateValue)()}
              onChange={(newValue, context) => {
                field.onChange(
                  newValue === null || context.validationError === 'invalidDate'
                    ? 0
                    : newValue.toJSDate(),
                )
              }}
              disablePast
              shouldDisableDate={(day) =>
                //disable day if equal to today
                day.valueOf() === DateTime.local().startOf('day').valueOf()
              }
              slotProps={{
                textField: {
                  onKeyDown: (e) => {
                    e.preventDefault()
                  },
                  onBlur: field.onBlur,
                  error: fieldState.invalid,
                  helperText: ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  }),
                  fullWidth: true,
                },
              }}
            />
          )}
        />
      </Grid>

      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.advance`)}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={ctIntl.formatMessage({
                id: 'admin.reminder.input.alertBefore',
              })}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? null : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
              sx={{ width: '50%' }}
            />
          )}
        />
        <SubNameController
          {...getSubNameControllerProps(`${index}.advanceIntervalId`)}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={(e) => {
                field.onChange(e.target.value as typeof field.value)
              }}
              sx={{ width: '50%' }}
              select
            >
              {criteriaIntervalType.map((option) => (
                <MenuItem
                  key={option.id}
                  value={option.value}
                >
                  {ctIntl.formatMessage({ id: option.name })}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </Grid>
      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.repeat`)}
          render={({ field, fieldState }) => (
            <TextField
              fullWidth
              // https://mui.com/material-ui/react-text-field/#type-quot-number-quot
              value={typeof field.value === 'number' ? field.value : ''}
              label={ctIntl.formatMessage({
                id: 'admin.reminder.input.repeatEvery',
              })}
              inputProps={{ inputMode: 'numeric' }}
              onChange={(e) => {
                const parsedValue = Number.parseInt(e.target.value, 10)
                field.onChange(Number.isNaN(parsedValue) ? null : parsedValue)
              }}
              error={fieldState.invalid}
              helperText={ctIntl.formatMessage({
                id: fieldState.error?.message ?? '',
              })}
              sx={{ width: '50%' }}
            />
          )}
        />

        <SubNameController
          {...getSubNameControllerProps(`${index}.repeatIntervalId`)}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={(e) => {
                field.onChange(e.target.value as typeof field.value)
              }}
              sx={{ width: '50%' }}
              select
            >
              {criteriaIntervalType.map((option) => (
                <MenuItem
                  key={option.id}
                  value={option.value}
                >
                  {ctIntl.formatMessage({ id: option.name })}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </Grid>
      <Grid size={6}>
        <SubNameController
          {...getSubNameControllerProps(`${index}.stop`)}
          render={({ field, fieldState }) => (
            <DatePicker
              label={ctIntl.formatMessage({
                id: 'admin.reminder.input.stopRepeatingAt',
              })}
              value={
                typeof field.value === 'number' || field.value === null
                  ? null
                  : DateTime.fromJSDate(new Date(field.value))
              }
              onChange={(newValue, context) => {
                field.onChange(
                  newValue === null || context.validationError === 'invalidDate'
                    ? 0
                    : newValue.toJSDate(),
                )
              }}
              // minDate={DateTime.fromJSDate(new Date(watch_stop))}
              disablePast
              slotProps={{
                textField: {
                  onBlur: field.onBlur,
                  error: fieldState.invalid,
                  helperText: ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  }),
                  fullWidth: true,
                },
              }}
            />
          )}
        />
      </Grid>
    </Grid>
  )
}

type CriteriaSectionFormValue = MultipleReminderSchema['categories'][number]['criteria']
function ReminderCriteriaSection<
  TFieldValues extends FieldValues,
  TName extends Path<TFieldValues>,
>({
  control,
  name,
  criteriaFields,
  criteriaSelectOptions,
  criteriaIntervalType,
  clearErrors,
  canBeEmpty,
}: {
  control: Control<TFieldValues>
  name: FieldPathValue<TFieldValues, TName> extends CriteriaSectionFormValue
    ? TName
    : never
  criteriaFields:
    | UseFieldArrayReturn<
        ReminderSchemaPossibleValues,
        FieldArrayPath<ReminderSchemaPossibleValues>
      >
    | UseFieldArrayReturn<MultipleReminderSchema, `categories.${number}.criteria`>
  clearErrors: UseFormClearErrors<TFieldValues>
  criteriaSelectOptions: FetchAdminRemindersPrerequisites.Return['criteriaSelectOptions']
  criteriaIntervalType: FetchAdminRemindersPrerequisites.Return['criteriaIntervalType']
  canBeEmpty: boolean
}) {
  return (
    <Stack
      gap={2}
      data-testid="RemindersDrawer-CriteriaSection"
    >
      <IntlTypography
        variant="overline"
        msgProps={{
          id: 'Schedule',
        }}
      />
      <Controller
        control={control}
        name={name}
        render={({ fieldState }) => {
          const criteriaIdsAlreadySelected = criteriaFields.fields.reduce(
            (acc, curr) => {
              if (curr.criteriaTypeId) {
                acc.push(curr.criteriaTypeId)
              }
              return acc
            },
            [] as Array<string>,
          )

          return (
            <>
              {criteriaFields.fields.map((criteriaFieldOption, index) => (
                <Box
                  // eslint-disable-next-line sonarjs/no-array-index-key
                  key={`reminder_criteria_${index}`}
                  data-testid={`RemindersDrawer-CriteriaSection-Criteria_${index}`}
                >
                  <Paper
                    variant="outlined"
                    sx={(theme) => ({
                      p: 2,
                      borderColor:
                        fieldState.error &&
                        isArrayFieldStateError(fieldState.error) &&
                        fieldState.error[index] &&
                        fieldState.error[index].message
                          ? theme.palette.error.light
                          : theme.palette.divider,
                    })}
                  >
                    <Stack gap={2}>
                      <Stack
                        direction="row"
                        alignItems="center"
                      >
                        <TextField
                          data-testid={`RemindersDrawer-CriteriaSection-Criteria_${index}-CriteriaInput`}
                          fullWidth
                          select
                          label={ctIntl.formatMessage({
                            id: 'admin.reminder.drawer.criteria',
                          })}
                          SelectProps={{
                            MenuProps: {
                              MenuListProps: {
                                sx: { p: 0 },
                                id: `RemindersDrawer-CriteriaSection-CriteriaInput-Options_${index}`,
                              },
                              PaperProps: { sx: { minHeight: 0 } },
                            },
                          }}
                          onChange={({ target: { value } }) => {
                            clearErrors(name)

                            criteriaFields.update(index, {
                              ...reminderDefaultCriteriaField,
                              first:
                                value === '3'
                                  ? DateTime.local().plus({ days: 1 }).toJSDate()
                                  : criteriaFields.fields[index].first,
                              criteriaTypeId:
                                value as typeof criteriaFieldOption.criteriaTypeId,
                            })
                          }}
                          value={criteriaFieldOption.criteriaTypeId ?? ''}
                        >
                          {criteriaSelectOptions.map((option) => (
                            <MenuItem
                              key={option.id}
                              value={option.value}
                              style={{
                                display: criteriaIdsAlreadySelected.includes(option.id)
                                  ? 'none'
                                  : 'block',
                              }}
                            >
                              {ctIntl.formatMessage({
                                id: option.label,
                              })}
                            </MenuItem>
                          ))}
                        </TextField>
                        {(canBeEmpty || criteriaFields.fields.length > 1) && (
                          <IconButton onClick={() => criteriaFields.remove(index)}>
                            <DeleteIcon />
                          </IconButton>
                        )}
                      </Stack>
                      {match(criteriaFieldOption)
                        .with({ criteriaTypeId: null }, () => <PlaceholderInputs />)
                        .with({ criteriaTypeId: '1' }, () => (
                          <KilometersInputs
                            name={name}
                            control={control}
                            index={index}
                          />
                        ))
                        .with({ criteriaTypeId: '2' }, () => (
                          <HoursInputs
                            name={name}
                            control={control}
                            index={index}
                          />
                        ))
                        .with({ criteriaTypeId: '3' }, () => (
                          <DateInputs
                            name={name}
                            control={control}
                            index={index}
                            criteriaIntervalType={criteriaIntervalType}
                          />
                        ))
                        .with({ criteriaTypeId: '4' }, () => (
                          <HoursInputs
                            name={name}
                            control={control}
                            index={index}
                          />
                        ))
                        .exhaustive()}
                    </Stack>
                  </Paper>
                  {fieldState.error &&
                    isArrayFieldStateError(fieldState.error) &&
                    fieldState.error[index] &&
                    fieldState.error[index].message && (
                      <FormHelperText error={!!fieldState.error}>
                        {ctIntl.formatMessage({
                          id:
                            fieldState.error &&
                            isArrayFieldStateError(fieldState.error) &&
                            fieldState.error[index] &&
                            fieldState.error[index].message
                              ? (fieldState.error[index].message ?? '')
                              : '',
                        })}
                      </FormHelperText>
                    )}
                </Box>
              ))}
              <Stack
                direction="row"
                alignItems="center"
              >
                <Button
                  data-testid="RemindersDrawer-CriteriaSection-AddCriteria"
                  startIcon={<AddIcon />}
                  onClick={() => criteriaFields.append(reminderDefaultCriteriaField)}
                  disabled={
                    criteriaFields.fields.length === criteriaSelectOptions.length
                  }
                >
                  {ctIntl.formatMessage({
                    id: 'admin.reminder.drawer.addAnotherCriteria',
                  })}
                </Button>
                <Tooltip
                  placement="right"
                  title={ctIntl.formatMessage({
                    id: 'admin.reminder.drawer.addAnotherCriteria.tooltip',
                  })}
                  PopperProps={{ sx: { width: 152 } }}
                >
                  <InfoIcon
                    fontSize="small"
                    color="disabled"
                  />
                </Tooltip>
              </Stack>
            </>
          )
        }}
      />
    </Stack>
  )
}

export default ReminderCriteriaSection

const isArrayFieldStateError = (
  value: Array<FieldError> | FieldError | undefined,
): value is Array<FieldError> => R.isArray(value)
