import { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  CircularProgressDelayedAbsolute,
  ClickAwayListener,
  DateRangePicker,
  Paper,
  SingleInputDateRangeField,
  Stack,
  styled,
  TextField,
  type DateRange,
  type PaperProps,
  type PickersShortcutsItem,
} from '@karoo-ui/core'
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined'
import type {
  QueryObserverPlaceholderResult,
  QueryObserverSuccessResult,
} from '@tanstack/react-query'
import { DateTime } from 'luxon'
import { Controller, useForm, type ControllerRenderProps } from 'react-hook-form'
import { useDispatch } from 'react-redux'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { Except, Writable } from 'type-fest'
import { z, ZodIssueCode } from 'zod/v4'

import { vehicleGroupIdSchema, vehicleIdSchema } from 'api/types'
import {
  fetchVehicleList,
  getVehicleGroups,
  getVehicleGroupsById,
  getVehicles,
  getVehiclesById,
} from 'duxs/vehicles'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { terminalEventTypeIdSchema } from 'src/api/types'
import { reGetAutocompleteVirtualizedProps } from 'src/components/_dropdowns/ReAutocompleteVirtualized'
import OverflowableTextTooltip from 'src/components/_popups/Tooltip/OverflowableText'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { useTypedSelector } from 'src/redux-hooks'
import { messages } from 'src/shared/forms/messages'
import type { ExcludeStrict } from 'src/types/utils'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import { toMutable } from 'src/util-functions/functional-utils'
import {
  Array_difference,
  Array_filter,
  Array_filterMap,
  Array_forEach,
  Array_uniqBy,
} from 'src/util-functions/performance-critical-utils'
import {
  createZodDateTimeRangeSchema_edgesRequired,
  createZodObjPathGetter,
} from 'src/util-functions/zod-utils'

import {
  useHeatmapsVehicleEventTypes,
  type UseVehicleEventTypesReturnedData,
} from './api'

const vehicleOrGroupSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('vehicle'),
    vehicleId: vehicleIdSchema,
    label: z.string(),
  }),
  z.object({
    type: z.literal('vehicleGroup'),
    groupId: vehicleGroupIdSchema,
    label: z.string(),
  }),
  z.object({
    type: z.literal('all'),
    label: z.string(),
  }),
])

type VehicleOrGroupOption = z.infer<typeof vehicleOrGroupSchema>

const getValidSchema = ({
  rangeMaxSizeInDays,
  outerLimits,
}: {
  rangeMaxSizeInDays: number
  outerLimits?: { lower?: DateTime | undefined; upper?: DateTime | undefined }
}) =>
  z
    .object({
      dateRange: createZodDateTimeRangeSchema_edgesRequired({
        rangeMaxSizeInDays,
        outerLimits,
      }),
      vehiclesAndGroups: z.array(vehicleOrGroupSchema),
      /* We keep allVehicles on a separate property so that the user does not loose vehicles and groups selections when toggling allVehicles */
      allVehicles: z.boolean(),
      eventTypes: z
        .array(z.object({ id: terminalEventTypeIdSchema, label: z.string() }))
        .min(1, ctIntl.formatMessage({ id: messages.required })),
    })
    .superRefine((obj, ctx) => {
      const { createPath } = createZodObjPathGetter(obj)
      if (obj.allVehicles === false && obj.vehiclesAndGroups.length === 0) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: ctIntl.formatMessage({ id: messages.required }),
          path: createPath(['vehiclesAndGroups']),
        })
      }
    })

export type ValidSchema = z.infer<ReturnType<typeof getValidSchema>>

type SchemaPossibleValues = Except<ValidSchema, 'dateRange'> & {
  dateRange: [ValidSchema['dateRange'][0] | null, ValidSchema['dateRange'][1] | null]
}

type Props = {
  onValidSubmit: (validValue: ValidSchema) => void
  onClearFilters: () => void
  isFetchingHeatmapsVehicleEvents: boolean
}

type ContextActions = {
  handleReset: () => void
}

export function ReRightPanel({
  onValidSubmit,
  onClearFilters,
  isFetchingHeatmapsVehicleEvents,
}: Props) {
  const vehicleEventTypesQuery = useHeatmapsVehicleEventTypes()

  return (
    <Paper
      sx={{
        width: '700px',
        position: 'relative',
      }}
    >
      {match(vehicleEventTypesQuery)
        .with({ status: 'error' }, () => null)
        .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
        .with({ status: 'success' }, (successQuery) => (
          <Content
            vehicleEventTypesSuccessQuery={successQuery}
            onValidSubmit={onValidSubmit}
            onClearFilters={onClearFilters}
            isFetchingHeatmapsVehicleEvents={isFetchingHeatmapsVehicleEvents}
          />
        ))
        .exhaustive()}
    </Paper>
  )
}

const ResetButtonContext = createContext<ContextActions | undefined>(undefined)

function CustomPaper(props: PaperProps) {
  const context = useContext(ResetButtonContext)
  return (
    <Paper {...props}>
      {props.children}
      <Button
        variant="text"
        onClick={() => context && context.handleReset()}
      >
        {ctIntl.formatMessage({ id: 'Reset' })}
      </Button>
    </Paper>
  )
}

function Content({
  vehicleEventTypesSuccessQuery,
  onValidSubmit,
  isFetchingHeatmapsVehicleEvents,
  onClearFilters,
}: {
  vehicleEventTypesSuccessQuery:
    | QueryObserverSuccessResult<UseVehicleEventTypesReturnedData>
    | QueryObserverPlaceholderResult<UseVehicleEventTypesReturnedData, unknown>
  onValidSubmit: Props['onValidSubmit']
  isFetchingHeatmapsVehicleEvents: Props['isFetchingHeatmapsVehicleEvents']
  onClearFilters: Props['onClearFilters']
}) {
  const dispatch = useDispatch()
  const shortcuts = useDateRangeShortcutItems()
  const dateRangeShortcutsItems = useMemo(
    () =>
      [
        shortcuts.last7Days,
        shortcuts.last15Days,
        shortcuts.thisMonth,
        shortcuts.lastMonth,
        shortcuts.thisYear,
        shortcuts.reset,
      ] as const satisfies ReadonlyArray<PickersShortcutsItem<DateRange<DateTime>>>,
    [shortcuts],
  )

  const [initialValues] = useState<SchemaPossibleValues>(() => ({
    dateRange: dateRangeShortcutsItems[0].getValue(),
    vehiclesAndGroups: [],
    allVehicles: false,
    eventTypes: [],
  }))

  const [dateRangeOuterUpperLimit] = useState(() => DateTime.local().endOf('day'))

  const validSchema = useMemo(
    () =>
      getValidSchema({
        rangeMaxSizeInDays: 366,
        outerLimits: {
          upper: dateRangeOuterUpperLimit,
        },
      }),
    [dateRangeOuterUpperLimit],
  )

  const {
    control,
    handleSubmit,
    setValue: setFormValue,
    reset: resetForm,
    getValues,
    formState,
  } = useForm<SchemaPossibleValues>({
    resolver: zodResolverV4(validSchema),
    mode: 'all',
    defaultValues: initialValues,
  })

  const [open, setOpen] = useState(false)
  const autocompleteRef = useRef<HTMLDivElement>(null)
  const handleClickAway = (event: MouseEvent | TouchEvent) => {
    if (
      autocompleteRef.current &&
      !autocompleteRef.current.contains(event.target as Node)
    ) {
      setOpen(false)
    }
  }

  const vehicles = useTypedSelector(getVehicles)
  const vehiclesById = useTypedSelector(getVehiclesById)
  const vehicleGroups = useTypedSelector(getVehicleGroups)
  const vehicleGroupsById = useTypedSelector(getVehicleGroupsById)

  const vehiclesAndGroupsOptions = useMemo((): ValidSchema['vehiclesAndGroups'] => {
    const array: Array<VehicleOrGroupOption> = [
      {
        type: 'all',
        label: ctIntl.formatMessage({ id: 'All vehicles' }),
      },
    ]

    Array_forEach(vehicles, (vehicle) => {
      array.push({
        type: 'vehicle',
        vehicleId: vehicle.id,
        label: vehicle.registration,
      })
    })

    Array_forEach(vehicleGroups, (vehicleGroup) => {
      const group = vehicleGroupsById.get(vehicleGroup.id)

      array.push({
        type: 'vehicleGroup',
        groupId: vehicleGroup.id,
        label: `${vehicleGroup.name} (${group ? group.itemIds.length : 0})`,
      })
    })

    return R.sortBy(
      array,
      (item) =>
        match(item)
          .with({ type: 'all' }, () => 0) // Show all first
          .with({ type: 'vehicleGroup' }, () => 1)
          .with({ type: 'vehicle' }, () => 2)
          .exhaustive(),
      (item) => item.label,
    )
  }, [vehicles, vehicleGroups, vehicleGroupsById])

  useEffect(() => {
    if (vehicles.length === 0) {
      dispatch(fetchVehicleList())
    }
  }, [dispatch, vehicles.length])

  const onSubmit = handleSubmit(
    (validValues_) => {
      const validValues = validValues_ as ValidSchema
      onValidSubmit(validValues)
    },
    (_errors) => {},
  )

  function onVehiclesAndGroupsAutocompleteChange({
    newValueFromChangeEvent,
    field,
  }: {
    newValueFromChangeEvent: ValidSchema['vehiclesAndGroups']
    field: ControllerRenderProps<SchemaPossibleValues, 'vehiclesAndGroups'>
  }) {
    if (newValueFromChangeEvent.length === 0) {
      setFormValue('allVehicles', false, { shouldValidate: false })
      field.onChange([])
      return
    }

    const unselectedElements = Array_difference(field.value, newValueFromChangeEvent)

    const newSelectedElements = Array_difference(newValueFromChangeEvent, field.value)

    if (newSelectedElements.length > 1 || unselectedElements.length > 1) {
      throw new Error(
        'The onchange event should only remove/add one element at a time. If it does not, we need to refactor the algorithm below in a different way.',
      )
    }

    if (newSelectedElements[0]?.type === 'all') {
      setFormValue('allVehicles', true, { shouldValidate: false })
      field.onChange([newSelectedElements[0]])
      return
    }

    if (unselectedElements[0]?.type === 'all') {
      setFormValue('allVehicles', false, { shouldValidate: false })
      field.onChange([])
      return
    }

    function generateNewValue(): Readonly<ValidSchema['vehiclesAndGroups']> {
      // Is Removal operation?
      if (unselectedElements.length === 1) {
        const unselectedElement = unselectedElements[0]

        switch (unselectedElement.type) {
          case 'vehicle': {
            const unselectedVehicle = unselectedElement
            return Array_filter(newValueFromChangeEvent, (el) =>
              match(el)
                .with({ type: 'vehicleGroup' }, (groupEl) => {
                  const group = vehicleGroupsById.get(groupEl.groupId)
                  if (!group) {
                    return false // should not happen
                  }

                  return group.itemIdsSet.has(unselectedVehicle.vehicleId)
                    ? false // remove
                    : true // keep
                })
                .with({ type: 'vehicle' }, () => true)
                .with({ type: 'all' }, () => false) //should not happen, but we remove it anyway
                .exhaustive(),
            )
          }
          case 'vehicleGroup': {
            const unselectedGroup = unselectedElement
            const removedGroup = vehicleGroupsById.get(unselectedGroup.groupId)
            if (!removedGroup) {
              return newValueFromChangeEvent
            }

            const selectedVehicleGroups = Array_filterMap(
              newValueFromChangeEvent,
              (el, { RemoveSymbol }) =>
                el.type === 'vehicleGroup'
                  ? (vehicleGroupsById.get(el.groupId) ?? RemoveSymbol)
                  : RemoveSymbol,
            )

            return Array_filter(newValueFromChangeEvent, (el) => {
              if (el.type === 'vehicleGroup') {
                return true
              }

              if (el.type === 'all') {
                return false //should not happen, but we remove it anyway
              }

              const vehicle = vehiclesById.get(el.vehicleId)
              if (!vehicle) {
                return false // should not happen
              }

              if (removedGroup.itemIdsSet.has(vehicle.id)) {
                const atLeastOneOtherSelectedGroupContainsVehicle =
                  selectedVehicleGroups.some((group) =>
                    group.itemIdsSet.has(vehicle.id),
                  )

                return atLeastOneOtherSelectedGroupContainsVehicle
              }

              return true
            })
          }
          case 'all': {
            return []
          }
        }
      } else {
        const { allVehicles } = getValues()
        if (allVehicles) {
          setFormValue('allVehicles', false, { shouldValidate: false })
        }

        if (newSelectedElements.length === 0) {
          return newValueFromChangeEvent
        }

        const newSelectedElement = newSelectedElements[0]
        switch (newSelectedElement.type) {
          case 'vehicle': {
            // We could select all groups that contain have all their vehicles selected but it's overcomplicated without much benefit
            // So we just select the vehicle, and remove the all if present
            return Array_filter(newValueFromChangeEvent, (el) => el.type !== 'all')
          }
          case 'vehicleGroup': {
            const newSelectedGroup = newSelectedElement
            const group = vehicleGroupsById.get(newSelectedGroup.groupId)

            const newValue = newValueFromChangeEvent.filter(
              (el) => el.type !== 'all',
            ) as Array<
              ExcludeStrict<VehicleOrGroupOption, { type: 'all'; label: string }>
            >

            Array_forEach(group?.itemIds ?? [], (vehicleId) => {
              const vehicle = vehiclesById.get(vehicleId)
              if (!vehicle) {
                return
              }

              newValue.push({
                type: 'vehicle',
                vehicleId: vehicle.id,
                label: vehicle.registration,
              })
            })

            return Array_uniqBy(newValue, (el) => {
              if (el.type === 'vehicle') {
                return el.vehicleId
              }

              return el.groupId
            })
          }
          case 'all': {
            return []
          }
        }
      }
    }

    field.onChange(toMutable(generateNewValue()))
  }

  function onClearFiltersClick() {
    resetForm({
      ...initialValues,
      dateRange: [null, null],
    })
    onClearFilters()
  }

  const contextActionValues: ContextActions = useMemo(() => {
    const handleReset = () => {
      setFormValue('vehiclesAndGroups', [], { shouldValidate: true })
    }
    return { handleReset }
  }, [setFormValue])

  return (
    <ResetButtonContext.Provider value={contextActionValues}>
      <Box
        component="form"
        onSubmit={onSubmit}
        sx={{
          height: '100%',
          /* Important for the position sticky actions */
          overflow: 'auto',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            height: '100%',
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <Stack sx={{ p: 2, gap: 2 }}>
            <Stack gap={0.5}>
              <IntlTypography
                variant="h5"
                msgProps={{
                  id: 'map.tab.heatmaps.title',
                }}
              />

              <IntlTypography
                msgProps={{
                  id: 'map.tab.heatmaps.drawer.title',
                }}
              />
            </Stack>
            <Stack>
              <Controller
                control={control}
                name="vehiclesAndGroups"
                render={({ field, fieldState }) => (
                  <ClickAwayListener onClickAway={handleClickAway}>
                    <div ref={autocompleteRef}>
                      <Autocomplete
                        open={open}
                        onOpen={() => setOpen(true)}
                        onClose={(_, reason) => {
                          if (reason === 'toggleInput') setOpen(false)
                        }}
                        data-testid="vehicles-and-groups-autocomplete"
                        {...reGetAutocompleteVirtualizedProps({
                          options: vehiclesAndGroupsOptions,
                          renderRowGroupContent: ({ group }) =>
                            group === 'all'
                              ? null
                              : ctIntl.formatMessage({
                                  id: group === 'vehicle' ? 'Vehicles' : 'Groups',
                                }),
                          renderRowSingleItemContent: ({ state, label }) => (
                            <>
                              <StyledCheckbox
                                checked={state.selected}
                                color="secondary"
                              />
                              <OverflowableTextTooltip>{label}</OverflowableTextTooltip>
                            </>
                          ),
                        })}
                        groupBy={(option) => option.type}
                        PaperComponent={CustomPaper}
                        disableCloseOnSelect
                        isOptionEqualToValue={(option, value) =>
                          match([option, value])
                            .with(
                              [{ type: 'vehicle' }, { type: 'vehicle' }],
                              ([option, value]) => value.vehicleId === option.vehicleId,
                            )
                            .with(
                              [{ type: 'vehicleGroup' }, { type: 'vehicleGroup' }],
                              ([option, value]) => value.groupId === option.groupId,
                            )
                            .with(
                              [{ type: 'all' }, { type: 'all' }],
                              ([option, value]) => value.type === option.type,
                            )
                            .otherwise(() => false)
                        }
                        multiple
                        value={field.value}
                        onChange={(_event, newValueFromChangeEvent) => {
                          onVehiclesAndGroupsAutocompleteChange({
                            newValueFromChangeEvent,
                            field,
                          })
                        }}
                        renderTags={(value, getTagProps) => {
                          // Based on https://github.com/mui/material-ui/issues/19137#issuecomment-979871073
                          // `limitTags` prop is not enough because it does not limit tags when autocomplete is focused.
                          // We need to limit tags shown (__always__) or else, if we select a group with hundreds or thousands of vehicles, the browser can freeze

                          const numTags = value.length
                          const limitTags = 10

                          return (
                            <>
                              {value.slice(0, limitTags).map((option, index) => {
                                const tagProps = getTagProps({ index })
                                return (
                                  <Chip
                                    {...tagProps}
                                    key={tagProps.key}
                                    label={option.label}
                                  />
                                )
                              })}

                              {numTags > limitTags && ` +${numTags - limitTags}`}
                            </>
                          )
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            error={!!fieldState.error}
                            helperText={fieldState.error?.message}
                            label={ctIntl.formatMessage({ id: 'Registration' })}
                          />
                        )}
                      />
                    </div>
                  </ClickAwayListener>
                )}
              />
            </Stack>
            <Stack>
              <Controller
                control={control}
                name="dateRange"
                render={({ field, fieldState }) => (
                  <DateRangePicker
                    label={ctIntl.formatMessage({ id: 'Start - End date' })}
                    value={field.value}
                    onChange={(value) => {
                      setFormValue('dateRange', value, { shouldValidate: true })
                    }}
                    maxDate={dateRangeOuterUpperLimit}
                    slots={{ field: SingleInputDateRangeField }}
                    slotProps={{
                      shortcuts: {
                        items: dateRangeShortcutsItems as Writable<
                          typeof dateRangeShortcutsItems
                        >,
                      },
                      textField: {
                        error: !!fieldState.error,
                        helperText: fieldState.error?.message,
                        InputProps: {
                          endAdornment: <CalendarTodayOutlinedIcon />,
                        },
                      },
                      actionBar: { actions: [] },
                    }}
                  />
                )}
              />
            </Stack>
            <Stack>
              <Controller
                control={control}
                name="eventTypes"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    data-testid="event-types-autocomplete"
                    {...reGetAutocompleteVirtualizedProps({
                      options: vehicleEventTypesSuccessQuery.data,
                      renderRowSingleItemContent: ({ label, state }) => (
                        <>
                          <StyledCheckbox
                            checked={state.selected}
                            color="secondary"
                          />
                          {label}
                        </>
                      ),
                    })}
                    disableCloseOnSelect
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    multiple
                    value={field.value}
                    onChange={(_event, value) => {
                      setFormValue(field.name, value, { shouldValidate: true })
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        label={ctIntl.formatMessage({ id: 'Event type' })}
                      />
                    )}
                  />
                )}
              />
            </Stack>
          </Stack>
          <Box
            sx={(theme) => ({
              position: 'sticky',
              bottom: 0,
              display: 'flex',
              justifyContent: 'space-between',
              borderTop: '1px solid rgba(0, 0, 0, 0.12)',
              px: 3,
              pb: 3,
              pt: 2,
              background: theme.palette.background.paper,
            })}
          >
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => {
                onClearFiltersClick()
              }}
            >
              {ctIntl.formatMessage({ id: 'Clear filters' })}
            </Button>

            <Button
              variant="contained"
              data-testid="apply-button"
              color="primary"
              type="submit"
              disabled={isFetchingHeatmapsVehicleEvents || !formState.isValid}
              startIcon={
                isFetchingHeatmapsVehicleEvents ? (
                  <CircularProgress
                    color="inherit"
                    size={16}
                  />
                ) : null
              }
            >
              {ctIntl.formatMessage({ id: 'Apply' })}
            </Button>
          </Box>
        </Box>
      </Box>
    </ResetButtonContext.Provider>
  )
}

const StyledCheckbox = styled(Checkbox)(({ theme }) =>
  theme.unstable_sx({
    '&.MuiCheckbox-root': {
      padding: 0,
      paddingRight: 1,
    },
  }),
)
