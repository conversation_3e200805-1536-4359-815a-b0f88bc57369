import { useMemo } from 'react'
import { isEmpty, isNil, reduce } from 'lodash'
import { useQuery } from '@tanstack/react-query'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import apiCaller from 'api/api-caller'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import type { VehicleId } from 'api/types'
import { getHoveredItem, getItems } from 'duxs/map'
import {
  getClustersEnabled,
  getListGeofencesSetting,
  getSettings_UNSAFE,
} from 'duxs/user'
import {
  getSensorPermission,
  getShowVehicleOverspeedThreshold,
} from 'duxs/user-sensitive-selectors'
import { TACHOGRAPH } from 'src/modules/app/components/routes/tachograph'
import { useTypedSelector } from 'src/redux-hooks'
import type { PromiseResolvedType } from 'src/types'
import type { ExcludeStrict, ExtractStrict } from 'src/types/utils'
import { minutesToMs } from 'src/util-functions/functional-utils'

import type {
  GetVehiclePopup,
  GetVehiclePopupParts,
  VehiclePopupPart,
} from './queries-types'
import {
  getPrivateVehiclePopoverSections,
  type PublicVehiclePopoverSections,
  type VehiclePopoverSections,
} from './slice'

async function getVehiclePopupAccessibleParts() {
  const response: GetVehiclePopupParts.ApiOutput = await apiCaller(
    'ct_fleet_get_popover_parts',
    undefined,
    { noX: true },
  )

  return new Set(response)
}

export const createVehiclePopoverAccessiblePartsQueryKey = () =>
  ['vehiclePopover/getParts'] as const

export function useVehiclePopoverAccessibleParts() {
  return useQuery({
    queryKey: createVehiclePopoverAccessiblePartsQueryKey(),
    queryFn: getVehiclePopupAccessibleParts,
    staleTime: minutesToMs(20),
    gcTime: minutesToMs(10),
    ...makeQueryErrorHandlerWithToast(),
  })
}

/** Get sections __available__ for the user */
export function usePublicVehiclePopoverSections() {
  const popoverPartsQuery = useVehiclePopoverAccessibleParts()
  const tachographPermission = useTypedSelector(TACHOGRAPH.tab.selector)
  const sections = useTypedSelector(getPrivateVehiclePopoverSections)
  const items_ = useTypedSelector(getItems)
  const hoveredItemId = useTypedSelector(getHoveredItem)
  const settings = useTypedSelector(getSettings_UNSAFE)
  const listGeofencesPermissionSetting = useTypedSelector(getListGeofencesSetting)
  const clustersEnabled = useTypedSelector(getClustersEnabled)
  const sensorPermission = useTypedSelector(getSensorPermission)

  return useMemo((): PublicVehiclePopoverSections => {
    if (sections === null || popoverPartsQuery.data === undefined) {
      return null
    }

    const accessibleParts = popoverPartsQuery.data

    const { carpool } = settings

    const byId: Partial<VehiclePopoverSections['byId']> = {}
    const allIds: VehiclePopoverSections['allIds'] = []

    for (const id of sections.allIds) {
      const section = sections.byId[id]

      const isSectionAllowed: boolean = match(section)
        .with({ id: 'CURRENT_HOURS_SERVICE' }, () => tachographPermission)
        .with({ id: 'TACHOGRAPH' }, () => tachographPermission)
        .with({ id: 'TAXI' }, () => {
          const vehicles = items_ as Array<
            ExtractStrict<(typeof items_)[number], { id: VehicleId }>
          >
          const currentHoveredVehicle = vehicles.find(
            (vehicle) => vehicle.id === hoveredItemId,
          )
          return currentHoveredVehicle?.taxiSensorNo !== null
        })
        .with({ id: 'CURRENT_GEOFENCES' }, () => listGeofencesPermissionSetting)
        .with({ id: 'REFRIGERATOR' }, () => sensorPermission.fridge)
        .with({ id: 'UNIT_CLOCK' }, () => true)
        .with({ id: 'ALERTS_ACTIONS' }, () => true)
        .with({ id: 'TCU_BATTERY' }, () => accessibleParts.has('tcu_battery'))
        .with({ id: 'DRIVER_ID' }, () => true)
        .with({ id: 'ALERTS_ACTIONS' }, () => true)
        .with({ id: 'ENGINE_FAULTS' }, () => false)
        .with({ id: 'CUSTOM_FIELDS' }, () =>
          accessibleParts.has('vehicle_custom_fields'),
        )
        .with({ id: 'CARPOOL' }, () => carpool)
        .with({ id: 'CLUSTERS' }, () => clustersEnabled)
        .with({ id: 'IS_WIFI' }, () => {
          const vehicles = items_ as Array<
            ExtractStrict<(typeof items_)[number], { id: VehicleId }>
          >
          const currentHoveredVehicle = vehicles.find(
            (vehicle) => vehicle.id === hoveredItemId,
          )
          return R.isNonNullish(currentHoveredVehicle) && currentHoveredVehicle.isWifi
        })
        .exhaustive()

      if (isSectionAllowed) {
        byId[id] = section
        allIds.push(id)
      }
    }

    return { byId, allIds } as const
  }, [
    clustersEnabled,
    hoveredItemId,
    items_,
    listGeofencesPermissionSetting,
    popoverPartsQuery.data,
    sections,
    settings,
    tachographPermission,
    sensorPermission,
  ])
}

export function usePublicVehiclePopoverList() {
  const sections = usePublicVehiclePopoverSections()

  return useMemo(
    () =>
      sections === null
        ? []
        : sections.allIds.map((id) => {
            const section = sections.byId[id]
            return section as ExcludeStrict<typeof section, undefined>
          }),
    [sections],
  )
}

function usePublicCheckedVehiclePopoverSet() {
  const publicVehiclePopoverList = usePublicVehiclePopoverList()
  const showVehicleOverspeedThreshold = useTypedSelector(
    getShowVehicleOverspeedThreshold,
  )
  const set = useMemo(
    () =>
      new Set([
        ...publicVehiclePopoverList
          .filter((section) => section.checked)
          .map((section) => section.id),
        'VEHICLE_DEF',
        'VEHICLE_PROPELLANT',
        'VEHICLE_BATTERY',
        ...(showVehicleOverspeedThreshold ? ['VEHICLE_OVERSPEED_THRESHOLD'] : []),
      ] as const),
    [publicVehiclePopoverList, showVehicleOverspeedThreshold],
  )

  return set
}

const createGetVehiclePopupDataKey = (params: {
  vehicleId: string | number
  parts?: Partial<Record<VehiclePopupPart, boolean | undefined>>
}) =>
  [
    'vehiclePopover/getData',
    // Some endpoints return vehicleId as number and others as string.
    // We want to consider them the same to prevent over fetching, e.g - when invalidating this query
    R.set(params, 'vehicleId', params.vehicleId.toString()),
  ] as const

type GetVehiclePopupKeyParams = Parameters<typeof createGetVehiclePopupDataKey>[0]

async function getVehiclePopupData(params: GetVehiclePopupKeyParams) {
  const { vehicleId, parts } = params
  const partsSelectedArray = reduce(
    parts,
    (acc, value, untypedKey) => {
      const key = untypedKey as keyof typeof parts
      if (value) {
        acc.push(key)
      }
      return acc
    },
    [] as GetVehiclePopup.Params['parts'],
  )

  const apiParams: GetVehiclePopup.Params = {
    vehicleId,
    parts: partsSelectedArray,
  }
  const response: GetVehiclePopup.ApiOutput = await apiCaller(
    'ct_fleet_get_vehicle_popover',
    apiParams,
    { noX: true },
  )

  return parseVehiclePopupData(response)
}

export type VehiclePopupData = PromiseResolvedType<typeof getVehiclePopupData>

export function parseVehiclePopupData(data: GetVehiclePopup.ApiOutput) {
  const {
    current_geofences,
    vehicle_custom_fields,
    vehicle_propellant,
    vehicle_def,
    vehicle_battery,
    carpool,
    vehicle_overspeed_threshold,
    is_wifi,
    tachograph,
  } = data

  const result = {
    vehicleCustomFields: vehicle_custom_fields,
    vehiclePropellant: vehicle_propellant,
    vehicleDef: vehicle_def,
    vehicleBattery: vehicle_battery,
    currentGeofences:
      current_geofences?.length === 1
        ? (current_geofences[0].geofences?.map((geo) => ({
            name: geo.geofence_name,
            id: geo.geofence_id,
            color: geo.colour,
          })) ?? null)
        : null,
    carpool:
      isNil(carpool) || isEmpty(carpool)
        ? null
        : {
            carpoolId: carpool.carpool_id,
            vehicleType: carpool.vehicle_type,
            bookingStatus: carpool.booking_status,
            bookingPurpose: carpool.booking_purpose,
          },
    vehicleOverspeedThreshold: vehicle_overspeed_threshold
      ? vehicle_overspeed_threshold.map((t) => ({
          terminalSerial: t.terminal_serial,
          overspeedBuzzer: t.overspeed_buzzer,
          threshold1: t.threshold_1,
          threshold2: t.threshold_2,
        }))
      : null,
    vehicleIsWifi: isNil(is_wifi) ? null : is_wifi.ct_fleet_get_wifi_reports.remaining,
    tachograph: isNil(tachograph) ? null : tachograph,
  }

  return result
}

const useVehiclePopupDataSharedConfig = () => {
  const publicCheckedVehiclePopoverSet = usePublicCheckedVehiclePopoverSet()

  const parts: GetVehiclePopupKeyParams['parts'] = {
    vehicle_custom_fields: publicCheckedVehiclePopoverSet.has('CUSTOM_FIELDS'),
    tcu_battery: publicCheckedVehiclePopoverSet.has('TCU_BATTERY'),
    vehicle_def: publicCheckedVehiclePopoverSet.has('VEHICLE_DEF'),
    vehicle_propellant: publicCheckedVehiclePopoverSet.has('VEHICLE_PROPELLANT'),
    vehicle_battery: publicCheckedVehiclePopoverSet.has('VEHICLE_BATTERY'),
    current_geofences: publicCheckedVehiclePopoverSet.has('CURRENT_GEOFENCES'),
    carpool: publicCheckedVehiclePopoverSet.has('CARPOOL'),
    vehicle_overspeed_threshold: publicCheckedVehiclePopoverSet.has(
      'VEHICLE_OVERSPEED_THRESHOLD',
    ),
    is_wifi: publicCheckedVehiclePopoverSet.has('IS_WIFI'),
    tachograph: publicCheckedVehiclePopoverSet.has('TACHOGRAPH'),
  }

  return { parts, queryEnabled: publicCheckedVehiclePopoverSet.size > 0 }
}

export function useVehiclePopupDataQuery({
  vehicleId,
}: Pick<GetVehiclePopup.Params, 'vehicleId'>) {
  const { parts, queryEnabled } = useVehiclePopupDataSharedConfig()
  return useQuery({
    queryKey: createGetVehiclePopupDataKey({ vehicleId, parts }),
    queryFn: ({ queryKey }) => getVehiclePopupData(queryKey[1]),
    enabled: queryEnabled,
    staleTime: minutesToMs(2),
  })
}

function useVehiclePopupCustomFieldsFnQuery({
  vehicleId,
}: Pick<GetVehiclePopup.Params, 'vehicleId'>) {
  const { parts, queryEnabled } = useVehiclePopupDataSharedConfig()

  return useQuery({
    queryKey: createGetVehiclePopupDataKey({
      vehicleId,
      parts: { ...parts, vehicle_custom_fields: true },
    }),
    queryFn: ({ queryKey }) => getVehiclePopupData(queryKey[1]),
    enabled: queryEnabled,
    select: (data) =>
      data.vehicleCustomFields as ExcludeStrict<
        typeof data.vehicleCustomFields,
        undefined
      >,
    staleTime: minutesToMs(2),
  })
}

export const useVehiclePopupCustomFields = Object.assign(
  useVehiclePopupCustomFieldsFnQuery,
  { createKey: createGetVehiclePopupDataKey },
)
