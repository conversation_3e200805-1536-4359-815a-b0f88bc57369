import {
  Component,
  Fragment,
  useEffect,
  useMemo,
  type ComponentProps,
  type ComponentType,
} from 'react'
import * as React from 'react'
import {
  debounce,
  delay,
  isArray,
  isEmpty,
  isFunction,
  isNil,
  isObject,
  toString,
  uniqBy,
} from 'lodash'
import { styled } from '@karoo-ui/core'
import memoizeOne from 'memoize-one'
import { connect } from 'react-redux'
import type { RouteComponentProps } from 'react-router-dom'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'
import { useEffectReducer, type EffectReducer } from 'use-effect-reducer'

import { hereSearchCoordinate, searchCoordinate } from 'api/coordinate'
import { hereFindPlaces, type HereApiParsedPlace } from 'api/here-places'
import type { FetchTimelineEventsRawResolved } from 'api/timeline'
import type { VehicleId } from 'api/types'
import { getActiveVehicle } from 'duxs/live-vision'
import {
  changeMapCenterZoom,
  changeViewMode,
  getFocusedGroup,
  getFocusedItem,
  getFocusedVehicleId,
  getHoveredVehicleMarkerData,
  getIsStreetViewVisible,
  getItems,
  getMapSize,
  getMapTypeId,
  getMapViewVisibleRightPanel,
  getPrevViewMode,
  getSelectedTripStartDate,
  getSortedLeftPanelExtras,
  getTimelineSliderPlaying,
  getTypeSortMethod,
  getViewMode,
  resetZoomFirstTimeLoading,
  selectTripSummary,
  setFocusedPoint,
  setNonGoogleMapTypeId,
} from 'duxs/map'
import { getIsComparingTrips, getVisibleLeftPanel } from 'duxs/map-timeline'
import {
  getActiveTimelineEventByActivity,
  getTimelineEventsUIDateRange,
} from 'duxs/timeline'
import { getClustersEnabled, savePreferences } from 'duxs/user'
import { getPreferences } from 'duxs/user-sensitive-selectors'
import {
  clearDashboardVehicleList,
  getVehiclesHaveBeenLoadedAtLeastOnce,
  type getActiveVehiclesWithDrivers,
} from 'duxs/vehicles'
import { MapApiProvider } from 'src/api/user/types'
import { MapThemeContextProvider } from 'src/components/context/MapThemeContext'
import Icon from 'src/components/Icon'
import { useUserGoogleAutocompleteSuggestionsAndPlaceFieldsLazy } from 'src/hooks/google-autocomplete-suggestions'
import {
  clickedLeftPanelPlace,
  clickedVehicleMarker,
  focusVehicle,
  jumpToTime,
  onGoogleMapTypeIdChange,
  onMapProviderUIClick,
  onMapTypeFromUrlChange,
  onVehicleMarkerMouseEnter,
  onVehicleMarkerMouseLeave,
} from 'src/modules/map-view/actions'
import { clickedLeftPanelVehicle } from 'src/modules/map-view/actions-circular-deps'
import {
  getActivityActiveTab,
  getActivityDateRange,
} from 'src/modules/map-view/FleetMapView/DetailsPanel/slice'
import NearbyVehiclesLeftPanel from 'src/modules/map-view/FleetMapView/LeftPanel/ReNearbyVehicles'
// import TripCompareLeftPanel from 'src/modules/map-view/FleetMapView/LeftPanel/TripCompare'
import ReFleetDetailsPanel from 'src/modules/map-view/FleetMapView/ReDetailsPanel'
import { vehicleSortMethods } from 'src/process-data/vehicles'
import { useAppDispatch, useTypedSelector } from 'src/redux-hooks'
import type { AppState } from 'src/root-reducer'
import { variables } from 'src/shared/components/styled/global-styles'
import { GA4 } from 'src/shared/google-analytics4'
import {
  createStandardPlaceDetailsFromGooglePlace,
  GOOGLE_PLACE_DETAILS_FIELDS_STANDARD,
} from 'src/shared/google-places'
import type { FixMeAny, ValueOf } from 'src/types'
import type { MapsExtended } from 'src/types/extended/google-maps'
import type { ExcludeStrict, ExtractStrict } from 'src/types/utils'
import type { OnVehicleHoverHandler } from 'src/util-components/map/google/layers/vehicle-components'
import type { OpenLayersMapApiProvider } from 'src/util-components/map/open-layers/base-map-config'
import { MapAreaThemeContextProvider } from 'src/util-components/map/shared/MapAreaThemeContext'
import {
  mapViewContainer,
  type MapViewContainerInjectedProps,
} from 'src/util-components/map/shared/mapViewContainer'
import type {
  MapProviderMetaData,
  UserAvailableMapApiProvider_Leaflet,
} from 'src/util-components/map/shared/types'
import { loadableWithSpinnerAndRetry } from 'src/util-functions/loadable'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'
import {
  generateItemMatchesWithTextAndFilters,
  stringSearch,
  type Filters,
} from 'src/util-functions/search-utils'

import {
  extractAddress,
  extractLatLngFromTextInput,
  getAutoCompleteBiasedPredictionBasedOnCurrentCenter,
  MAP_PLACE_ZOOM,
  VIEW_MODE,
} from 'cartrack-utils'
import { useLocalMapApiProviderWithUserRestrictedSetter } from '../components/connected/useLocalMapApiProviderWithUserRestrictedSetter'
import { LiveVision } from '../live-vision'
import type { FocusedVehicle } from './FleetMapView/DetailsPanel/ui-types'
import ReTripCompare from './FleetMapView/LeftPanel/ReTripCompare'
import TripCompareTimeline from './FleetMapView/LeftPanel/TripCompare/components/trip-compare-timeline'
import type { ListItemProps } from './left-panel/ListItem'
import ReLeftPanel from './left-panel/ReLeftPanel'
import Map, { type MapComponentProps } from './map/map'
import MapEventInfoPanel from './MapEventInfoPanel'
import {
  createMapFleetSearchParams,
  MapFleetContextConsumer,
  useMapFleetContext,
} from './MapFleetProvider'
import MapMultipleDaysEventInfoPanel from './MapMultipleDaysEventInfoPanel'
import { MapViewSearchProvider } from './MapViewSearchProvider'
import ReLandscapeBottomPanel from './ReLandscapeBottomPanel'
import ReTimeline from './timeline/ReTimeline'
import type {
  MapPlaceResult,
  MapSearchResult,
  ResolvedPlaceOrGooglePredictionSearchItem,
  ResolvedPlaceSearchItem,
  ResolvedSearchItems,
} from './types'
import { ClustersChipsGoogle } from './VehicleCluster/ClustersChipsGoogle'
import ReVehicleClusterPanel from './VehicleCluster/ReVehicleClusterPanel'
import VehicleClusterOnBoardingModal from './VehicleCluster/VehicleClusterOnBoardingModal'

const MapOpenLayers = loadableWithSpinnerAndRetry(() => import('./map/map-open-layers'))

const generateKeyFrom = (...args: Array<any>) =>
  isArray(args) ? args.reduce((accKey, arg) => accKey + toString(arg), '') : ''

type ReducerEvent = {
  type: 'vehicleIdSearchParam_or_pathname_or_haveVehiclesLoaded_changed'
  pathname: string
  vehicleIdSearchParam: VehicleId | null
  haveVehiclesLoaded: boolean
}

type ReducerState = null

export const createSideEffectsReducer =
  ({
    items,
    reduxDispatch,
    activityDateRange,
  }: {
    items: PropsClass['items']
    reduxDispatch: ReturnType<typeof useAppDispatch>
    activityDateRange: PropsClass['activityDateRange']
  }): EffectReducer<ReducerState, ReducerEvent> =>
  (state, event, captureEffect) =>
    match([event])
      .with(
        [
          {
            type: 'vehicleIdSearchParam_or_pathname_or_haveVehiclesLoaded_changed',
          },
        ],
        ([{ pathname, vehicleIdSearchParam, haveVehiclesLoaded }]): ReducerState => {
          if (haveVehiclesLoaded && vehicleIdSearchParam && pathname === '/map/fleet') {
            const vehicle = items.find(
              (v): v is ExtractStrict<typeof v, { id: VehicleId }> =>
                v.id === vehicleIdSearchParam.toString(),
            )

            if (!isNil(vehicle)) {
              captureEffect(() => {
                reduxDispatch(
                  focusVehicle({
                    vehicle: vehicle,
                    dateTimeRange: activityDateRange,
                  }),
                )
              })
            }
          }

          return state
        },
      )
      .otherwise(() => state)

// Allows us to do side-effects with hooks while the main component is not converted
function SideEffects({
  location: { pathname },
  items,
  activityDateRange,
  haveVehiclesLoaded,
}: Pick<
  PropsClass,
  'location' | 'items' | 'activityDateRange' | 'haveVehiclesLoaded'
>) {
  const reduxDispatch = useAppDispatch()
  const [, dispatch] = useEffectReducer(
    createSideEffectsReducer({ items, reduxDispatch, activityDateRange }),
    (): ReducerState => null,
  )

  const { selectedVehicleMeta } = useMapFleetContext()
  const vehicleIdSearchParam = selectedVehicleMeta?.initialVehicleId ?? null

  React.useEffect(() => {
    dispatch({
      type: 'vehicleIdSearchParam_or_pathname_or_haveVehiclesLoaded_changed',
      vehicleIdSearchParam,
      pathname,
      haveVehiclesLoaded,
    })
  }, [dispatch, pathname, haveVehiclesLoaded, vehicleIdSearchParam])

  const mapTypeFromUrl = useMemo(() => {
    // path /map/fleet will return fleet
    const parts = pathname.split('/')
    const mapIndex = parts.indexOf('map')
    return mapIndex !== -1 && mapIndex + 1 < parts.length ? parts[mapIndex + 1] : null
  }, [pathname])

  useEffect(() => {
    if (mapTypeFromUrl) {
      reduxDispatch(onMapTypeFromUrlChange({ mapTypeFromUrl }))
    }
  }, [reduxDispatch, mapTypeFromUrl])

  return null
}

type FunctionalProps = {
  // Should not be needed at all in the future
  selectedType: 'vehicle'
} & MapViewContainerInjectedProps &
  RouteComponentProps<FixMeAny, FixMeAny, FixMeAny> &
  ReturnType<typeof mapStateToProps> &
  typeof mapDispatchToProps

export type LeftPanelOpenSidePanel = 'filters' | 'settings' | null

function MapViewFunctional({
  forceMapUpdate = null,
  groupsEnabled = false,
  selectedType = 'vehicle',
  mapApiProviderId: mapContainerMapApiProviderIdToIgnore,
  ...rest
}: FunctionalProps) {
  const currentMapTypeId = useTypedSelector(getMapTypeId)
  const dispatch = useAppDispatch()
  const {
    currentMapProvider,
    setCurrentMapProvider,
    hasCurrentMapProviderChangedAfterInitialization,
  } = useLocalMapApiProviderWithUserRestrictedSetter({
    currentMapTypeId,
    setMapTypeId: (selectedMapTypeId) =>
      dispatch(
        currentMapProvider === MapApiProvider.GOOGLE
          ? onGoogleMapTypeIdChange({ selectedMapTypeId, currentMapTypeId })
          : setNonGoogleMapTypeId(selectedMapTypeId),
      ),
  })
  const { dispatch: mapFleetDispatch } = useMapFleetContext()
  const googleAutocompleteSuggestionsAndPlaceFieldsLazy =
    useUserGoogleAutocompleteSuggestionsAndPlaceFieldsLazy({
      placeFields: GOOGLE_PLACE_DETAILS_FIELDS_STANDARD,
    })

  return (
    <MapFleetContextConsumer>
      {({ selectedVehicleMeta }) => (
        <MapView
          {...rest}
          mapFleetDispatch={mapFleetDispatch}
          forceMapUpdate={forceMapUpdate}
          groupsEnabled={groupsEnabled}
          selectedType={selectedType}
          mapProviderMetaData={{
            currentMapProvider,
            selectionUI: {
              onClick: (provider) => {
                dispatch(
                  onMapProviderUIClick({
                    clickedMapProvider: provider,
                    currentMapProvider,
                    mapTypeId: currentMapTypeId,
                  }),
                )
                setCurrentMapProvider(provider)
              },
            },
          }}
          hasCurrentMapProviderChangedAfterInitialization={
            hasCurrentMapProviderChangedAfterInitialization
          }
          selectedVehicleMeta={selectedVehicleMeta}
          mapTypeId={currentMapTypeId}
          onChangeMapTypeId={(selectedMapTypeId) =>
            dispatch(
              currentMapProvider === MapApiProvider.GOOGLE
                ? onGoogleMapTypeIdChange({ selectedMapTypeId, currentMapTypeId })
                : setNonGoogleMapTypeId(selectedMapTypeId),
            )
          }
          dispatch={dispatch}
          googleAutocompleteSuggestionsAndPlaceFieldsLazy={
            googleAutocompleteSuggestionsAndPlaceFieldsLazy
          }
        />
      )}
    </MapFleetContextConsumer>
  )
}

type State = {
  leftPanelOpenSidePanel: LeftPanelOpenSidePanel
  isSearching: boolean
  searchInput: string
  addressByCoordinate:
    | Array<google.maps.GeocoderResult>
    | Array<HereApiParsedPlace>
    | null
  places: typeof initialPlaces
  mapObject: MapsExtended.MapObject | undefined
}

const initialPlaces: {
  query: string | undefined
  results: Array<MapPlaceResult>
  error: boolean
} = {
  query: '',
  results: [],
  error: false,
}

type PropsClass = Except<
  FunctionalProps,
  'forceMapUpdate' | 'groupsEnabled' | 'selectedType' | 'mapApiProviderId'
> & {
  forceMapUpdate: FunctionalProps['forceMapUpdate'] | null
  groupsEnabled: ExcludeStrict<FunctionalProps['groupsEnabled'], undefined>
  selectedType: FunctionalProps['selectedType']
  mapProviderMetaData: MapProviderMetaData
  hasCurrentMapProviderChangedAfterInitialization: boolean
  mapTypeId: google.maps.MapTypeId
  onChangeMapTypeId: (type: google.maps.MapTypeId) => void
  selectedVehicleMeta: ReturnType<typeof useMapFleetContext>['selectedVehicleMeta']
  mapFleetDispatch: ReturnType<typeof useMapFleetContext>['dispatch']
  dispatch: ReturnType<typeof useAppDispatch>
  googleAutocompleteSuggestionsAndPlaceFieldsLazy: ReturnType<
    typeof useUserGoogleAutocompleteSuggestionsAndPlaceFieldsLazy<
      typeof GOOGLE_PLACE_DETAILS_FIELDS_STANDARD
    >
  >
}

class MapView extends Component<PropsClass, State> {
  state: State = {
    leftPanelOpenSidePanel: null,
    isSearching: false,
    searchInput: '',
    addressByCoordinate: [],
    places: initialPlaces,
    mapObject: undefined,
  }

  componentDidMount() {
    // To cater for the situation where there is a crash in fullscreen view that leaves the redux viewMode value as fullscreen
    if (this.props.viewMode === VIEW_MODE.fullscreen) {
      this.props.changeViewMode(this.props.prevViewMode)
    }
  }

  componentDidUpdate(prevProps: PropsClass) {
    const { resetZoomFirstTimeLoading, focusedVehicleId } = this.props

    if (
      focusedVehicleId !== prevProps.focusedVehicleId &&
      R.isNullish(focusedVehicleId)
    ) {
      this.props.history.push(
        createMapFleetSearchParams({
          locationPrefix: 'same_page',
          history: this.props.history,
          newSearchParams: () => ({
            selectedVehicleMeta: undefined,
          }),
        }),
      )
    }

    // Check if vehicles and map are loaded
    if (!this.hasResetZoom && this.props.haveVehiclesLoaded && this.props.mapState) {
      this.hasResetZoom = true
      resetZoomFirstTimeLoading()
    }

    // Change view mode back to portal when selecting tab that is different from Fleet
    if (
      this.props.mapType !== prevProps.mapType &&
      prevProps.mapType === 'fleet' &&
      this.props.viewMode !== VIEW_MODE.portrait
    ) {
      this.props.changeViewMode(VIEW_MODE.portrait)
      this.props.changeMapHeight('100%')
    }
  }

  componentWillUnmount() {
    this.props.clearDashboardVehicleList()
  }

  hasResetZoom: boolean | undefined = undefined

  handleLeftPanelVehicleClick = ({ vehicleId }: { vehicleId: VehicleId }) => {
    GA4.event({
      category: 'Map',
      action: 'Left Panel - Vehicle Click',
    })

    this.setState(() => ({
      leftPanelOpenSidePanel: null,
    }))

    const { clickedLeftPanelVehicle, focusedItem } = this.props
    const { searchInput } = this.state

    const vehicle = (
      searchInput ? this.resolvedSearchItems() : this.getSortedItems()
    ).find(
      (v): v is ExtractStrict<typeof v, { id: VehicleId }> =>
        'id' in v && vehicleId === v.id,
    )

    if (!vehicle) {
      return
    }

    // Only dispatch actions if the vehicle clicked is different from the one already focused
    if (focusedItem === null || (focusedItem && vehicle.id !== focusedItem.id)) {
      clickedLeftPanelVehicle({
        vehicle,
        history: this.props.history,
      })
    }
  }

  handleFocusVehicleOnMap = (id: string | number) => {
    const vehicle = this.getSortedItems().find((v) => id === v.id)
    if (!isNil(vehicle)) {
      this.props.focusVehicle({
        vehicle: vehicle as FocusedVehicle,
        dateTimeRange: 'default',
      })
    }
  }

  handleVehicleMarkerClick = (vehicleId: VehicleId) =>
    this.props.clickedVehicleMarker({ vehicleId })

  handleToggleEventPanel = (value: FixMeAny) => {
    this.props.savePreferences('showEventInfoPanel', value)
  }

  handleEventMarkerClick = (
    id: string,
    events: FetchTimelineEventsRawResolved['events'],
  ) => {
    // When we enabled the eslint rule below, there was not enough confidence to use trip equals here
    // eslint-disable-next-line eqeqeq
    const nextActiveEventIndex = events.findIndex((event) => event.id == id)
    const nextEvent = events[nextActiveEventIndex]

    if (nextEvent) {
      const timelineTimeSpan = this.props.timelineEndTime - this.props.timelineStartTime

      const nextProgress =
        (nextEvent.time - this.props.timelineStartTime) / timelineTimeSpan
      this.props.jumpToTime({
        nextProgress,
        nextActiveEventIndex,
        nextCoords: {
          lat: nextEvent.lat,
          lng: nextEvent.lng,
        },
      })
    }
  }

  resolveFlatItems = (items: PropsClass['items']) => uniqBy(items, 'id')

  resolvedSearchItems = (): ResolvedSearchItems => {
    const sortedItems = this.getSortedItems()
    const flatItems = [
      ...this.resolveFlatItems(sortedItems),
      ...this.props.sortedExtraSearchItems,
    ]

    if (this.props.searchType === 'fleetSearch') {
      // Don't filter vehicles in vehicle groups
      if (this.props.focusedGroup) {
        return this.props.items
      }

      return getMapSearchResults(
        this.state.searchInput,
        flatItems,
        sortedItems,
        this.props.groupsEnabled,
      )
    }

    if (this.props.searchType === 'placeSearch') {
      return this.state.places.results
    }

    if (this.props.searchType === 'coordinateSearch') {
      const { mapProviderMetaData } = this.props
      const { addressByCoordinate } = this.state

      if (addressByCoordinate === null) {
        return [{ address: null }]
      }

      if (addressByCoordinate.length > 0) {
        const address = extractAddress({
          mapApiProviderId: mapProviderMetaData.currentMapProvider,
          addressArray: addressByCoordinate,
        } as
          | {
              mapApiProviderId: OpenLayersMapApiProvider
              addressArray: Array<HereApiParsedPlace>
            }
          | {
              mapApiProviderId: (typeof MapApiProvider)['GOOGLE']
              addressArray: Array<google.maps.GeocoderResult>
            })
        const { lat, lng } = extractLatLngFromTextInput(this.state.searchInput)

        return [
          {
            ...address,
            lat,
            lng,
          },
        ]
      }

      return []
    }

    return []
  }

  handleMouseEnterVehicle: OnVehicleHoverHandler['onMouseEnter'] = ({ vehicleId }) => {
    this.props.onVehicleMarkerMouseEnter({ vehicleId })
  }

  handleMouseLeaveVehicle: OnVehicleHoverHandler['onMouseLeave'] = ({ vehicleId }) =>
    this.props.onVehicleMarkerMouseLeave({ vehicleId })

  handleChangeViewMode = (newMode: ValueOf<typeof VIEW_MODE>) => {
    let { mapHeight } = this.props
    if (newMode === 0) {
      mapHeight = '100%'
    }

    this.props.changeMapHeight(mapHeight)
    this.props.changeViewMode(newMode)
  }

  handleFullscreenClick = () => {
    GA4.event({
      category: 'Map',
      action: 'Fullscreen View Click',
    })

    const { focusedItem, selectedTripStartDate, selectedType } = this.props

    if (!isEmpty(focusedItem) && !isNil(focusedItem) && selectedTripStartDate) {
      this.props.selectTripSummary(selectedType, focusedItem, selectedTripStartDate)
    }
  }

  checkViewMode = (viewMode: ValueOf<typeof VIEW_MODE>) => {
    if (this.props.mapType === 'fleet') {
      return this.props.viewMode === viewMode
    }

    return viewMode === VIEW_MODE.portrait
  }

  onSettingsIconClick = () => {
    this.setState({
      leftPanelOpenSidePanel: 'settings',
    })
  }
  onFiltersIconClick = () => {
    this.setState({
      leftPanelOpenSidePanel: 'filters',
    })
  }

  onSettingsPanelClickAway = () => {
    this.setState({ leftPanelOpenSidePanel: null })
  }

  onFilterPanelClickAway = () => {
    this.setState({ leftPanelOpenSidePanel: null })
  }

  getSortedItems = () => {
    if (this.props.searchType === 'fleetSearch') {
      return getSortedMapItems({
        items: this.props.items,
        vehiclePropToSortBy: this.props.vehicleDisplayName,
        selectedSortMethod: this.props.selectedSortMethod,
      })
    }

    return []
  }

  renderReTimelinePanel = () => {
    const { isComparingTrips, focusedItem, isLVVehicleActive, selectedVehicleMeta } =
      this.props

    // eslint-disable-next-line no-nested-ternary
    return isComparingTrips ? (
      <TripCompareTimeline onChangeMapHeight={this.props.changeMapHeight} />
    ) : focusedItem && !isLVVehicleActive && selectedVehicleMeta?.bottomPanel ? (
      <ReTimeline
        key={generateKeyFrom(focusedItem.id, this.props.selectedTripStartDate)}
        focusedItem={focusedItem}
        onChangeMapHeight={this.props.changeMapHeight}
        bottomPanelState={selectedVehicleMeta.bottomPanel}
      />
    ) : null
  }

  /**
   * This function is responsible on changing the type of search
   * search types are
   * @local search for vehicles, geofences, etc. that are already populated in the app
   * @placeSearch does the autocomplete search using Google Places API
   */
  handleOnChangeType = (value: PropsClass['searchType']) => {
    this.props.mapFleetDispatch({
      type: 'leftPanel_searchTypeChange',
      newType: value,
    })

    const { places, searchInput } = this.state
    if (value === 'placeSearch' && searchInput !== '' && places.query !== searchInput) {
      this.setState(
        {
          isSearching: true,
          places: {
            query: undefined,
            results: [],
            error: false,
          },
        },
        () => {
          this.handleHereFindPlaces()
        },
      )
    }
  }

  stopSearchLoading = () => {
    if (this.props.searchType === 'placeSearch' && this.state.searchInput.length < 3) {
      this.setState({
        isSearching: false,
      })
    } else if (this.props.searchType !== 'placeSearch') {
      this.setState({
        isSearching: false,
      })
    }
  }

  handleSearchCoordinate = debounce(
    (value: { lat: `${number}` | number; lng: `${number}` | number }) => {
      const onSuccess = (
        items: State['addressByCoordinate'] | Array<HereApiParsedPlace>,
      ) => {
        const { lat, lng } = value
        this.setState({
          isSearching: false,
          addressByCoordinate: items,
        })
        this.props.changeMapCenterZoom(Number(lat), Number(lng), 15)
      }

      const onFail = () => {
        this.setState({
          isSearching: false,
          addressByCoordinate: null,
        })
      }

      if (this.props.mapProviderMetaData.currentMapProvider === MapApiProvider.GOOGLE) {
        if (this.state.mapObject) {
          searchCoordinate(this.state.mapObject, value, onSuccess, onFail)
        }
      } else {
        const { hereApiCode, hereMapsLanguageCode } = this.props
        hereSearchCoordinate({
          latlng: value,
          successCB: onSuccess,
          errorCB: onFail,
          apiKey: hereApiCode,
          language: hereMapsLanguageCode,
        })
      }
    },
    1000,
  )

  createGoogleAutocompleteSuggestions = debounce(
    async (mapObject: MapsExtended.MapObject, input: string) => {
      if (
        this.props.googleAutocompleteSuggestionsAndPlaceFieldsLazy.status ===
        'not_ready'
      ) {
        return
      }
      const { triggerAutocompleteSuggestionsQuery } =
        this.props.googleAutocompleteSuggestionsAndPlaceFieldsLazy

      const center = mapObject.map.getCenter()
      const predictionsResult = await triggerAutocompleteSuggestionsQuery({
        input,
        locationBias: center
          ? getAutoCompleteBiasedPredictionBasedOnCurrentCenter(center).locationBias
          : undefined,
      })

      if (predictionsResult.isErr()) {
        this.setState({
          places: initialPlaces,
          isSearching: false,
        })
        return
      }

      this.setState({
        isSearching: false,
        places: {
          query: this.state.searchInput,
          results: Array_filterMap(
            predictionsResult.value.suggestions,
            (suggestion, { RemoveSymbol }) => {
              if (!suggestion.placePrediction) {
                return RemoveSymbol
              }

              const placePrediction = suggestion.placePrediction

              return {
                type: 'google_place_prediction',
                placeId: placePrediction.placeId,
                name: placePrediction.mainText?.text ?? '',
                address: placePrediction.text.text,
              } satisfies MapPlaceResult
            },
          ),
          error: false,
        },
      })
    },
    700,
  )

  handleHereFindPlaces = debounce(async () => {
    const onSuccess = (items: Array<HereApiParsedPlace>) => {
      this.setState({
        isSearching: false,
        places: {
          query: this.state.searchInput,
          results: items,
          error: false,
        },
      })
    }

    const onFail = () => {
      this.setState({
        isSearching: false,
        places: initialPlaces,
      })
    }

    const { hereApiCode, mapCenter, ISOCountryCode, hereMapsLanguageCode } = this.props
    hereFindPlaces({
      query: this.state.searchInput,
      countryCode: ISOCountryCode,
      locale: hereMapsLanguageCode,
      successCB: onSuccess,
      errorCB: onFail,
      hereApiCode,
      mapCenter,
    })
  }, 700)

  onLeftPanelSearchInputChange = (event: { target: { value: string } }) => {
    GA4.eventWithTimeout({
      category: 'Map',
      action: 'Left Panel - Search',
    })

    const value = event.target.value

    if (this.props.searchType === 'fleetSearch') {
      this.setState({
        searchInput: value,
      })
    } else {
      this.setState(
        {
          searchInput: value,
          isSearching: true,
        },
        () => {
          if (
            this.props.searchType === 'placeSearch' &&
            value.trim().length >= 2 &&
            (this.state.mapObject ||
              this.props.mapProviderMetaData.currentMapProvider !==
                MapApiProvider.GOOGLE)
          ) {
            if (
              this.props.mapProviderMetaData.currentMapProvider ===
              MapApiProvider.GOOGLE
            ) {
              if (this.state.mapObject) {
                this.createGoogleAutocompleteSuggestions(this.state.mapObject, value)
              }
            } else {
              this.handleHereFindPlaces()
            }
          } else if (this.props.searchType === 'placeSearch' && value === '') {
            this.setState({
              isSearching: false,
              places: initialPlaces,
            })
          } else if (this.props.searchType === 'coordinateSearch') {
            if (value === '') {
              this.setState({
                isSearching: false,
              })
            } else if (
              value.trim().split(',').length === 2 &&
              value
                .trim()
                .split(',')
                .every((i) => i.length > 0)
            ) {
              const { lat, lng } = extractLatLngFromTextInput(value)
              this.handleSearchCoordinate({ lat, lng })
            } else {
              this.setState({
                isSearching: false,
                addressByCoordinate: null,
              })
            }
          } else {
            delay(this.stopSearchLoading, 1000)
          }
        },
      )
    }
  }

  handleMapsApiLoaded: MapsExtended.OnGoogleApiLoaded = (mapObject) => {
    this.setState({ mapObject })
  }

  selectPlaceWithSideEffects = (placeLatLng: ResolvedPlaceSearchItem) => {
    this.props.dispatch(
      changeMapCenterZoom(
        placeLatLng.lat,
        placeLatLng.lng,
        MAP_PLACE_ZOOM,
        'placeClick',
      ),
    )
    this.props.dispatch(clickedLeftPanelPlace(placeLatLng))
    this.props.dispatch(setFocusedPoint(placeLatLng))
  }

  onLeftPanelSearchInputPlaceClick = async (
    _event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    placeMeta: ResolvedPlaceOrGooglePredictionSearchItem,
  ) => {
    const placeLatLng = await (async (): Promise<ResolvedPlaceSearchItem> => {
      switch (placeMeta.type) {
        case 'place': {
          return placeMeta
        }
        case 'google_place_prediction': {
          const placeholderPlace: ResolvedPlaceSearchItem = {
            type: 'place',
            id: placeMeta.placeId,
            lat: 0,
            lng: 0,
            name: placeMeta.name,
            address: placeMeta.address,
            photo: '',
          }
          const { googleAutocompleteSuggestionsAndPlaceFieldsLazy } = this.props
          if (googleAutocompleteSuggestionsAndPlaceFieldsLazy.status === 'not_ready') {
            return placeholderPlace
          }

          const detailsResult =
            await googleAutocompleteSuggestionsAndPlaceFieldsLazy.triggerPlaceFieldsQuery(
              new googleAutocompleteSuggestionsAndPlaceFieldsLazy.placesLib.Place({
                id: placeMeta.placeId,
              }),
            )

          if (detailsResult.isErr()) {
            // Something went wrong, return a placeholder
            return placeholderPlace
          }

          return createStandardPlaceDetailsFromGooglePlace(detailsResult.value)
        }
      }
    })()

    this.selectPlaceWithSideEffects(placeLatLng)
  }

  onLeftPanelPlaceItemClick: ListItemProps['events']['onPlaceClick'] = (
    _event,
    place,
  ) => {
    this.selectPlaceWithSideEffects(place)
  }

  renderLeftPanel = () => {
    const { googleAutocompleteSuggestionsAndPlaceFieldsLazy } = this.props
    if (this.props.visibleLeftPanel === 'nearbyVehicles') {
      return (
        <NearbyVehiclesLeftPanel
          history={this.props.history}
          currentMapProvider={this.props.mapProviderMetaData.currentMapProvider}
          currentMapTypeId={this.props.mapTypeId}
        />
      )
    } else if (this.props.visibleLeftPanel === 'compareTrips') {
      return <ReTripCompare />
    } else if (this.checkViewMode(VIEW_MODE.portrait)) {
      const { mapType, searchType } = this.props

      return (
        <MapViewSearchProvider>
          <ReLeftPanel
            mapTypeId={this.props.mapTypeId}
            mapApiProvider={this.props.mapProviderMetaData.currentMapProvider}
            mapType={mapType}
            focusedItem={this.props.focusedItem}
            visibleLeftPanel={this.props.visibleLeftPanel}
            changeSearchType={this.handleOnChangeType}
            searchType={searchType}
            places={this.getPlacesResult()}
            nonCompactedItems={
              this.state.searchInput
                ? this.resolvedSearchItems()
                : this.getSortedItems()
            }
            searchInput={this.state.searchInput}
            isSearching={this.state.isSearching}
            isFetchingPlacesResultsInPopover={
              googleAutocompleteSuggestionsAndPlaceFieldsLazy.suggestionsQueryMeta
                .fetchStatus === 'fetching' || this.state.isSearching
            }
            onSearchInputChange={this.onLeftPanelSearchInputChange}
            onSettingsIconClick={this.onSettingsIconClick}
            onFiltersIconClick={this.onFiltersIconClick}
            onVehicleClick={this.handleLeftPanelVehicleClick}
            leftPanelOpenSidePanel={this.state.leftPanelOpenSidePanel}
            onSettingsPanelClickAway={this.onSettingsPanelClickAway}
            onFilterPanelClickAway={this.onFilterPanelClickAway}
            items={
              this.state.searchInput
                ? this.resolvedSearchItems()
                : this.getSortedItems()
            }
            onSearchInputPlaceClick={this.onLeftPanelSearchInputPlaceClick}
            onLeftPanelPlaceItemClick={this.onLeftPanelPlaceItemClick}
            isFetchingGooglePlaceDetails={
              googleAutocompleteSuggestionsAndPlaceFieldsLazy.placesQueryMeta
                .fetchStatus === 'fetching'
            }
          />
        </MapViewSearchProvider>
      )
    }
    return null
  }

  getPlacesResult = () => {
    const { results } = this.state.places
    return this.props.searchType === 'placeSearch' ? results : []
  }

  renderBottomPanelSection = () => {
    const {
      props: { vehicleDetailsActivityTab },
      checkViewMode,
      handleFocusVehicleOnMap,
      renderReTimelinePanel,
    } = this

    if (vehicleDetailsActivityTab === 'booking') {
      return null
    }

    if (checkViewMode(VIEW_MODE.portrait)) {
      return renderReTimelinePanel()
    } else if (checkViewMode(VIEW_MODE.landscape)) {
      return (
        <ReLandscapeBottomPanel
          handleFocusVehicleOnMap={handleFocusVehicleOnMap}
          onChangeMapHeight={this.props.changeMapHeight}
        />
      )
    }

    return null
  }

  // Make this a stable object to prevent re-renders on vehicle-marker. Huge perf gain
  onVehicleHover: OnVehicleHoverHandler = {
    onMouseEnter: this.handleMouseEnterVehicle,
    onMouseLeave: this.handleMouseLeaveVehicle,
  }

  render() {
    const {
      focusedItem,
      history,
      hoveredVehicleMarkerData,
      isComparingTrips,
      mapViewVisibleRightPanel,
      location,
      mapProviderMetaData,
      mapHeight,
      preferences,
      selectedTripStartDate: focusedItemStartDate,
      selectedType,
      timelineStartTime,
      mapType,
      viewMode,
      activeEvent,
      clustersEnabled,
      isStreetViewVisible,
      hasCurrentMapProviderChangedAfterInitialization,
      mapTypeId,
      onChangeMapTypeId,
    } = this.props

    const { mapObject } = this.state

    const showDetailsPanel =
      this.checkViewMode(VIEW_MODE.portrait) &&
      focusedItem &&
      !this.props.isLVVehicleActive &&
      this.props.visibleLeftPanel === 'default'

    const mapProps = {
      changeViewMode: this.handleChangeViewMode,
      focusedItem,
      focusedItemStartDate,
      isComparingTrips,
      location,
      history,
      mapApiProviderId: mapProviderMetaData.currentMapProvider,
      mapHeight,
      onVehicleMarkerClick: this.handleVehicleMarkerClick,
      onEventMarkerClick: this.handleEventMarkerClick,
      onFullscreenClick: this.handleFullscreenClick,
      onChangeMapTypeId,
      onVehicleHover: this.onVehicleHover,
      selectedType,
      timelineStartTime,
      type: mapType,
      viewMode,
      mapObject,
      mapTypeId,
    } satisfies Partial<MapComponentProps>

    const isAnyLeftPanelSidePanelOpen = !!this.state.leftPanelOpenSidePanel

    return (
      <Fragment>
        <SideEffects
          items={this.props.items}
          activityDateRange={this.props.activityDateRange}
          location={location}
          haveVehiclesLoaded={this.props.haveVehiclesLoaded}
        />
        {mapViewVisibleRightPanel === 'live-vision' && <LiveVision />}
        <div className="MapView">
          {this.renderLeftPanel()}
          {showDetailsPanel && <ReFleetDetailsPanel focusedVehicle={focusedItem} />}
          <RightContent
            className={`RightPanel${isStreetViewVisible ? ' is-streetView' : ''}`}
          >
            <MapThemeContextProvider mapTypeId={mapTypeId}>
              <MapAreaThemeContextProvider
                fullscreen={this.props.viewMode === VIEW_MODE.fullscreen}
              >
                {mapProviderMetaData.currentMapProvider === MapApiProvider.GOOGLE ? (
                  <Map
                    {...mapProps}
                    onVehicleHover={mapProps.onVehicleHover}
                    mapProviderMetaData={{
                      currentMapProvider: mapProviderMetaData.currentMapProvider,
                      selectionUI: mapProviderMetaData.selectionUI,
                    }}
                    onMapsApiLoaded={this.handleMapsApiLoaded}
                    places={this.getPlacesResult()}
                    forceMapUpdate={this.props.forceMapUpdate}
                    timelineSliderPlaying={this.props.timelineSliderPlaying}
                    useVehicleIconColor={this.props.useVehicleIconColor}
                    vehicleDisplayName={this.props.vehicleDisplayName}
                    extraContent={
                      <MapControls>
                        <MapControlsTopLeftContainer>
                          {clustersEnabled && mapViewVisibleRightPanel === null && (
                            <ClustersChipsGoogle />
                          )}
                        </MapControlsTopLeftContainer>
                      </MapControls>
                    }
                  />
                ) : (
                  <MapOpenLayers
                    focusVehiclesOnMapMountAndVehiclesSizeChange={
                      !hasCurrentMapProviderChangedAfterInitialization
                    }
                    mapProviderMetaData={{
                      currentMapProvider: this.props.mapProviderMetaData
                        .currentMapProvider as UserAvailableMapApiProvider_Leaflet,
                      selectionUI: this.props.mapProviderMetaData.selectionUI,
                    }}
                    mapTypeId={mapTypeId}
                    mapHeight={mapProps.mapHeight}
                    changeViewMode={this.handleChangeViewMode}
                    focusedItem={focusedItem}
                    location={location}
                    history={history}
                    onEventMarkerClick={this.handleEventMarkerClick}
                    onVehicleMarkerClick={mapProps.onVehicleMarkerClick}
                    onVehicleHover={mapProps.onVehicleHover}
                    onFullscreenClick={this.handleFullscreenClick}
                    timelineStartTime={timelineStartTime}
                    mapType={mapProps.type}
                    viewMode={viewMode}
                    preferences={preferences}
                    fullscreenState={{
                      focusedItemStartDate,
                      selectedType,
                    }}
                  />
                )}
              </MapAreaThemeContextProvider>
            </MapThemeContextProvider>
            {this.renderBottomPanelSection()}
            {(() => {
              if (hoveredVehicleMarkerData !== null) {
                return (
                  <StyledMapEventInfoPanel
                    isAnyLeftPanelSidePanelOpen={isAnyLeftPanelSidePanelOpen}
                    showEventInfoPanel={this.props.showEventInfoPanel}
                    eventData={{
                      origin: 'HOVERED_MAP_VEHICLE',
                      event: hoveredVehicleMarkerData,
                    }}
                    hardwareType={this.props.mapType}
                    onEventPanelToggle={this.handleToggleEventPanel}
                  />
                )
              }

              if (!showDetailsPanel || activeEvent.data === null) {
                return null
              }

              // NOTE: do not render the info panel to avoid heavy calculation and rendering if it's closed
              if (this.props.showEventInfoPanel) {
                return activeEvent.type === 'daily' ? (
                  <StyledMapEventInfoPanel
                    isAnyLeftPanelSidePanelOpen={isAnyLeftPanelSidePanelOpen}
                    showEventInfoPanel={this.props.showEventInfoPanel}
                    eventData={{
                      origin: 'FOCUSED_ITEM',
                      event: activeEvent.data,
                    }}
                    hardwareType={this.props.mapType}
                    onEventPanelToggle={this.handleToggleEventPanel}
                  />
                ) : (
                  activeEvent.type === 'multipleDays' && activeEvent.data !== null && (
                    <StyledMapMultipleDaysEventInfoPanel
                      event={activeEvent.data}
                      mapType={this.props.mapType}
                      showEventInfoPanel={this.props.showEventInfoPanel}
                      onEventPanelToggle={this.handleToggleEventPanel}
                    />
                  )
                )
              } else {
                return (
                  <div
                    className="MapEventInfoPanel-toggle MapEventInfoPanel-toggle-dummy"
                    onClick={() => this.handleToggleEventPanel(true)}
                  >
                    <Icon
                      icon="chevron-right"
                      className="isToggleIcon"
                    />
                  </div>
                )
              }
            })()}
          </RightContent>
          {mapProviderMetaData.currentMapProvider === MapApiProvider.GOOGLE &&
            clustersEnabled && (
              <>
                {mapViewVisibleRightPanel === 'vehicle-clusters' && (
                  <ReVehicleClusterPanel />
                )}
                <VehicleClusterOnBoardingModal />
              </>
            )}
        </div>
      </Fragment>
    )
  }
}

const mapStateToProps = (state: AppState) => {
  const preferences = getPreferences(state)
  const {
    vehicleDisplayName,
    useVehicleIconColor,
    mapViewGroups: { fleet: groupsEnabled },
    showEventInfoPanel,
  } = preferences

  const { timelineStartTime, timelineEndTime } = getTimelineEventsUIDateRange(state)

  return {
    selectedSortMethod: getTypeSortMethod(state),
    haveVehiclesLoaded: getVehiclesHaveBeenLoadedAtLeastOnce(state),
    sortedExtraSearchItems: getSortedLeftPanelExtras(state),
    selectedTripStartDate: getSelectedTripStartDate(state),
    isComparingTrips: getIsComparingTrips(state),
    activeEvent: getActiveTimelineEventByActivity(state),
    focusedVehicleId: getFocusedVehicleId(state),
    focusedGroup: getFocusedGroup(state),
    timelineSliderPlaying: getTimelineSliderPlaying(state),
    preferences,
    vehicleDisplayName,
    useVehicleIconColor,
    showEventInfoPanel,
    groupsEnabled,
    viewMode: getViewMode(state),
    prevViewMode: getPrevViewMode(state),
    mapViewVisibleRightPanel: getMapViewVisibleRightPanel(state),
    mapState: getMapSize(state),
    isLVVehicleActive: !isNil(getActiveVehicle(state)),
    timelineStartTime,
    timelineEndTime,
    visibleLeftPanel: getVisibleLeftPanel(state),
    hoveredVehicleMarkerData: getHoveredVehicleMarkerData(state),
    activityDateRange: getActivityDateRange(state),
    vehicleDetailsActivityTab: getActivityActiveTab(state),
    clustersEnabled: getClustersEnabled(state),
    isStreetViewVisible: getIsStreetViewVisible(state),
  }
}

const mapDispatchToProps = {
  changeViewMode,
  clearDashboardVehicleList,
  focusVehicle,
  clickedLeftPanelVehicle,
  jumpToTime,
  savePreferences,
  selectTripSummary,
  clickedVehicleMarker,
  resetZoomFirstTimeLoading,
  onVehicleMarkerMouseEnter,
  onVehicleMarkerMouseLeave,
}

const Connected = connect(mapStateToProps, mapDispatchToProps)(MapViewFunctional)

const EnhancedComponent: FixMeAny = mapViewContainer(Connected as FixMeAny, {
  getItems,
  getFocusedItem,
})

// Since mapContainer return type is not correct right now we have to subtract, from MapView, props provided by mapContainer
export default EnhancedComponent as ComponentType<
  Except<ComponentProps<typeof Connected>, keyof MapViewContainerInjectedProps>
>

type GetSortedMapItemsParams = {
  items: PropsClass['items']
  selectedSortMethod: 'status' | 'alphabetical'
  vehiclePropToSortBy: PropsClass['vehicleDisplayName']
}

/* We can use object as param because a isEqual function is used, otherwise it would always run the function
since object param would always be different. But since we do custom comparison it's fine.
*/
const getSortedMapItems = memoizeOne(
  ({ items, selectedSortMethod, vehiclePropToSortBy }: GetSortedMapItemsParams) => {
    const sortedItems = (() => {
      const vehicleSortMethod = vehicleSortMethods[selectedSortMethod]
      return (
        isFunction(vehicleSortMethod) && vehicleSortMethod(items, vehiclePropToSortBy)
      )
    })()

    return sortedItems ? sortedItems : items
  },
  ([currentParams_], [prevParams_]) => {
    const currentParams = currentParams_ as GetSortedMapItemsParams
    const prevParams = prevParams_ as GetSortedMapItemsParams

    const { items, vehiclePropToSortBy, selectedSortMethod } = currentParams

    return !(
      prevParams.selectedSortMethod !== selectedSortMethod ||
      prevParams.vehiclePropToSortBy !== vehiclePropToSortBy ||
      prevParams.items !== items
    )
  },
)

const getMapSearchResults = memoizeOne(
  (
    value: string,
    flatItems: ReadonlyArray<
      PropsClass['items'][number] | PropsClass['sortedExtraSearchItems'][number]
    >,
    items: PropsClass['items'],
    groupsEnabled: boolean,
  ): ReadonlyArray<MapSearchResult> => {
    // Filter flat items (vehicles + groups + geofences + landmarks)
    const filteredArray = (() => {
      if (isEmpty(value)) {
        return flatItems
      }

      type Vehicle = ReturnType<typeof getActiveVehiclesWithDrivers>[number]

      const filters: Filters<Vehicle> = {
        search: [
          (i) =>
            i.driverName.status === 'UNDISCLOSED' ? '' : (i.driverName.name ?? ''),
        ],
      }

      const { itemMatchesWithTextAndFilters } =
        generateItemMatchesWithTextAndFilters(value)

      const isItemAVehicle = (item: unknown): item is Vehicle =>
        isObject(item) && (item as Vehicle).__entityType__ === 'VEHICLE'

      return flatItems.filter(
        (i) =>
          (isItemAVehicle(i) && itemMatchesWithTextAndFilters(i, filters)) ||
          stringSearch(i, value),
      )
    })().slice(0, 25)

    if (!groupsEnabled) {
      return filteredArray
    }

    // Find all the available groups
    const groups = items.filter(
      (item): item is ExtractStrict<(typeof items)[number], { type: 'group' }> =>
        item.type === 'group',
    )
    // Find groups that the filtered vehicles belong to
    const matchedGroups: typeof groups = []
    for (const { id, type } of filteredArray) {
      if (type === 'group') continue
      const matches = groups.filter((group) =>
        group.items?.some((item) => item.id === id),
      )
      if (matches.length > 0) matchedGroups.push(...matches)
    }

    // There could be duplicate groups because multiple vehicles in the filteredArray could belong to the same group
    return uniqBy([...filteredArray, ...matchedGroups], 'id')
  },
)

const RightContent = styled('div')`
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 0;
`

const StyledMapEventInfoPanel = styled(MapEventInfoPanel)<{
  isAnyLeftPanelSidePanelOpen: boolean
}>((props) =>
  props.isAnyLeftPanelSidePanelOpen ? {} : { left: `-${variables.mapLeftPanelWidth}` },
)

const StyledMapMultipleDaysEventInfoPanel = styled(MapMultipleDaysEventInfoPanel)({
  left: `-${variables.mapLeftPanelWidth}`,
})

const MapControls = styled('div')(({ theme }) =>
  theme.unstable_sx({
    position: 'absolute',
    height: '100%',
    width: '100%',
    display: 'grid',
    gridTemplateColumns: '1fr',
    gridTemplateRows: '1fr',
  }),
)

const MapControlsTopLeftContainer = styled('div')(({ theme }) =>
  theme.unstable_sx({
    alignSelf: 'start',
    justifySelf: 'start',
    p: 1.5,
    zIndex: 1,
    // Can be removed once the controls on top-right are within a container as well
    width: '50%', //so that these controls don't overlap with the right content (layers menu, etc)
  }),
)
