import {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
  type Dispatch,
  type SetStateAction,
} from 'react'
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  ClickAwayListener,
  FormControlLabel,
  FormGroup,
  NativeLink,
  Paper,
  Skeleton,
  Stack,
  TextField,
  Typography,
  type PaperProps,
} from '@karoo-ui/core'
import CheckBoxIcon from '@mui/icons-material/CheckBox'
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank'
import { useController, type Control } from 'react-hook-form'
import { useIntl } from 'react-intl'
import { useDispatch } from 'react-redux'
import { match, P } from 'ts-pattern'

import type { Geofence } from 'api/alerts/alerts'
import type { GeofenceId, PositionDescription } from 'api/types'
import {
  fetchGeofences,
  fetchSystemGeofences,
  getGeofenceAndGeofenceGroupsOptions,
  getGeofencesQueryIsFetching,
} from 'duxs/geofences'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { useAlertTypesQuery } from 'src/modules/alerts/api/useAlertTypesQuery'
import { UserFormattedPositionAddress } from 'src/modules/components/connected/UserFormattedPositionAddress'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import type { FleetAlertsSchema } from '../schema'

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />
const checkedIcon = <CheckBoxIcon fontSize="small" />

const geofenceTypeOptions: Array<{ label: string; value: 'user' | 'system' }> = [
  { label: 'User geofences', value: 'user' },
  { label: 'System geofences', value: 'system' },
]

const Locations = ({
  control,
  conditionIndex,
}: {
  control: Control<FleetAlertsSchema>
  conditionIndex: number
}) => {
  const dispatch = useDispatch()
  const autocompleteRef = useRef<HTMLDivElement>(null)
  const { field } = useController({
    control,
    name: `conditionTypes.${conditionIndex}`,
  })

  const { onChange, value } = field

  const [open, setOpen] = useState(false)
  const [showSelectAll, setShowSelectAll] = useState<boolean>(
    value.type === 'geofence' &&
      value.values &&
      value.values.geofenceOrGeofenceGroup.length > 0 &&
      value.values.geofenceOrGeofenceGroup[0].value === 'all',
  )
  const { formatList } = useIntl()
  const { data: alertTypesQueryData, isPending: alertTypeQueryPending } =
    useAlertTypesQuery()
  const geofenceOrGeofenceGroupOptions = useTypedSelector(
    getGeofenceAndGeofenceGroupsOptions,
  )
  const geofencesQueryIsFetching = useTypedSelector(getGeofencesQueryIsFetching)

  useEffect(() => {
    dispatch(fetchGeofences())
    dispatch(fetchSystemGeofences())
  }, [dispatch])

  const selectedGeofenceType = useMemo(
    () => (value.type === 'geofence' ? value.values.geofenceType : 'user'),
    [value],
  )

  const selectedMasterGeofenceType = useMemo(
    () =>
      value.type === 'geofence' && value.values.geofenceType === 'system'
        ? value.values.zoneType
        : (alertTypesQueryData?.systemZoneTypes[0].value ?? '2'),
    [alertTypesQueryData?.systemZoneTypes, value],
  )

  // To get the correct error object
  const { fieldState } = useController({
    control,
    name: `conditionTypes.${conditionIndex}.values`,
  })

  const filteredGeofencesList = useMemo(
    () =>
      match(selectedGeofenceType)
        .with('user', () =>
          (geofenceOrGeofenceGroupOptions || []).map((g) => ({
            ...g,
            geofenceType: selectedGeofenceType,
          })),
        )
        .with('system', () =>
          (alertTypesQueryData?.systemZones[selectedMasterGeofenceType] || []).map(
            (g) => {
              const geoId = g.value as GeofenceId
              return {
                id: geoId,
                value: geoId,
                key: geoId,
                name: g.name,
                label: g.label,
                isGroup: false,
                address: null,
                geofenceType: selectedGeofenceType,
              } as Geofence & { geofenceType: 'system' | 'user' }
            },
          ),
        )
        .otherwise(() => [])
        .sort((a, b) => {
          if (a.isGroup === b.isGroup) return 0
          return a.isGroup ? -1 : 1
        }),
    [
      alertTypesQueryData?.systemZones,
      geofenceOrGeofenceGroupOptions,
      selectedGeofenceType,
      selectedMasterGeofenceType,
    ],
  )

  const filteredOptions = useMemo(
    () =>
      showSelectAll
        ? [
            {
              value: 'all' as const,
              label: 'global.allGeofences',
              name: ctIntl.formatMessage({
                id: 'global.allGeofences',
              }),
              id: 'all' as const,
              key: 'all' as const,
              isGroup: false as const,
              geofenceType: selectedGeofenceType,
            },
            ...filteredGeofencesList,
          ]
        : filteredGeofencesList,
    [filteredGeofencesList, selectedGeofenceType, showSelectAll],
  )

  const contextActionValues: ContextActions = useMemo(() => {
    const handleReset = () => {
      if (value.type === 'geofence')
        onChange({
          ...value,
          values: {
            ...value.values,
            geofenceOrGeofenceGroup: [],
          },
        })
    }

    return {
      handleReset,
      showSelectAll: { open: showSelectAll, setOpen: setShowSelectAll },
    }
  }, [onChange, showSelectAll, value])

  if (value.type !== 'geofence') {
    return null
  }

  if (alertTypeQueryPending || geofencesQueryIsFetching) {
    return (
      <Stack sx={{ display: 'flex', flex: 1, gap: 3 }}>
        <Stack
          sx={{
            display: 'flex',
            flex: 1,
            flexDirection: 'row',
            gap: 2,
            alignItems: 'center',
          }}
        >
          <Skeleton
            variant="rectangular"
            width={16}
            height={16}
          />
          <Skeleton
            animation="wave"
            height={20}
            width={150}
          />
          <Skeleton
            variant="rectangular"
            width={16}
            height={16}
          />
          <Skeleton
            animation="wave"
            height={20}
            width={150}
          />
        </Stack>
        <Skeleton
          animation="wave"
          height={20}
          width={'100%'}
        />
        <Stack sx={{ display: 'flex', flex: 1, flexDirection: 'row', gap: 2 }}>
          <Skeleton
            animation="wave"
            height={20}
            width={'50%'}
          />
          <Skeleton
            animation="wave"
            height={20}
            width={'50%'}
          />
        </Stack>
      </Stack>
    )
  }

  return (
    <Stack gap={2}>
      <FormGroup sx={{ gap: 1, flex: 1, flexDirection: 'row' }}>
        <FormControlLabel
          control={
            <Checkbox
              name="whenEntersGeofence"
              value={value.values.whenEntersGeofence}
              checked={value.values.whenEntersGeofence}
              onChange={(e, checked) => {
                onChange({
                  ...value,
                  values: { ...value.values, [e.target.name]: checked },
                })
              }}
            />
          }
          label={
            <Stack>
              <Typography>
                {ctIntl.formatMessage({
                  id: 'alerts.condition.geofence.setting.enterGeofence',
                })}
              </Typography>
            </Stack>
          }
        />
        <FormControlLabel
          control={
            <Checkbox
              name="whenLeavesGeofence"
              value={value.values.whenLeavesGeofence}
              checked={value.values.whenLeavesGeofence}
              onChange={(e, checked) => {
                onChange({
                  ...value,
                  values: { ...value.values, [e.target.name]: checked },
                })
              }}
            />
          }
          label={
            <Stack>
              <Typography>
                {ctIntl.formatMessage({
                  id: 'alerts.condition.geofence.setting.leavesGeofence',
                })}
              </Typography>
            </Stack>
          }
        />
      </FormGroup>
      <Autocomplete
        size="small"
        {...getAutocompleteVirtualizedProps({
          options: geofenceTypeOptions,
        })}
        disableClearable
        disabled={!(value.values.whenEntersGeofence || value.values.whenLeavesGeofence)}
        value={geofenceTypeOptions.find((o) => o.value === selectedGeofenceType)}
        onChange={(_, selectedOption) => {
          onChange({
            ...value,
            values: {
              ...value.values,
              ...(selectedOption.value === 'user'
                ? { geofenceType: 'user' }
                : { geofenceType: 'system', zoneType: '' }),
              geofenceOrGeofenceGroup: [],
            },
          })
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            label={ctIntl.formatMessage({
              id: 'Geofence type',
            })}
          />
        )}
        sx={{
          '.MuiInputBase-root': {
            backgroundColor: 'white',
          },
        }}
      />
      <Stack sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}>
        {selectedGeofenceType === 'system' && (
          <Autocomplete
            sx={{
              width: '30%',
              '.MuiInputBase-root': {
                backgroundColor: 'white',
              },
            }}
            size="small"
            {...getAutocompleteVirtualizedProps({
              options: alertTypesQueryData?.systemZoneTypes || [],
            })}
            disableClearable
            disabled={
              !(value.values.whenEntersGeofence || value.values.whenLeavesGeofence)
            }
            value={(alertTypesQueryData?.systemZoneTypes || []).find(
              (o) => o.value === selectedMasterGeofenceType,
            )}
            onChange={(_, selectedOption) => {
              onChange({
                ...value,
                values: {
                  ...value.values,
                  geofenceType: 'system',
                  zoneType: selectedOption.value,
                  geofenceOrGeofenceGroup: [],
                },
              })
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                label={ctIntl.formatMessage({
                  id: 'Zone Type',
                })}
              />
            )}
          />
        )}
        <GeofenceSelectionContext.Provider value={contextActionValues}>
          <ClickAwayListener
            onClickAway={(event: MouseEvent | TouchEvent) => {
              if (
                autocompleteRef.current &&
                !autocompleteRef.current.contains(event.target as Node)
              ) {
                setOpen(false)
              }
            }}
          >
            <Box
              sx={{ width: '100%' }}
              ref={autocompleteRef}
            >
              <Autocomplete
                open={open}
                onOpen={() => setOpen(true)}
                onClose={(_, reason) => {
                  if (reason === 'toggleInput') setOpen(false)
                }}
                fullWidth
                multiple
                options={filteredOptions}
                disableCloseOnSelect
                disabled={
                  !(value.values.whenEntersGeofence || value.values.whenLeavesGeofence)
                }
                getOptionLabel={(option) => option.name}
                getOptionKey={(option) => option.id}
                groupBy={(option) =>
                  match([option.isGroup, option.id])
                    .with([false, 'all'], () => '')
                    .with([true, P._], () => ctIntl.formatMessage({ id: 'Groups' }))
                    .otherwise(() => ctIntl.formatMessage({ id: 'Geofences' }))
                }
                slots={{ paper: CustomPaper }}
                renderOption={(props, option, { selected }) => {
                  const { key, ...optionProps } = props
                  return (
                    <li
                      key={key}
                      {...optionProps}
                    >
                      <Checkbox
                        icon={icon}
                        checkedIcon={checkedIcon}
                        style={{ marginRight: 8 }}
                        checked={selected}
                      />
                      <Stack>
                        <Stack flexDirection="row">
                          {option.name}
                          {option.isGroup && (
                            <Typography
                              sx={(theme) => ({
                                ml: 0.5,
                                color: theme.palette.text.secondary,
                              })}
                            >
                              {`(${option.itemIds.length})`}
                            </Typography>
                          )}
                        </Stack>
                        <Typography
                          variant="caption"
                          sx={(theme) => ({
                            color: theme.palette.text.secondary,
                          })}
                        >
                          {'address' in option && (
                            <UserFormattedPositionAddress
                              gpsFixType={null}
                              address={option.address as PositionDescription}
                            />
                          )}
                        </Typography>
                      </Stack>
                    </li>
                  )
                }}
                value={value.values.geofenceOrGeofenceGroup}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                renderTags={() => {
                  const groupsCount = value.values.geofenceOrGeofenceGroup.filter(
                    (g) => g.isGroup,
                  ).length
                  const geofencesCount =
                    value.values.geofenceOrGeofenceGroup.length - groupsCount
                  const allIsSelected = value.values.geofenceOrGeofenceGroup.filter(
                    (g) => g.id === 'all',
                  ).length
                  const previews = []
                  if (allIsSelected > 0) {
                    return ctIntl.formatMessage({ id: 'global.allGeofences' })
                  }

                  if (geofencesCount) {
                    previews.push(
                      `${geofencesCount} ${ctIntl.formatMessage({
                        id: 'Geofences are selected',
                      })}`,
                    )
                  }

                  if (groupsCount) {
                    previews.push(
                      `${groupsCount} ${ctIntl.formatMessage({
                        id: 'Groups are selected',
                      })}`,
                    )
                  }

                  return formatList(previews, { type: 'unit' })
                }}
                onChange={(_event, selectedOption) => {
                  if (
                    value.values.geofenceOrGeofenceGroup.length === 1 &&
                    value.values.geofenceOrGeofenceGroup[0].id === 'all'
                  ) {
                    onChange({
                      ...value,
                      values: {
                        ...value.values,
                        geofenceOrGeofenceGroup: selectedOption.filter(
                          (v) => v.id !== 'all',
                        ),
                      },
                    })
                  } else if (selectedOption.some((v) => v.id === 'all')) {
                    onChange({
                      ...value,
                      values: {
                        ...value.values,
                        geofenceOrGeofenceGroup: selectedOption.filter(
                          (v) => v.id === 'all',
                        ),
                      },
                    })
                  } else {
                    onChange({
                      ...value,
                      values: {
                        ...value.values,
                        geofenceOrGeofenceGroup: selectedOption.filter(
                          (v) => v.id !== 'all',
                        ),
                      },
                    })
                  }
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={ctIntl.formatMessage({
                      id: 'Geofences',
                    })}
                    error={!!fieldState.error}
                    helperText={ctIntl.formatMessage({
                      id: fieldState.error?.message ?? '',
                    })}
                  />
                )}
                sx={{
                  '.MuiInputBase-root': {
                    backgroundColor: 'white',
                  },
                }}
              />
            </Box>
          </ClickAwayListener>
        </GeofenceSelectionContext.Provider>
      </Stack>
    </Stack>
  )
}

type ContextActions = {
  handleReset: () => void
  showSelectAll: { open: boolean; setOpen: Dispatch<SetStateAction<boolean>> }
}
const GeofenceSelectionContext = createContext<ContextActions | undefined>(undefined)

function CustomPaper(props: PaperProps) {
  const context = useContext(GeofenceSelectionContext)

  if (!context) return
  return (
    <Paper {...props}>
      {!context.showSelectAll.open && (
        <Stack
          gap={1}
          sx={{ px: 2, pt: 1 }}
        >
          <NativeLink
            underline="hover"
            sx={{ cursor: 'pointer' }}
            color="primary"
            onClick={() => context.showSelectAll.setOpen(true)}
          >
            {ctIntl.formatMessage({
              id: 'alerts.modal.section.conditionsSelection.geofence.selectAllGeofence',
            })}
          </NativeLink>
        </Stack>
      )}

      {props.children}
      <Button
        variant="text"
        onClick={() => context.handleReset()}
      >
        {ctIntl.formatMessage({ id: 'Reset All' })}
      </Button>
    </Paper>
  )
}
export default Locations
