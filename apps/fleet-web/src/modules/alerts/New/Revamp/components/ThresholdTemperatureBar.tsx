import { Box, Stack, Tooltip, Typography } from '@karoo-ui/core'
import * as R from 'remeda'
import { match, P } from 'ts-pattern'

import { ctIntl } from 'src/util-components/ctIntl'

type Props = {
  values: {
    applyOnLowerThreshold: boolean | null
    applyOnHigherThreshold: boolean | null
    lowerThresholdValue: number | null
    higherThresholdValue: number | null
  }
}

const getArrayOfValues = (values: Props['values']) => {
  const graphValues: { lower?: Array<string>; higher?: Array<string> } = {
    lower: undefined,
    higher: undefined,
  }

  const {
    applyOnLowerThreshold,
    lowerThresholdValue,
    applyOnHigherThreshold,
    higherThresholdValue,
  } = values

  if (applyOnLowerThreshold && R.isNonNull(lowerThresholdValue)) {
    const lowerTarget = lowerThresholdValue - 3
    const arr = []
    for (let index = lowerThresholdValue; index > lowerTarget; index--) {
      arr.push(`${Number(index).toFixed(1)}°C`)
    }
    arr.push('...')
    graphValues.lower = arr.reverse()
  }

  if (applyOnHigherThreshold && R.isNonNull(higherThresholdValue)) {
    const higherTarget = higherThresholdValue + 3
    const arr = []
    for (let index = higherThresholdValue; index < higherTarget; index++) {
      arr.push(`${Number(index).toFixed(1)}°C`)
    }
    arr.push('...')
    graphValues.higher = arr
  }

  return graphValues
}

const ThresholdTemperatureBar = ({ values }: Props) => {
  if (!values.applyOnLowerThreshold && !values.applyOnHigherThreshold) {
    return null
  }

  const updatedGraphValues = getArrayOfValues(values)

  return (
    <Stack sx={{ flex: 0.5 }}>
      <Box
        sx={{
          width: '100%',
          height: 10,
          backgroundColor: 'lightgray',
          borderRadius: 4,
          display: 'flex',
        }}
      >
        <Tooltip
          title={match(updatedGraphValues)
            .with({ lower: P.nonNullable }, () =>
              ctIntl.formatMessage(
                { id: 'alerts.engine.coolant.temperature.trigger.bellow' },
                { values: { degrees: values.lowerThresholdValue } },
              ),
            )
            .with({ higher: P.nonNullable, lower: P.nullish }, () =>
              ctIntl.formatMessage(
                { id: 'alerts.engine.coolant.temperature.notTrigger.below' },
                { values: { degrees: values.higherThresholdValue } },
              ),
            )
            .otherwise(() => undefined)}
        >
          <Box
            sx={(theme) => ({
              flex: 0.5,
              backgroundColor: updatedGraphValues.lower
                ? theme.palette.primary.main
                : 'transparent',
              borderRadius: 4,
            })}
          />
        </Tooltip>

        {updatedGraphValues.lower && updatedGraphValues.higher && (
          <Tooltip
            title={ctIntl.formatMessage(
              { id: 'alerts.engine.coolant.temperature.trigger.between' },
              {
                values: {
                  degreesFrom: values.lowerThresholdValue,
                  degreesTo: values.higherThresholdValue,
                },
              },
            )}
          >
            <Box sx={{ flex: 0.3 }} />
          </Tooltip>
        )}

        <Tooltip
          title={match(updatedGraphValues)
            .with({ higher: P.nonNullable }, () =>
              ctIntl.formatMessage(
                { id: 'alerts.engine.coolant.temperature.trigger.above' },
                { values: { degrees: values.higherThresholdValue } },
              ),
            )
            .with({ higher: P.nullish, lower: P.nonNullable }, () =>
              ctIntl.formatMessage(
                { id: 'alerts.engine.coolant.temperature.notTrigger.above' },
                { values: { degrees: values.lowerThresholdValue } },
              ),
            )
            .otherwise(() => undefined)}
        >
          <Box
            sx={(theme) => ({
              flex: 0.5,
              backgroundColor: updatedGraphValues.higher
                ? theme.palette.primary.main
                : 'transparent',
              borderRadius: 4,
            })}
          />
        </Tooltip>
      </Box>

      <Stack sx={{ display: 'flex', flexDirection: 'row' }}>
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            flex: 0.5,
            justifyContent: 'space-between',
          }}
        >
          {updatedGraphValues.lower?.map((v) => (
            <Typography key={v}>{v}</Typography>
          ))}
        </Stack>

        {updatedGraphValues.lower && updatedGraphValues.higher && (
          <Stack sx={{ flex: 0.3, display: 'flex', alignItems: 'center' }}>
            <Typography>{'...'}</Typography>
          </Stack>
        )}

        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            flex: 0.5,
            justifyContent: 'space-between',
          }}
        >
          {updatedGraphValues.higher?.map((v) => (
            <Typography key={v}>{v}</Typography>
          ))}
        </Stack>
      </Stack>
    </Stack>
  )
}

export default ThresholdTemperatureBar
