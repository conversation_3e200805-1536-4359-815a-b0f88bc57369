import { createR<PERSON>, PureComponent, type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, type RefObject } from 'react'
import type { ChangeEventValue, ClickEventValue } from 'google-map-react'
import type * as H from 'history'
import type { Cluster, Points } from 'points-cluster'
import { connect } from 'react-redux'
import type { SetOptional } from 'type-fest'

import { getMapVehicles, setLayerVisibility } from 'duxs/map'
import { getCurrentLayerVisibility } from 'duxs/map-timeline'
import { getTimelineEventsByActivityAndMapType } from 'duxs/timeline/index'
import { getPreferences } from 'duxs/user-sensitive-selectors'
import { MapApiProvider } from 'src/api/user/types'
import { MapThemeContextProvider } from 'src/components/context/MapThemeContext'
import { UserGoogleBaseMapWithChinaSupport } from 'src/modules/components/connected/UserGoogleBaseMap'
import PathComponents from 'src/modules/map-view/map/path-components/path-components'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny } from 'src/types'
import { MapTypeId, type MapsExtended } from 'src/types/extended/google-maps'
import MapControls from 'src/util-components/map/shared/map-controls'
import type { MapProviderMetaData } from 'src/util-components/map/shared/types'
import { wgs2gcjIfInChinaMainland } from 'src/util-functions/china-map-utils'

import {
  createMapOptions,
  generateClusters,
  latLng2World,
  screen2World,
  world2LatLng,
  world2Screen,
} from 'cartrack-utils'
import { renderLandmarkMarker, VehicleComponents } from 'cartrack-ui-kit'

type VehiclePoints = ReadonlyArray<
  Points[number] & {
    vehicle: Props['vehicles'][number]
  }
>

const mapVehicleCluster = (c: Cluster<VehiclePoints>) => ({
  id: `${c.numPoints}_${c.points[0].vehicle.id}`,
  vehiclePoints: c.points,
  lat: c.wy,
  lng: c.wx,
  numPoints: c.numPoints,
})

const generateVehiclesClusterData = (
  props: Pick<Props, 'vehicles' | 'currentLayerVisibility'> & {
    mapState: ChangeEventValue
  },
) =>
  generateClusters(props, {
    itemName: 'vehicle',
    accessor: 'vehicles',
    mapCluster: mapVehicleCluster,
  })

type OwnProps = {
  mapProviderMetaData: MapProviderMetaData
  editing: boolean
  color: string
  name: string
  radius: number
  history: H.History<{
    mapAssets?: Record<string, FixMeAny>
    mapState?: Record<string, FixMeAny>
  }>
  onChange: (point: { lat: number; lng: number }) => void
  onDblClick?: (props: {
    lat: number
    lng: number
    bounds: google.maps.LatLngBounds | undefined
  }) => void
  focusedVehicle?: Record<string, FixMeAny>
  initialZoom: number
  handleMapChange: (obj: ChangeEventValue) => void
  onChangeMapCenterZoom: (lat: number, lng: number, zoom: number) => void
  mapState:
    | ChangeEventValue
    | SetOptional<ChangeEventValue, 'bounds' | 'marginBounds' | 'size'>
  pointInWgs84: { lat: number; lng: number } | null
  fullscreen: boolean
  minZoom: number | undefined
  maxZoom: number | undefined
  mapTypeId: google.maps.MapTypeId
  onChangeMapTypeId: (mapTypeId: google.maps.MapTypeId) => void
}

type Props = ReturnType<typeof mapStateToProps> & typeof actionCreators & OwnProps

type State = {
  mapObject: MapsExtended.MapObject | undefined
  mouse: { x: number; y: number }
  editingPoint: boolean
  type: null | 'polygon'
  dragging: boolean
  vehicleClusters: ReturnType<typeof generateVehiclesClusterData>[0]
  vehicleClustersFunc: ReturnType<typeof generateVehiclesClusterData>[1] | null
  mapContainerRef: RefObject<HTMLDivElement>
}

class MapCreate extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)

    this.state = {
      mouse: { x: 0, y: 0 },
      type: props.name ? null : 'polygon',
      editingPoint: this.props.editing,
      dragging: false,
      mapObject: undefined,
      vehicleClustersFunc: null,
      vehicleClusters: [],
      mapContainerRef: createRef<HTMLDivElement>(),
    }
  }

  componentDidMount() {
    const timelineTripEvents = this.props.history?.location?.state?.mapAssets
      ? this.props.timelineTripEvents
      : undefined
    if (timelineTripEvents) {
      this.props.doSetLayerVisibility({ showTripLines: true })
    }
  }

  dblClicked: boolean | undefined = undefined

  // Commenting out if case something not handled correctly
  // componentWillReceiveProps(nextProps) {
  //   if (
  //     !shallowEqual(this.state.mapState, nextProps.center) ||
  //     this.state.mapState.zoom !== nextProps.zoom
  //   ) {
  //     const {
  //       center: { lat, lng },
  //       zoom
  //     } = nextProps
  //     this.changeMapCenterZoom(lat, lng, zoom)
  //   }
  // if (this.props.selectedPlace !== nextProps.selectedPlace) {
  //   const { lat, lng } = nextProps.selectedPlace
  //   this.changeMapCenterZoom(lat, lng, 16)
  //   // Only move landmarks for now...
  //   if (this.props.type === 'landmark') {
  //     const points = [{ lat, lng }]
  //     this.setState({ points })
  //     this.props.onChange(points)
  //   }
  // }
  // }

  generateVehicleClusters = () => {
    // get vehicles if create/edit geofence from context menu
    const props = {
      vehicles: this.props.history?.location?.state?.mapAssets
        ? this.props.vehicles
        : [],
      currentLayerVisibility: this.props.currentLayerVisibility,
      mapState: this.props.mapState as ChangeEventValue,
    }
    const [vehicleClusters, vehicleClustersFunc] = generateVehiclesClusterData(props)

    this.setState({
      vehicleClusters,
      vehicleClustersFunc: vehicleClustersFunc,
    })
  }

  setDblClickTimer() {
    this.dblClicked = true
    window.setTimeout(() => (this.dblClicked = false), 400)
  }

  handleChildClick = () => {
    if (!this.props.editing) return

    if (this.state.mapObject) {
      this.state.mapObject.map.setOptions({ draggable: false })
    }

    this.setState({
      dragging: !this.state.dragging,
    })
  }

  handleGoogleAPILoaded: MapsExtended.OnGoogleApiLoaded = (mapObject) => {
    if (!this.state.vehicleClustersFunc) {
      this.generateVehicleClusters()
    }

    this.setState({ mapObject })
  }

  handleMapChange = (obj: ChangeEventValue) => {
    const { vehicleClustersFunc } = this.state
    this.props.handleMapChange(obj)
    this.setState({
      vehicleClusters: vehicleClustersFunc
        ? vehicleClustersFunc(obj).map((c) => mapVehicleCluster(c))
        : [],
    })
  }

  handleMapCommand = ({ lat, lng }: { lat: number; lng: number }) => {
    if (!this.state.editingPoint) return

    this.props.onChange({ lat, lng })
    this.setState({ editingPoint: false })
  }

  // NOTE: coord can be gcj or wgs standard
  handleMapCommandDelayed = (coord: { lat: number; lng: number } | ClickEventValue) => {
    if (this.dblClicked === true) {
      this.setState({
        editingPoint: false,
        type: null,
      })

      if (this.props.onDblClick && this.state.mapObject) {
        this.props.onDblClick({
          lat: coord.lat,
          lng: coord.lng,
          bounds: this.state.mapObject.map.getBounds(),
        })
      }
    } else {
      this.setDblClickTimer()
      this.handleMapCommand(coord)
    }
  }

  handleMouseMove: MouseEventHandler<HTMLDivElement> = (event) => {
    if (!this.props.editing) return

    const { top, left } = event.currentTarget.getBoundingClientRect()
    let x = event.clientX - left
    let y = event.clientY - top

    if (this.props.mapState.bounds === undefined) {
      return
    }

    const { x: mapLeft, y: mapTop } = world2Screen(
      latLng2World(this.props.mapState.bounds.nw),
      this.props.mapState.zoom,
    )

    y += mapTop

    const widthMinusLeft =
      world2Screen({ x: 1, y: 0 }, this.props.mapState.zoom).x - mapLeft
    if (x >= widthMinusLeft) x -= widthMinusLeft
    else x += mapLeft

    const coords = screen2World({ x, y }, this.props.mapState.zoom)

    if (this.state.dragging && this.props.pointInWgs84) {
      const dx = coords.x - this.state.mouse.x
      const dy = coords.y - this.state.mouse.y

      // NOTE: it's in google map, the position should be converted to gcj when in China mainland with roadmap
      const point =
        this.props.mapTypeId === MapTypeId.ROADMAP
          ? wgs2gcjIfInChinaMainland(this.props.pointInWgs84)
          : this.props.pointInWgs84

      const { x, y } = latLng2World(point)
      const newPoint = world2LatLng({ x: x + dx, y: y + dy })

      this.props.onChange(newPoint)
    }

    this.setState({ mouse: coords })
  }

  handleMouseUp = () => {
    if (this.state.mapObject?.map)
      this.state.mapObject.map.setOptions({ draggable: true })
    if (this.state.type === null) {
      this.setState({
        editingPoint: false,
        dragging: false,
      })
    }
  }

  renderVehicleClusters = () => {
    const showVehicles = this.props.history?.location?.state?.mapAssets?.showVehicles
    const {
      currentLayerVisibility: { livePositionLabels },
      useVehicleIconColor,
    } = this.props
    const { mapObject } = this.state

    return (
      mapObject &&
      showVehicles && (
        <VehicleComponents
          mapTypeId={this.props.mapTypeId}
          map={mapObject.map}
          vehicleClusters={this.state.vehicleClusters}
          useVehicleIconColor={useVehicleIconColor}
          showLabels={livePositionLabels}
          onVehicleClick={() => {}}
          onVehicleClusterClick={() => {}}
          onVehicleContextMenuClick={() => {}}
        />
      )
    )
  }

  handleOtherChildClick = () => {
    const args = world2LatLng(this.state.mouse)
    this.handleMapCommandDelayed(args)
  }

  renderPath = () => {
    // get timeline trip events if create/edit geofence from context menu
    const timelineTripEvents = this.props.history?.location?.state?.mapAssets
      ? this.props.timelineTripEvents
      : undefined
    const { initialZoom, currentLayerVisibility, mapState, mapTypeId } = this.props
    const { mapObject } = this.state

    return (
      timelineTripEvents &&
      mapObject &&
      mapState && (
        <PathComponents
          mapProps={{ mapApiProviderId: MapApiProvider.GOOGLE, mapObject, mapTypeId }}
          currentLayerVisibility={currentLayerVisibility}
          zoom={initialZoom}
          events={timelineTripEvents.type === 'daily' ? timelineTripEvents.events : []}
          focusedVehicle={
            this.props.history?.location?.state?.mapAssets?.focusedVehicle ?? null
          }
          onEventMarkerClick={undefined}
          selectedTripEvents={[]}
          useSVREvents={false}
          timelineTripsMarkerData={undefined}
        />
      )
    )
  }

  render() {
    const {
      pointInWgs84,
      editing: editView,
      color,
      mapState,
      mapProviderMetaData,
      minZoom,
      maxZoom,
      onChangeMapTypeId,
      mapTypeId,
      radius,
    } = this.props
    const { editingPoint, dragging, mapObject } = this.state

    return (
      <div
        className={`MapDetailView-map ${editView && editingPoint ? 'is-editing' : ''}`}
        onMouseMove={mapState.bounds && this.handleMouseMove}
        onMouseUp={this.handleMouseUp}
        ref={this.state.mapContainerRef}
      >
        <MapThemeContextProvider mapTypeId={mapTypeId}>
          <UserGoogleBaseMapWithChinaSupport
            mapTypeId={mapTypeId}
            bootstrapURLKeys={{ libraries: ['places'] }}
            onGoogleApiLoaded={this.handleGoogleAPILoaded}
            yesIWantToUseGoogleMapApiInternals
            center={mapState.center}
            zoom={mapState.zoom}
            options={(maps) => ({
              ...createMapOptions(maps),
              disableDoubleClickZoom: true,
              streetViewControl: false,
            })}
            onChange={this.handleMapChange}
            onClick={this.handleMapCommandDelayed}
            resetBoundsOnResize
            onChildClick={this.handleOtherChildClick}
          >
            {pointInWgs84 &&
              renderLandmarkMarker({
                key: 'landmark',
                latInWgs84: pointInWgs84.lat,
                lngInWgs84: pointInWgs84.lng,
                color,
                drawRadius: {
                  radius,
                  realZoom: mapObject ? (mapObject.map.getZoom() ?? 16) : 16,
                },
                onLandmarkClick: this.handleChildClick,
                dragging: dragging,
                scale: 3,
              })}
            {this.renderPath()}
            {this.renderVehicleClusters()}
          </UserGoogleBaseMapWithChinaSupport>
        </MapThemeContextProvider>
        {mapObject && (
          <MapControls
            mapProviderSelectionUI={
              mapProviderMetaData.selectionUI === 'do_not_show'
                ? 'do_not_show'
                : {
                    currentMapProvider: mapProviderMetaData.currentMapProvider,
                    onClick: mapProviderMetaData.selectionUI.onClick,
                  }
            }
            mapTypeId={mapTypeId}
            onChangeMapTypeId={onChangeMapTypeId}
            onChangeMapCenterZoom={this.props.onChangeMapCenterZoom}
            center={mapState.center}
            zoom={mapState.zoom}
            minZoom={minZoom}
            maxZoom={maxZoom}
            fullscreenUI={{
              isFullscreen: this.props.fullscreen,
              onEnterButtonClick: () =>
                this.state.mapContainerRef.current?.requestFullscreen(),
            }}
          />
        )}
      </div>
    )
  }
}

function mapStateToProps(state: AppState) {
  const { vehicleDisplayName, useVehicleIconColor } = getPreferences(state)

  return {
    vehicleDisplayName,
    useVehicleIconColor,
    currentLayerVisibility: getCurrentLayerVisibility(state),
    timelineTripEvents: getTimelineEventsByActivityAndMapType(state),
    vehicles: getMapVehicles(state),
  }
}

const actionCreators = { doSetLayerVisibility: setLayerVisibility }

export default connect(mapStateToProps, actionCreators)(MapCreate)
