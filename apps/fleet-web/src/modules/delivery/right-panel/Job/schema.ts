/* eslint-disable sonarjs/concise-regex */
/* eslint-disable sonarjs/slow-regex */
import { isEmpty } from 'lodash'
import { parsePhoneNumber } from 'react-phone-number-input/input'
import { match, P } from 'ts-pattern'
import * as Yup from 'yup'

import {
  CONVERSION_THRESHOLD,
  LENGTH_OPTIONS,
  ONLY_NUMBER_REGEX,
  SCHEDULE_TYPE_ID,
} from 'src/modules/delivery/utils/constants'
import { LatLngRegex } from 'src/modules/delivery/utils/regex'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'

//schema
import { validateMaxWeight } from '../Driver/form/schema'

const fetchMetricOption = (dimensionId: number) =>
  LENGTH_OPTIONS.find((metric) => metric.value === dimensionId)

const StopsSchema = Yup.array().of(
  Yup.object({
    customer: Yup.object()
      .required('Required')
      .shape({
        customerName: Yup.string().required('Required'),
      })
      .nullable(),
    contactNumber: Yup.string()
      .matches(
        ONLY_NUMBER_REGEX,
        ctIntl.unsafe_formatMessage({
          id: 'Invalid',
        }),
      )
      .when('contactCode', (contactCode, schema, objValue) =>
        schema.test(
          'is-valid-phone-number',
          ctIntl.unsafe_formatMessage({ id: 'Invalid phone number' }),
          (contactNumber: string) => {
            if (!objValue.value) return true

            let code: number | string
            if (contactCode?.label) {
              code = contactCode.label
            } else {
              code = `+${contactCode}`
            }
            const option = parsePhoneNumber(`${code}${contactNumber}`)
            return !!option?.country
          },
        ),
      ),
    country: Yup.string().required('Required').nullable(),
    email: Yup.string().email('Invalid email').nullable(),
    latLng: Yup.string().matches(LatLngRegex),
    duration: Yup.number().moreThan(0, 'Duration must be greater than 0'),
    deliveryWindowError: Yup.string()
      .nullable()
      .matches(
        /^$/,
        ctIntl.unsafe_formatMessage({
          id: 'Time conflict',
        }),
      ),
    deliveryWindowDurationError: Yup.string()
      .nullable()
      .matches(
        /^$/,
        ctIntl.unsafe_formatMessage({
          id: 'Invalid time window',
        }),
      ),
  }),
)

const ItemsSchema = Yup.array().of(
  Yup.object({
    quantity: Yup.number()
      .typeError('delivery.stopItem.quantityMustBeNumber')
      .moreThan(0, 'delivery.stopItem.quantityGreaterThanZero')
      .required('Required'),
    weight: Yup.string()
      .matches(/(^$|^[0-9]*\.?[0-9]*$)/, 'Invalid') //Regex to accept numbers and 1 dot
      .test('checkWholeNumberLength', 'Invalid', validateMaxWeight),
    dimensionId: Yup.number(),
    length: Yup.string()
      .nullable()
      .matches(/(^$|^[0-9]*\.?[0-9]*$)/, 'Invalid') //Regex to accept numbers and 1 dot
      .when('dimensionId', (dimensionId: number, schema: any) => {
        const threshold =
          dimensionId && dimensionId > 0
            ? CONVERSION_THRESHOLD[dimensionId as keyof typeof CONVERSION_THRESHOLD]
                .LENGTH
            : null
        return schema.test({
          test: (length: any) => {
            if (!length) return true
            return threshold ? length <= threshold : false
          },
          message: `Value should not be greater than ${threshold}${
            fetchMetricOption(dimensionId)?.label
          }`,
        })
      })
      .test(
        'checkWholeNumberLength',
        'Invalid',
        (length) =>
          length && length !== undefined ? length.split('.')[0].length <= 6 : true, //Limit the whole number digit length to 6
      ),
    width: Yup.string()
      .nullable()
      .matches(/(^$|^[0-9]*\.?[0-9]*$)/, 'Invalid') //Regex to accept numbers and 1 dot
      .when('dimensionId', (dimensionId: number, schema: any) => {
        const threshold =
          dimensionId && dimensionId > 0
            ? CONVERSION_THRESHOLD[dimensionId as keyof typeof CONVERSION_THRESHOLD]
                .WIDTH
            : null
        return schema.test({
          test: (width: any) => {
            if (!width) return true
            return threshold ? width <= threshold : false
          },
          message: `Value should not be greater than ${threshold}${
            fetchMetricOption(dimensionId)?.label
          }`,
        })
      })
      .test(
        'checkWholeNumberLength',
        'Invalid',
        (width) =>
          width && width !== undefined ? width.split('.')[0].length <= 6 : true, //Limit the whole number digit length to 6
      ),
    height: Yup.string()
      .nullable()
      .matches(/(^$|^[0-9]*\.?[0-9]*$)/, 'Invalid') //Regex to accept numbers and 1 dot
      .when('dimensionId', (dimensionId: number, schema: any) => {
        const threshold =
          dimensionId && dimensionId > 0
            ? CONVERSION_THRESHOLD[dimensionId as keyof typeof CONVERSION_THRESHOLD]
                .HEIGHT
            : null
        return schema.test({
          test: (height: any) => {
            if (!height) return true
            return threshold ? height <= threshold : false
          },
          message: `Value should not be greater than ${threshold}${
            fetchMetricOption(dimensionId)?.label
          }`,
        })
      })
      .test(
        'checkWholeNumberLength',
        'Invalid',
        (height) =>
          height && height !== undefined ? height.split('.')[0].length <= 6 : true, //Limit the whole number digit length to 6
      ),
  }),
)

const JobFormHeader = (isEditAndDateUntouched?: boolean) => ({
  scheduleTypeList: Yup.object()
    .shape({
      id: Yup.number().required('Required'),
      value: Yup.string().required('Required'),
      label: Yup.string().required('Required'),
    })
    .required('Required')
    .nullable(),
  scheduledDeliveryTs: Yup.string()
    .when('scheduleTypeList', {
      is: (scheduleTypeList) =>
        !isEmpty(scheduleTypeList) &&
        scheduleTypeList.id &&
        [SCHEDULE_TYPE_ID.ASAP, SCHEDULE_TYPE_ID.SCHEDULE].includes(
          scheduleTypeList.id,
        ),
      then: (schema: Yup.NullableArraySchema<Yup.StringSchema>) =>
        schema.test(
          'dateTimeIsSameOrAfterCurrent',
          ctIntl.unsafe_formatMessage({
            id: 'Cannot select a passed date/time',
          }),
          (scheduledDeliveryTs) =>
            match({ isEditAndDateUntouched, scheduledDeliveryTs })
              .with(
                { scheduledDeliveryTs: P.string, isEditAndDateUntouched: true },
                ({ scheduledDeliveryTs }) =>
                  DeliveryDateTime.fromJSDate(new Date(scheduledDeliveryTs)).startOf(
                    'day',
                  ) >= DeliveryDateTime.now().startOf('day'),
              )
              .with(
                { scheduledDeliveryTs: P.string },
                ({ scheduledDeliveryTs }) =>
                  DeliveryDateTime.fromJSDate(new Date(scheduledDeliveryTs)) >
                  DeliveryDateTime.now(),
              )
              .otherwise(() => true),
        ),
    })
    .nullable(),
  hasCustomPriority: Yup.boolean(),
  priorityInput: Yup.number().when('hasCustomPriority', {
    is: true,
    then: Yup.number()
      .required('Required')
      .min(
        1,
        ctIntl.unsafe_formatMessage(
          {
            id: 'Priority must be greater than {minValue}',
          },
          {
            values: {
              minValue: 0,
            },
          },
        ),
      )
      .max(
        10000,
        ctIntl.unsafe_formatMessage(
          {
            id: 'Priority must be less than and equal to {maxValue}',
          },
          {
            values: {
              maxValue: 10000,
            },
          },
        ),
      ),
  }),
})

export const MultiStopJobSchema = (isEditAndDateUntouched?: boolean) =>
  Yup.object().shape({
    ...JobFormHeader(isEditAndDateUntouched),
    jobs: Yup.array().of(
      Yup.object({
        stops: StopsSchema,
        items: ItemsSchema,
      }),
    ),
  })
