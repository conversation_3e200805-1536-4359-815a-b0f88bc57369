import lodash from 'lodash'

import { JOB_STOP_TYPE_ID, TODO_TYPE_ID } from 'src/modules/delivery/utils/constants'
import { Countries } from 'src/modules/delivery/utils/countries'
import { setScheduledDeliveryTs } from 'src/modules/delivery/utils/helpers'

import { validateGPSLatitudeLongitudeField } from '../../field-validation'
import { getDeliveryWindows, getPriority, jobTypeVerify } from '../helper'

const ZERO_STRING = '0'
const NAN_STRING = 'NaN'

const getItemTodosInfo = (input: string) => {
  if (!input.trim()) return []

  // handle cases like 125
  if (/^\d+(,\d+)*$/.test(input.trim())) {
    return input
      .trim()
      .split('')
      .map((n) => `${n}`)
  }

  // handle cases like (1, aa)(2, bb)
  const regex = /\((\d+),\s*([^)]{1,100})\)/g

  const result: Array<string> = []
  let match: RegExpExecArray | null

  while ((match = regex.exec(input)) !== null) {
    const todoTypeId = match[1].trim()
    const description = match[2].trim()
    result.push(`${todoTypeId}|${description}`)
  }

  return result
}

const generateStopsTodos = (
  arr: Array<JobImporterInput.ParsedObject> = [],
  taggedArray: Array<JobImporterInput.ParsedObject> = [],
  isPickupRow = false,
) => {
  const stopTodos = arr.find((item) => item.stopTodos)?.stopTodos || ''
  const hasPriorityStopTodos = stopTodos !== ''
  const priorityTodoCell = taggedArray.find(
    (item) => item['stopTodos']?.split('|')[0] === stopTodos,
  )

  const customStopSignature = taggedArray.flatMap((item) =>
    item['stopSignature'].split('|')[0]
      ? [
          {
            description: item['stopSignature'].split('|')[0],
            cellLocation: item['stopSignature'].split('|')[1],
          },
        ]
      : [],
  )
  const customStopPod = taggedArray.flatMap((item) =>
    item['stopPod'].split('|')[0]
      ? [
          {
            description: item['stopPod'].split('|')[0],
            cellLocation: item['stopPod'].split('|')[1],
          },
        ]
      : [],
  )
  const customStopNote = taggedArray.flatMap((item) =>
    item['stopNote'].split('|')[0]
      ? [
          {
            description: item['stopNote'].split('|')[0],
            cellLocation: item['stopNote'].split('|')[1],
          },
        ]
      : [],
  )

  //This will remove SCAN TO ATTACH todos on stop level
  const toTrimmedTodos = new Set([
    ZERO_STRING,
    NAN_STRING,
    TODO_TYPE_ID.SCAN_TO_ATTACH.toString(),
  ])

  const priorityTodos = getItemTodosInfo(stopTodos.trim())
    .map((todo) => ({
      todoTypeId: +todo.split('|')[0],
      cellLocation: priorityTodoCell
        ? priorityTodoCell['stopTodos']?.split('|')[1]
        : '',
      isRequired: true,
      description: todo.split('|')[1] || '',
    }))
    .filter((todo) => !toTrimmedTodos.has(todo.todoTypeId.toString()))

  return hasPriorityStopTodos || isPickupRow
    ? priorityTodos
    : [
        ...customStopSignature.map(({ description, cellLocation }) => ({
          todoTypeId: TODO_TYPE_ID.SIGNATURE,
          cellLocation,
          isRequired: true,
          description,
        })),
        ...customStopPod.map(({ description, cellLocation }) => ({
          todoTypeId: TODO_TYPE_ID.POD,
          cellLocation,
          isRequired: true,
          description,
        })),
        ...customStopNote.map(({ description, cellLocation }) => ({
          todoTypeId: TODO_TYPE_ID.NOTE,
          cellLocation,
          isRequired: true,
          description,
        })),
      ]
}

const compare = (
  a: JobImporterInput.ParsedObject,
  b: JobImporterInput.ParsedObject,
) => {
  const onlyNumbersRegexTest = /^\d+$/
  const stopNoA: number =
    a?.stopNo && onlyNumbersRegexTest.test(a.stopNo) ? +a?.stopNo : -1
  const stopNoB: number =
    b?.stopNo && onlyNumbersRegexTest.test(b.stopNo) ? +b?.stopNo : -1
  if (stopNoA < stopNoB) {
    return -1
  } else if (stopNoA > stopNoB) {
    return 1
  } else {
    return 0
  }
}

const taggedCompare = (
  a: JobImporterInput.ParsedObject,
  b: JobImporterInput.ParsedObject,
) => {
  const onlyNumbersRegexTest = /^\d+$/
  const stopNoA: number =
    a?.stopNo && onlyNumbersRegexTest.test(a.stopNo?.split('|')[0])
      ? +a.stopNo?.split('|')[0]
      : -1
  const stopNoB: number =
    b?.stopNo && onlyNumbersRegexTest.test(b.stopNo?.split('|')[0])
      ? +b.stopNo?.split('|')[0]
      : -1
  if (stopNoA < stopNoB) {
    return -1
  } else if (stopNoA > stopNoB) {
    return 1
  } else {
    return 0
  }
}

export const generateStops = (
  itemArray: Array<JobImporterInput.ParsedObject> = [],
  taggedArray: Array<JobImporterInput.ParsedObject> = [],
  // phonePrefix: string,
): {
  stops?: Array<JobImporterOutPut.Stop>
  multiStops?: Array<Array<JobImporterOutPut.Stop>>
  stopErrors: Array<JobImporterError.Error | undefined>
} => {
  const jobPriority = getPriority(itemArray.find((item) => item.priority)?.priority)

  const pickUpArr = itemArray.filter((item) => {
    const stopType = item?.stopType?.toLowerCase().trim()
    return ['p', 'pickup'].includes(stopType)
  })
  const dropOffArr = itemArray.filter((item) => {
    const stopType = item?.stopType?.toLowerCase().trim()
    return !['p', 'pickup'].includes(stopType)
  })

  let taggedPickUpArr = taggedArray.filter((item) => {
    const stopType = item?.stopType.split('|')[0]?.toLowerCase().trim()
    return ['p', 'pickup'].includes(stopType)
  })
  let taggedDropOffArr = taggedArray.filter((item) => {
    const stopType = item?.stopType.split('|')[0]?.toLowerCase().trim()
    return !['p', 'pickup'].includes(stopType)
  })

  const getActualStops = (key: string, value: Array<JobImporterInput.ParsedObject>) => {
    if (key === '') {
      return [...value]
    } else {
      const stopAllValues: JobImporterInput.ParsedObject = lodash.clone(value[0])
      for (const key of Object.keys(value[0])) {
        stopAllValues[key] = value?.find((n) => n[key])?.[key]
      }
      const rowLocation = value
        .map((stop) => stop?.rowLocation)
        .join(',')
        .toString()
      stopAllValues['rowLocation'] = rowLocation
      return [stopAllValues]
    }
  }

  const stopNoGroupedPickup = lodash.groupBy(pickUpArr, (n) =>
    n.orderNumber.trim()?.toLowerCase(),
  )
  const stopNoGroupedDropoff = lodash.groupBy(dropOffArr, (n) =>
    (n?.stopNo || '').trim(),
  )
  let actualPickupStops = Object.entries(stopNoGroupedPickup).flatMap(([key, value]) =>
    getActualStops(key, value),
  )
  let actualDropoffStops = Object.entries(stopNoGroupedDropoff).flatMap(
    ([key, value]) => getActualStops(key, value),
  )

  const errors = [...validateGPSLatitudeLongitudeField(taggedArray)].filter(Boolean)

  //If pickup stops length > 0 BUT dropoff stops length is 0, treat is as SINGLE
  if (actualPickupStops.length > 0 && actualDropoffStops.length === 0) {
    actualDropoffStops = [...actualPickupStops]
    actualPickupStops = []
    taggedDropOffArr = [...taggedPickUpArr]
    taggedPickUpArr = []
  }

  const {
    isSingleJobPickupDropOff,
    isMultiJobPickupDropOff,
    isSingleStopJob,
    isMultiJobSingleStop,
  } = jobTypeVerify(actualPickupStops, actualDropoffStops)

  const uniquePickUp = actualPickupStops[0]
  const uniqueDropOff = actualDropoffStops[0]

  const pickUpCountry = Countries.find(
    (item) => item.country_code === uniquePickUp?.countryCode?.toUpperCase(),
  )
  const droppOffCountry = Countries.find(
    (item) => item.country_code === uniqueDropOff?.countryCode?.toUpperCase(),
  )

  const getCountryId = (cc: string) =>
    Countries.find(
      (item) => item.country_code === cc.toUpperCase() || item.country_id === cc,
    )?.country_id || ''

  if (isSingleJobPickupDropOff) {
    //  This is the logic where Priority based Customer Info providing ,
    // CustomerID --first Priority -> GPS ->
    const uniquePickUpStopEmail = uniquePickUp?.email
      ? { email: uniquePickUp?.email }
      : ''
    const customerInfoPickUp = !uniquePickUp?.customerId
      ? {
          customerName: uniquePickUp?.customerName,
          ...uniquePickUpStopEmail,
          ...(uniquePickUp?.phoneCountryCode?.toString().replaceAll(/\D/g, '') && {
            contactCode: uniquePickUp.phoneCountryCode.toString().replaceAll(/\D/g, ''),
          }),
          ...(uniquePickUp?.phone?.toString().replaceAll(/\D/g, '') && {
            contactNumber: uniquePickUp.phone.toString().replaceAll(/\D/g, ''),
          }),
          ...((pickup) => {
            if (pickup?.gps) {
              return {
                latitude: pickup?.gps.split(',')[0],
                longitude: pickup?.gps.split(',')[1],
              } // Latitude
            } else if (pickup?.lat && pickup?.lng) {
              return {
                latitude: pickup?.lat,
                longitude: pickup?.lng,
              }
            } else {
              return {
                addressLine1: pickup?.addressLine1,
                addressLine2: pickup?.addressLine2,
                postalCode: pickup?.postalCode || null,
                countryId: pickUpCountry?.country_id,
              }
            }
          })(uniquePickUp),
        }
      : {}

    const uniquePickUpCustomerId = uniquePickUp?.customerId
      ? { customerId: uniquePickUp?.customerId }
      : ''
    const uniqueDropOffCustomerId = uniqueDropOff?.customerId
      ? { customerId: uniqueDropOff?.customerId }
      : ''
    const uniqueDropOffStopEmail = uniqueDropOff?.email
      ? { email: uniqueDropOff?.email }
      : ''
    const customerInfoDropOff = !uniqueDropOff?.customerId
      ? {
          customerName: uniqueDropOff?.customerName,
          ...uniqueDropOffStopEmail,
          ...(uniqueDropOff?.phoneCountryCode?.toString().replaceAll(/\D/g, '') && {
            contactCode: uniqueDropOff.phoneCountryCode
              .toString()
              .replaceAll(/\D/g, ''),
          }),
          ...(uniqueDropOff?.phone?.toString().replaceAll(/\D/g, '') && {
            contactNumber: uniqueDropOff.phone.toString().replaceAll(/\D/g, ''),
          }),
          ...((dropOff) => {
            if (dropOff?.gps) {
              return {
                latitude: dropOff?.gps.split(',')[0],
                longitude: dropOff?.gps.split(',')[1],
              } // Latitude
            } else if (dropOff?.lat && dropOff?.lng) {
              return {
                latitude: dropOff?.lat,
                longitude: dropOff?.lng,
              }
            } else {
              return {
                addressLine1: dropOff?.addressLine1,
                addressLine2: dropOff?.addressLine2,
                postalCode: dropOff?.postalCode || null,
                countryId: droppOffCountry?.country_id,
              }
            }
          })(uniqueDropOff),
        }
      : {}
    return {
      stops: [
        {
          ...uniquePickUpCustomerId,
          stopTypeId: JOB_STOP_TYPE_ID.PICKUP,
          scheduledDeliveryTs:
            uniquePickUp?.sendDateTime && uniquePickUp?.sendDateTime.trim() !== ''
              ? setScheduledDeliveryTs(uniquePickUp?.sendDateTime)
              : null,
          rowLocation: uniquePickUp?.rowLocation,
          ...customerInfoPickUp,

          note: uniquePickUp?.note,
          priority: jobPriority,
          duration: Number(uniquePickUp?.duration) || 5,
          ...(uniquePickUp?.timeWindow?.trim() && {
            deliveryWindows: getDeliveryWindows(uniquePickUp?.timeWindow),
          }),
          // saveToAddressBook: !uniquePickUp?.customerId ? true : false,
          todos: generateStopsTodos(actualPickupStops, taggedPickUpArr, true),
        },
        {
          ...uniqueDropOffCustomerId,
          stopTypeId: JOB_STOP_TYPE_ID.DROPOFF,
          scheduledDeliveryTs:
            uniqueDropOff?.sendDateTime && uniqueDropOff?.sendDateTime.trim() !== ''
              ? setScheduledDeliveryTs(uniqueDropOff?.sendDateTime)
              : null,
          rowLocation: uniqueDropOff?.rowLocation,
          ...customerInfoDropOff,
          // saveToAddressBook: !uniqueDropOff?.customerId ? true : false,
          note: uniqueDropOff?.note,
          priority: jobPriority,
          duration: Number(uniqueDropOff?.duration) || 5,
          ...(uniqueDropOff?.timeWindow?.trim() && {
            deliveryWindows: getDeliveryWindows(uniqueDropOff?.timeWindow),
          }),
          todos: generateStopsTodos(actualDropoffStops, taggedDropOffArr),
        },
      ],
      stopErrors: errors,
    }
  }

  if (isSingleStopJob) {
    const customerId = uniqueDropOff?.customerId
      ? { customerId: uniqueDropOff?.customerId }
      : ''
    const uniqueDropOffStopEmail = uniqueDropOff?.email
      ? { email: uniqueDropOff?.email }
      : ''
    const customerInfoDropOff = !uniqueDropOff?.customerId
      ? {
          customerName: uniqueDropOff?.customerName,
          ...uniqueDropOffStopEmail,
          ...(uniqueDropOff?.phoneCountryCode?.toString().replaceAll(/\D/g, '') && {
            contactCode: uniqueDropOff.phoneCountryCode
              .toString()
              .replaceAll(/\D/g, ''),
          }),
          ...(uniqueDropOff?.phone?.toString().replaceAll(/\D/g, '') && {
            contactNumber: uniqueDropOff.phone.toString().replaceAll(/\D/g, ''),
          }),
          ...((dropOff) => {
            if (dropOff?.gps) {
              return {
                latitude: dropOff?.gps.split(',')[0],
                longitude: dropOff?.gps.split(',')[1],
              } // Latitude
            } else if (dropOff?.lat && dropOff?.lng) {
              return {
                latitude: dropOff?.lat,
                longitude: dropOff?.lng,
              }
            } else {
              return {
                addressLine1: dropOff?.addressLine1,
                addressLine2: dropOff?.addressLine2,
                postalCode: dropOff?.postalCode || null,
                countryId: droppOffCountry?.country_id,
              }
            }
          })(uniqueDropOff),
        }
      : {}
    return {
      stops: [
        {
          ...customerId,
          stopTypeId: JOB_STOP_TYPE_ID.SINGLE,
          scheduledDeliveryTs:
            uniqueDropOff?.sendDateTime && uniqueDropOff?.sendDateTime.trim() !== ''
              ? setScheduledDeliveryTs(uniqueDropOff?.sendDateTime)
              : null,
          rowLocation: taggedDropOffArr
            .map((item) => item.orderNumber.split('|')[1].split(',')[1])
            .toString(),
          ...customerInfoDropOff,
          note: uniqueDropOff?.note,
          priority: jobPriority,
          duration: Number(uniqueDropOff?.duration) || 5,
          ...(uniqueDropOff?.timeWindow?.trim() && {
            deliveryWindows: getDeliveryWindows(uniqueDropOff?.timeWindow),
          }),
          // saveToAddressBook: !uniqueDropOff?.customerId ? true : false,
          todos: generateStopsTodos(actualDropoffStops, taggedDropOffArr),
        },
      ],
      stopErrors: errors,
    }
  }

  if (isMultiJobSingleStop) {
    const sortedUntaggedStops = actualDropoffStops.sort(compare)
    const sortedTaggedStops = taggedDropOffArr.sort(taggedCompare)
    const stops = {
      multiStops: sortedUntaggedStops.map((stop, stopIndex) => {
        const customerId = stop.customerId ? { customerId: stop.customerId } : ''
        const stopEmail = stop.email ? { email: stop.email } : ''
        const customerInfoDropOff = !stop.customerId
          ? {
              customerName: stop.customerName,
              ...stopEmail,
              ...(stop.phoneCountryCode && {
                contactCode: stop.phoneCountryCode.toString().replaceAll(/\D/g, ''),
              }),
              ...(stop.phone && {
                contactNumber: stop.phone.toString().replaceAll(/\D/g, ''),
              }),
              ...((pickup) => {
                if (pickup.gps) {
                  return {
                    latitude: pickup.gps.split(',')[0],
                    longitude: pickup.gps.split(',')[1],
                  } // Latitude
                } else if (pickup?.lat && pickup?.lng) {
                  return {
                    latitude: pickup?.lat,
                    longitude: pickup?.lng,
                  }
                } else {
                  return {
                    addressLine1: pickup.addressLine1 || '',
                    addressLine2: pickup.addressLine2 || '',
                    postalCode: pickup.postalCode || null,
                    countryId:
                      (pickup.countryCode && getCountryId(pickup.countryCode)) || '',
                  }
                }
              })(stop),
            }
          : {}
        return [
          {
            ...customerId,
            stopTypeId: JOB_STOP_TYPE_ID.SINGLE,
            scheduledDeliveryTs:
              stop?.sendDateTime && stop?.sendDateTime.trim() !== ''
                ? setScheduledDeliveryTs(stop?.sendDateTime)
                : null,
            rowLocation: stop.rowLocation,
            ...customerInfoDropOff,
            note: stop?.note,
            priority: jobPriority,
            duration: Number(stop?.duration) || 5,
            ...(stop?.timeWindow?.trim() && {
              deliveryWindows: getDeliveryWindows(stop?.timeWindow),
            }),
            // saveToAddressBook: !stop.customerId ? true : false,
            todos: generateStopsTodos([stop], [sortedTaggedStops[stopIndex]]),
          },
        ]
      }),
      stopErrors: errors,
    }
    return stops
  }

  if (isMultiJobPickupDropOff) {
    const sortedUntaggedStops = actualDropoffStops.sort(compare)
    const sortedTaggedStops = taggedDropOffArr.sort(taggedCompare)
    const customerId = uniquePickUp?.customerId
      ? { customerId: uniquePickUp?.customerId }
      : ''

    const stops = {
      multiStops: sortedUntaggedStops.map((stop, stopIndex) => {
        const dropOffCustomerId = stop.customerId ? { customerId: stop.customerId } : ''
        const uniquePickUpStopEmail = uniquePickUp?.email
          ? { email: uniquePickUp?.email }
          : ''
        const customerInfoPickUp = !uniquePickUp?.customerId
          ? {
              customerName: uniquePickUp?.customerName,
              ...uniquePickUpStopEmail,
              ...(uniquePickUp?.phoneCountryCode?.toString().replaceAll(/\D/g, '') && {
                contactCode: uniquePickUp.phoneCountryCode
                  .toString()
                  .replaceAll(/\D/g, ''),
              }),
              ...(uniquePickUp?.phone?.toString().replaceAll(/\D/g, '') && {
                contactNumber: uniquePickUp.phone.toString().replaceAll(/\D/g, ''),
              }),
              ...((pickup) => {
                if (pickup?.gps) {
                  return {
                    latitude: pickup?.gps.split(',')[0],
                    longitude: pickup?.gps.split(',')[1],
                  } // Latitude
                } else if (pickup?.lat && pickup?.lng) {
                  return {
                    latitude: pickup?.lat,
                    longitude: pickup?.lng,
                  }
                } else {
                  return {
                    addressLine1: uniquePickUp?.addressLine1,
                    addressLine2: uniquePickUp?.addressLine2,
                    postalCode: uniquePickUp?.postalCode || null,
                    countryId: pickUpCountry?.country_id,
                  }
                }
              })(uniquePickUp),
            }
          : {}
        const stopEmail = stop.email ? { email: stop.email } : ''
        const customerInfoDropOff = !stop.customerId
          ? {
              customerName: stop.customerName || '',
              ...stopEmail,
              ...(stop.phoneCountryCode && {
                contactCode: stop.phoneCountryCode.toString().replaceAll(/\D/g, ''),
              }),
              ...(stop.phone && {
                contactNumber: stop.phone.toString().replaceAll(/\D/g, ''),
              }),
              ...((pickup) => {
                if (pickup.gps) {
                  return {
                    latitude: pickup.gps.split(',')[0],
                    longitude: pickup.gps.split(',')[1],
                  } // Latitude
                } else if (pickup?.lat && pickup?.lng) {
                  return {
                    latitude: pickup?.lat,
                    longitude: pickup?.lng,
                  }
                } else {
                  return {
                    addressLine1: pickup.addressLine1 || '',
                    addressLine2: pickup.addressLine2 || '',
                    postalCode: pickup.postalCode || null,
                    countryId:
                      (pickup.countryCode && getCountryId(pickup.countryCode)) || '',
                  }
                }
              })(stop),
            }
          : {}
        return [
          {
            ...customerId,
            stopTypeId: JOB_STOP_TYPE_ID.PICKUP,
            scheduledDeliveryTs:
              uniquePickUp?.sendDateTime && uniquePickUp?.sendDateTime.trim() !== ''
                ? setScheduledDeliveryTs(uniquePickUp?.sendDateTime)
                : null,
            rowLocation: uniquePickUp?.rowLocation,
            ...customerInfoPickUp,
            note: uniquePickUp?.note,
            priority: jobPriority,
            duration: Number(uniquePickUp?.duration) || 5,
            ...(uniquePickUp?.timeWindow?.trim() && {
              deliveryWindows: getDeliveryWindows(uniquePickUp?.timeWindow),
            }),
            // saveToAddressBook: !uniquePickUp?.customerId ? true : false,
            todos: generateStopsTodos(actualPickupStops, taggedPickUpArr, true),
          },
          {
            ...dropOffCustomerId,
            stopTypeId: JOB_STOP_TYPE_ID.DROPOFF,
            scheduledDeliveryTs:
              stop?.sendDateTime && stop?.sendDateTime.trim() !== ''
                ? setScheduledDeliveryTs(stop?.sendDateTime)
                : null,
            rowLocation: stop?.rowLocation,
            ...customerInfoDropOff,
            note: stop?.note,
            priority: jobPriority,
            duration: Number(stop?.duration) || 5,
            ...(stop?.timeWindow?.trim() && {
              deliveryWindows: getDeliveryWindows(stop?.timeWindow),
            }),
            // saveToAddressBook: !stop.customerId ? true : false,
            todos: generateStopsTodos([stop], [sortedTaggedStops[stopIndex]]),
          },
        ]
      }),
      stopErrors: errors,
    }

    return stops
  }
  return { multiStops: [], stops: [], stopErrors: [] }
}
