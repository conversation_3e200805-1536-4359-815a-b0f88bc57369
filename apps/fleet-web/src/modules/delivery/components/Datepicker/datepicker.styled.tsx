/* stylelint-disable no-descending-specificity */
import DatePicker from 'react-datepicker'
import styled, { css } from 'styled-components'

import { themeType, type ThemeType } from 'src/modules/delivery/components/Theme'
import { isFirefox } from 'src/modules/delivery/utils/browser'

const CustomDateTimePicker = styled(DatePicker)``
const CustomDatePicker = styled(DatePicker)<{ hasIcon?: boolean }>`
  input {
    text-align: ${({ hasIcon }) => (hasIcon ? 'center' : 'left')};

    &:focus ~ svg,
    &:active ~ svg {
      path {
        fill: ${themeType.light.black3};
      }
    }

    &:hover,
    &:focus,
    &:active {
      background: ${themeType.light.grey13};
    }
  }
`

const PickerLabel = styled.div`
  color: ${themeType.light.styledDefaultFontColor};
  font-size: 10px;
  padding: 2px 0;
  text-align: left;
`

const DeliveryDatePickerWrapper = styled.div<{ isDisabled?: boolean }>`
  width: 100%;
  display: flex;

  ${(p) =>
    p.isDisabled
      ? css`
          input {
            color: ${themeType.light.styleDisabledColour};
            cursor: not-allowed !important;

            &:hover {
              border-color: ${themeType.light.grey6};
            }
          }
        `
      : ''}

  & .DayPicker-wrapper {
    min-width: 100%;
  }

  & .DayPicker-Month {
    margin: 0 1rem;
  }

  &&& {
    .react-datepicker__triangle {
      transform: translate(105px, 0px) !important;
      display: none;
    }
    .react-datepicker-popper {
      z-index: 2;
    }
  }

  & .delivery_datePicker_clearButton::after {
    background-color: transparent;
    color: ${themeType.light.grey};
    padding: 0px;
    font-size: 20px;
  }

  .react-datepicker-popper[data-placement^='bottom'] {
    padding-top: 0;
  }

  .react-datepicker {
    display: flex;
    border: none;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.09);
    font-size: 0.7rem;

    .react-datepicker__month-container {
      width: 190px;
    }

    .react-datepicker__header {
      padding: 0;
      background-color: white;
      border: none;

      .react-datepicker__current-month,
      .react-datepicker-time__header {
        padding: 8px 0;
        color: ${themeType.light.grey3};
        font-weight: normal;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.09);
      }
    }

    .react-datepicker__navigation-icon::before {
      border-color: #ccc;
      border-style: solid;
      border-width: 2px 2px 0 0;
      content: '';
      display: block;
      height: 7px;
      position: absolute;
      top: 7px;
      width: 7px;
    }

    .react-datepicker__navigation--next--with-time:not(
      .react-datepicker__navigation--next--with-today-button
    ) {
      right: 65px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 1.25rem;
    }

    .react-datepicker__day--weekend {
      color: ${themeType.light.red5};

      &.react-datepicker__day--disabled {
        color: ${themeType.light.grey6};
      }
    }

    .react-datepicker__day--selected {
      background-color: ${`var(--styleActiveButtonsColour)`};
      color: white;
    }

    .react-datepicker__day--keyboard-selected,
    .react-datepicker__month-text--keyboard-selected,
    .react-datepicker__quarter-text--keyboard-selected,
    .react-datepicker__year-text--keyboard-selected {
      background-color: ${`var(--styleActiveButtonsColour)`};
      color: white;
    }

    .react-datepicker__time-container {
      width: 60px;

      .react-datepicker__time-box {
        width: auto;

        ul.react-datepicker__time-list {
          ${isFirefox
            ? css`
                scrollbar-color: grey transparent;
                overflow: scroll !important;
              `
            : 'overflow: overlay !important;'};

          .react-datepicker__time-list-item--selected {
            background-color: ${`var(--styleActiveButtonsColour)`} !important;
          }
        }
      }
    }

    .react-datepicker-time__input {
      margin-left: 5px;

      input {
        border: 1px solid #ccc;
        border-radius: 5px;
        color: ${themeType.light.grey3};
        padding-left: 5px;
      }
    }
  }
`
const InputWrapper = styled.div`
  position: relative;

  & svg {
    position: absolute;
    top: 5px;
    display: flex;
    left: 8px;
    font-size: 15px;
    z-index: 1;
    background-color: ${themeType.light.white};
    pointer-events: none;
  }

  & svg[data-icon='caret-down'] {
    left: revert;
    right: 7px;
    width: 1em;
  }
`

const InputField = styled.input<{
  themeType: ThemeType
  width?: string
  borderless?: boolean
}>`
  background: #fff;
  color: grey;
  border: ${({ borderless }) =>
    `1px solid ${borderless ? 'transparent' : themeType.light.grey6}`};
  border-radius: 5px;
  box-sizing: border-box;
  font-size: 12px;
  height: 25px;
  padding: 5px;
  text-align: center;
  transition: all 0.2s linear;
  width: ${({ width }) => (width ? width : '100%')};
  font-family: inherit;
  position: relative;

  ::-webkit-calendar-picker-indicator {
    background-image: url(${`assets/svg/caret-down.svg`});
    background-size: 16px 24px;
    background-position: 50% 50%;
  }

  :active,
  :hover,
  :focus {
    border-color: ${`var(--styleActiveButtonsColour)`};
  }

  :focus {
    outline: 0;
  }
`

const FieldWrapper = styled.div`
  width: 100%;
  position: relative;
  text-align: left;
`
const InputLabel = styled.span<{ required?: boolean }>`
  height: auto;
  ${({ required }) => !required && `width:100%;`}
  padding: 5px 0;
  font-size: 10px;
  color: #666;
  float: left;

  &:focus {
    outline: 0;
  }
`

const HelperText = styled.div<{ required?: boolean }>`
  width: 100%;
  height: auto;
  padding: 5px 0;
  font-size: 10px;
  color: #d52121;
`

const TimeSelectOnlyPicker = styled(CustomDateTimePicker)<{
  textAlign?: string
  disabled?: boolean
}>`
  border: none;
  background: transparent;
  max-width: 100px;
  text-align: ${(p) => (p.textAlign ? p.textAlign : '#666666')};
  font-size: 12px;
  cursor: ${(p) => (p.disabled ? 'not-allowed' : 'inherit')};

  &:focus-visible {
    border: none;
    outline: none;
  }
`

export {
  DeliveryDatePickerWrapper,
  InputWrapper,
  InputField,
  CustomDateTimePicker,
  FieldWrapper,
  InputLabel,
  HelperText,
  TimeSelectOnlyPicker,
  CustomDatePicker,
  PickerLabel,
}
