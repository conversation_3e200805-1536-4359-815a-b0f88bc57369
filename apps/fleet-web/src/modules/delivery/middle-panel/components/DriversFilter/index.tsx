/* eslint-disable react-hooks/react-compiler */
import { useEffect, useState } from 'react'
import type { IndicatorProps } from 'react-select'

import { useDebouncedValue } from 'src/hooks'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
import type { FetchGetDeliveryDrivers } from 'src/modules/delivery/api/drivers/useDeliveryGetDrivers'
import Box from 'src/modules/delivery/components/Box'
import Checkbox from 'src/modules/delivery/components/Checkbox'
import {
  StyledCustomIndicatorWrapper,
  StyledDropdownIcon,
  StyledMultiSelectDropdown,
} from 'src/modules/delivery/middle-panel/components/StatusFilter'
import { ctIntl } from 'src/util-components/ctIntl'

import { useFilterContext } from '../../hooks/useFilterContext'

type Props = {
  drivers: FetchGetDeliveryDrivers.Return
}

type Options = {
  label: string
  value: string
  name: string
}

const defaultDropdownIndicator = (props: IndicatorProps<any>) => (
  <StyledCustomIndicatorWrapper {...props}>
    <StyledDropdownIcon icon="caret-down" />
  </StyledCustomIndicatorWrapper>
)

const DriversFilter = ({ drivers }: Props) => {
  const { filterStateDispatcher, filterState } = useFilterContext()
  const [filterValues, setFilterValues] = useState<Array<Options> | undefined>()
  const debounceValue = useDebouncedValue<Array<Options> | undefined>(filterValues, 500)
  const options = drivers.map((driver) => ({
    value: driver.driverId,
    label: driver.fullName,
    name: driver.fullName,
  }))

  useEffect(() => {
    const selected = options.filter((opt) =>
      filterState.filters.deliveryDriverId?.includes(opt.value),
    )
    setFilterValues(selected)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (
      !filterState.filters.deliveryDriverId ||
      filterState.filters.deliveryDriverId.length === 0
    ) {
      setFilterValues([])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(filterState.filters.deliveryDriverId)])

  useEffectExceptOnMount(() => {
    if (debounceValue) {
      filterStateDispatcher({
        key: 'SET_FILTERS',
        payload: {
          deliveryDriverId: debounceValue.map((opt) => opt.value),
        },
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceValue])

  const customOptions = (optionProps: Record<string, any>) => (
    <Box
      display="flex"
      alignItems="center"
    >
      <Box
        marginRight="5px"
        marginTop="-5px"
      >
        <Checkbox
          isSelected={optionProps.isSelected}
          value={optionProps.value}
          label={''} // Adding Empty label to utilize the icon label from the right
        />
      </Box>
      <Box>{optionProps.label}</Box>
    </Box>
  )

  return (
    <StyledMultiSelectDropdown
      closeMenuOnSelect={false}
      options={options}
      placeholder={ctIntl.formatMessage({ id: 'Filter by job drivers' })}
      customDropDownIndicator={defaultDropdownIndicator}
      customOptions={customOptions}
      onChange={(value: Array<Options>) => {
        setFilterValues(value || [])
      }}
      value={filterValues}
    />
  )
}

export default DriversFilter
