/* eslint-disable react-hooks/react-compiler */
import { useEffect, useMemo, useState } from 'react'
//types
import type { IndicatorProps, ValueType } from 'react-select'

//hooks
import { useDebouncedValue } from 'src/hooks'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
//api
import useFetchDeliveryJobLabels from 'src/modules/delivery/api/job-labels/useFetchDeliveryJobLabels'
//component
import Box from 'src/modules/delivery/components/Box'
import Checkbox from 'src/modules/delivery/components/Checkbox'
//constants

//helpers
import { ctIntl } from 'src/util-components/ctIntl'

import { useFilterContext } from '../../hooks/useFilterContext'
//styled
import {
  StyledCustomIndicatorWrapper,
  StyledDropdownIcon,
  StyledMultiSelectDropdown,
} from '../StatusFilter'

const defaultDropdownIndicator = (props: IndicatorProps<any>) => (
  <StyledCustomIndicatorWrapper {...props}>
    <StyledDropdownIcon icon="caret-down" />
  </StyledCustomIndicatorWrapper>
)

type JobLabelOptions = {
  label: string
  value: number
  name: string
}

const JobLabelsFilter = () => {
  const { filterStateDispatcher, filterState } = useFilterContext()

  const [filterValues, setFilterValues] = useState<Array<JobLabelOptions> | undefined>()
  const debounceValue = useDebouncedValue<Array<JobLabelOptions> | undefined>(
    filterValues,
    500,
  )

  const jobLabelsData = useFetchDeliveryJobLabels()

  const jobLabels = useMemo(
    () =>
      jobLabelsData.isSuccess && jobLabelsData.data
        ? jobLabelsData.data
            .sort((a, b) => Number(a.id) - Number(b.id))
            .map((jobLabels) => ({
              value: Number(jobLabels.id),
              label: !jobLabels.userId
                ? ctIntl.formatMessage({ id: jobLabels.name })
                : jobLabels.name,
              name: jobLabels.name,
            }))
        : [],
    [jobLabelsData],
  )

  useEffect(() => {
    const setJobLabelsFilterState = jobLabels.filter((jobLabelsOption) =>
      filterState?.filters?.label?.includes(jobLabelsOption.value),
    )
    setFilterValues(setJobLabelsFilterState)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (!filterState.filters.label || filterState.filters.label.length === 0) {
      setFilterValues([])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(filterState.filters.label)])

  useEffectExceptOnMount(() => {
    const labelsFilters =
      debounceValue && debounceValue.length > 0
        ? debounceValue.map((option: JobLabelOptions) => option.value)
        : null

    filterStateDispatcher({
      key: 'SET_FILTERS',
      payload: {
        label: labelsFilters || [],
      },
    })

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceValue])

  const handleFilterChange = (values: ValueType<any>) => {
    setFilterValues(values)
  }

  const customOptions = (optionProps: Record<string, any>) => (
    <Box
      display="flex"
      alignItems="center"
    >
      <Box
        marginRight="5px"
        marginTop="-5px"
      >
        <Checkbox
          isSelected={optionProps.isSelected}
          value={optionProps.value}
          label={''} // Adding Empty label to utilize the icon label from the right
        />
      </Box>
      <Box>{optionProps.label}</Box>
    </Box>
  )

  return (
    <StyledMultiSelectDropdown
      options={jobLabels}
      closeMenuOnSelect={false}
      hideSelectedOptions={false}
      isSearchable={false}
      onChange={handleFilterChange}
      isRenderTotalSelected
      value={filterValues}
      placeholder={ctIntl.formatMessage({ id: 'Filter by job labels' })}
      customDropDownIndicator={defaultDropdownIndicator}
      customOptions={customOptions}
    />
  )
}

export default JobLabelsFilter
