import { useMemo } from 'react'
import * as R from 'remeda'

import type {
  BookingPurposeId,
  BookingVehicleCategoryId,
  DriverId,
  LocationId,
  VehicleId,
} from 'api/types'
import type { UseDriversQueryData } from 'src/modules/api/useDriversQuery'
import { useVehiclesWithCarpoolMetaQuery } from 'src/modules/api/useVehiclesQuery'
import type useCarpoolOptionsQuery from 'src/modules/carpool/queries/useCarpoolOptionsQuery'
import type { UseBookingConfigurationQueryData } from 'src/modules/carpool/Settings/BookingManagement/api/useBookingConfigurationQuery'
import type { FacilityTranslationTermsBanned } from 'src/util-components/intlBannedTerms'

export type BookingPurposeAutocompleteOption = { id: BookingPurposeId; label: string }
export type BookingVehicleCategoryAutocompleteOption = {
  id: BookingVehicleCategoryId
  label: string
  value: BookingVehicleCategoryId
  vehicleIds: Set<VehicleId>
}
export type VehicleRegistrationAutocompleteOption = {
  id: VehicleId
  label: string
  value: VehicleId
}

export type EmptyVehicleErrorMessage = {
  message: string
  fieldMessages?: Array<string>
} | null

function createErrorMessageIfNeeded(
  condition: boolean,
  currentError: EmptyVehicleErrorMessage,
  details:
    | { type: 'message'; message: string }
    | { type: 'criteria'; fieldMessages: Array<string> },
): EmptyVehicleErrorMessage {
  if (condition && !currentError) {
    return details.type === 'message'
      ? { message: details.message }
      : {
          message: 'carpool.noVehicle.criteria',
          fieldMessages: details.fieldMessages,
        }
  }
  return currentError
}

/**
 * Hook to handle data needed for the issuance form.
 * This includes computed memos and error message logic.
 */
function useIssuanceFormOptions({
  carpoolOptions,
  selectedBookingPurposeId,
  selectedDriverId,
  selectedLocationId,
  translateFacilitiesTerm,
  bookingConfigurationQueryData,
  allDriversById,
}: {
  carpoolOptions: ReturnType<typeof useCarpoolOptionsQuery>
  selectedBookingPurposeId: BookingPurposeId | null
  selectedDriverId: DriverId | null
  selectedLocationId: LocationId | null
  translateFacilitiesTerm: (term: FacilityTranslationTermsBanned) => string
  bookingConfigurationQueryData: UseBookingConfigurationQueryData
  allDriversById: UseDriversQueryData['allDriversById']
}) {
  const vehiclesWithCarpoolMetaQuery = useVehiclesWithCarpoolMetaQuery()

  const {
    bookingPermissions: { checkLicenses, checkSpecialLicenses, checkDepartments },
    vehicleManagementConfig: {
      vehicleCategoriesAndBookingPurposes,
      vehicleManagementLocation,
    },
  } = bookingConfigurationQueryData

  const bookingPurposeOptions = useMemo(() => {
    const array: Array<BookingPurposeAutocompleteOption> = []
    const byId = new Map<
      BookingPurposeAutocompleteOption['id'],
      BookingPurposeAutocompleteOption
    >()

    if (carpoolOptions.data && !R.isEmpty(carpoolOptions.data)) {
      for (const purpose of carpoolOptions.data.bookingPurposes) {
        const option = { id: purpose.id, label: purpose.purpose }
        array.push(option)
        byId.set(option.id, option)
      }
    }

    return { array, byId }
  }, [carpoolOptions.data])

  const baseBookingVehicleCategoriesArray = useMemo(() => {
    const array: Array<BookingVehicleCategoryAutocompleteOption> = []

    if (carpoolOptions.data && !R.isEmpty(carpoolOptions.data)) {
      const bookingPurpose = carpoolOptions.data.bookingPurposes.find(
        (p) => p.id === selectedBookingPurposeId,
      )

      if (bookingPurpose) {
        for (const vehicleCategory of bookingPurpose.allowedVehicle) {
          // Get vehicles for this category
          const vehiclesInCategory =
            vehiclesWithCarpoolMetaQuery.status === 'success'
              ? (vehiclesWithCarpoolMetaQuery.data.bookingCategoriesAndVehiclesJoinByCategoryId.get(
                  vehicleCategory.vehicleCategoryId,
                ) as Set<VehicleId>) || new Set<VehicleId>()
              : new Set<VehicleId>()

          const option = {
            id: vehicleCategory.vehicleCategoryId,
            label: vehicleCategory.vehicleCategory,
            value: vehicleCategory.vehicleCategoryId,
            vehicleIds: vehiclesInCategory,
          }
          array.push(option)
        }
      }
    }

    return array
  }, [
    carpoolOptions.data,
    selectedBookingPurposeId,
    vehiclesWithCarpoolMetaQuery.status,
    vehiclesWithCarpoolMetaQuery.data?.bookingCategoriesAndVehiclesJoinByCategoryId,
  ])

  const availableVehicleMeta = useMemo((): {
    optionsMeta: {
      array: Array<VehicleRegistrationAutocompleteOption>
      byId: Map<
        VehicleRegistrationAutocompleteOption['id'],
        VehicleRegistrationAutocompleteOption
      >
    }
    emptyVehicleErrorMessage: EmptyVehicleErrorMessage
    availableVehiclesSet: Set<VehicleId>
  } => {
    let emptyVehicleErrorMessage: EmptyVehicleErrorMessage = null
    const fieldMessages: Array<string> = []

    const array: Array<VehicleRegistrationAutocompleteOption> = []
    const byId = new Map<
      VehicleRegistrationAutocompleteOption['id'],
      VehicleRegistrationAutocompleteOption
    >()

    if (carpoolOptions.data && vehiclesWithCarpoolMetaQuery.data) {
      const {
        readyNotInMaintenanceCarpoolBookingVehicleOptionsMeta,
        locationAndVehicleJoinByVehicleId,
      } = vehiclesWithCarpoolMetaQuery.data

      const selectedDriver = selectedDriverId
        ? (allDriversById.get(selectedDriverId) ?? null)
        : null

      let filteredVehicles = readyNotInMaintenanceCarpoolBookingVehicleOptionsMeta.array

      // filter by purpose
      if (selectedBookingPurposeId && vehicleCategoriesAndBookingPurposes) {
        filteredVehicles = filteredVehicles.filter((vehicle) =>
          carpoolOptions.data.bookingPurposes
            .find((p) => p.id === selectedBookingPurposeId)
            ?.allowedVehicle.some(
              (av) => av.vehicleCategoryId === vehicle.bookingCategoryId,
            ),
        )
        fieldMessages.push('carpool.list.addEditBooking.purposeOfRequest')

        emptyVehicleErrorMessage = createErrorMessageIfNeeded(
          filteredVehicles.length === 0,
          emptyVehicleErrorMessage,
          {
            type: 'criteria',
            fieldMessages: [...fieldMessages],
          },
        )
      }

      // filter by location
      if (selectedLocationId && vehicleManagementLocation) {
        filteredVehicles = filteredVehicles.filter(
          (vehicle) =>
            locationAndVehicleJoinByVehicleId.get(vehicle.id) === selectedLocationId,
        )

        const facilityLabel = translateFacilitiesTerm('Facility')
        fieldMessages.push(facilityLabel)

        emptyVehicleErrorMessage = createErrorMessageIfNeeded(
          filteredVehicles.length === 0,
          emptyVehicleErrorMessage,
          {
            type: 'criteria',
            fieldMessages: [...fieldMessages],
          },
        )
      }

      if (checkDepartments) {
        filteredVehicles = filteredVehicles.filter((vehicle) => {
          if (!selectedDriver) {
            // No driver selected, so keep list as is.
            return true
          }
          if (!selectedDriver.departmentId) {
            return false
          }

          return (
            // If common pool, show it
            vehicle.commonPool ||
            // Check if driver belongs to at least one of the vehicle's departments
            vehicle.departmentIds.has(selectedDriver.departmentId)
          )
        })

        fieldMessages.push('carpool.noVehicle.driverDepartment')
        emptyVehicleErrorMessage = createErrorMessageIfNeeded(
          filteredVehicles.length === 0,
          emptyVehicleErrorMessage,
          {
            type: 'criteria',
            fieldMessages: [...fieldMessages],
          },
        )
      }

      if (selectedDriver) {
        const driverValidSpecialLicenseTypes =
          selectedDriver.driverSpecialLicenseTypes.filter((t) => t.isValid)
        const driverValidLicenseTypes = selectedDriver.driverLicenseTypes.filter(
          (t) => t.isValid,
        )

        // license
        filteredVehicles = filteredVehicles.filter(
          (vehicle) =>
            (!checkSpecialLicenses ||
              R.isEmpty(vehicle.specialLicenses) ||
              driverValidSpecialLicenseTypes.some((type) =>
                vehicle.specialLicenses.some((sl) => sl.id === type.id),
              )) &&
            (!checkLicenses ||
              vehicle.vehicleClassId === null ||
              driverValidLicenseTypes.some(
                (type) => type.id === vehicle.vehicleClassId,
              )),
        )

        if (checkLicenses || checkSpecialLicenses) {
          fieldMessages.push('carpool.noVehicle.driverLicences')

          emptyVehicleErrorMessage = createErrorMessageIfNeeded(
            filteredVehicles.length === 0,
            emptyVehicleErrorMessage,
            {
              type: 'criteria',
              fieldMessages: [...fieldMessages],
            },
          )
        }
      }

      for (const vehicle of filteredVehicles) {
        // To maintain consistency with mobile app, we use the registration number as the label, at all times in the booking form.
        const option = {
          id: vehicle.id,
          label: vehicle.registration,
          value: vehicle.value,
        }
        array.push(option)
        byId.set(option.id, option)
      }
    }

    const availableVehiclesSet = new Set(byId.keys())

    return {
      optionsMeta: { array, byId },
      emptyVehicleErrorMessage,
      availableVehiclesSet,
    }
  }, [
    carpoolOptions.data,
    vehiclesWithCarpoolMetaQuery.data,
    selectedDriverId,
    allDriversById,
    selectedBookingPurposeId,
    vehicleCategoriesAndBookingPurposes,
    selectedLocationId,
    vehicleManagementLocation,
    checkDepartments,
    translateFacilitiesTerm,
    checkLicenses,
    checkSpecialLicenses,
  ])

  const bookingVehicleCategoriesOptions = useMemo(() => {
    const array: Array<BookingVehicleCategoryAutocompleteOption> = []
    const byId = new Map<
      BookingVehicleCategoryAutocompleteOption['id'],
      BookingVehicleCategoryAutocompleteOption
    >()

    if (
      baseBookingVehicleCategoriesArray.length > 0 &&
      availableVehicleMeta.optionsMeta.byId.size > 0
    ) {
      const availableVehicleIds = new Set(availableVehicleMeta.optionsMeta.byId.keys())

      // For each category, filter to only include available vehicles
      for (const category of baseBookingVehicleCategoriesArray) {
        // Create a new set with only the vehicles that are available
        const filteredVehicleIds = new Set<VehicleId>()

        for (const vehicleId of category.vehicleIds) {
          if (availableVehicleIds.has(vehicleId)) {
            filteredVehicleIds.add(vehicleId)
          }
        }

        // if there are no vehicles in the category, the we no need to display this category
        if (filteredVehicleIds.size > 0) {
          const filteredOption = {
            ...category,
            vehicleIds: filteredVehicleIds,
          }

          array.push(filteredOption)
          byId.set(filteredOption.id, filteredOption)
        }
      }
    }

    return { array, byId }
  }, [baseBookingVehicleCategoriesArray, availableVehicleMeta.optionsMeta.byId])

  return {
    bookingPurposeOptions,
    bookingVehicleCategoriesOptions,
    availableVehicleOptions: availableVehicleMeta.optionsMeta,
    emptyVehicleErrorMessage: availableVehicleMeta.emptyVehicleErrorMessage,
    availableVehiclesSet: availableVehicleMeta.availableVehiclesSet,
  }
}

export default useIssuanceFormOptions
