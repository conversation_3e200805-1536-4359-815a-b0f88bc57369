import { useCallback, useEffect, useMemo, useRef } from 'react'
import { isEmpty } from 'lodash'
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  ButtonGroup,
  CircularProgressDelayedCentered,
  DateTimePicker,
  FormHelperText,
  IconButton,
  KarooFormStateContextProvider,
  Stack,
  TextField,
  Typography,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import { useForm, useStore } from '@tanstack/react-form'
import { DateTime } from 'luxon'
import { useHistory } from 'react-router'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'

import type { BookingVehicleCategoryId, DriverId, VehicleId } from 'api/types'
import {
  getCarpoolCreateBookingsSetting,
  getCarpoolEditBookingsSetting,
} from 'duxs/user'
import {
  getCarpoolAllowBackDateBooking,
  getFacilitiesTranslatorFn,
} from 'duxs/user-sensitive-selectors'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import { useValidatedSearchParams } from 'src/hooks/useValidatedSearchParams'
import {
  useDriversWithCarpoolMetaQuery,
  type UseDriversWithCarpoolMetaQueryData,
} from 'src/modules/api/useDriversQuery'
import { LIST } from 'src/modules/app/components/routes/list'
import useIssuanceListQuery, {
  type FetchIssuanceList,
} from 'src/modules/carpool/List/api/useIssuanceListQuery'
import {
  useLocationsQuery,
  type UseLocationsQueryData,
} from 'src/modules/lists/Facilities/api/queries'
import { useTypedSelector } from 'src/redux-hooks'
import { setFieldValueAndValidate } from 'src/shared/tanstack-form/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import useCarpoolOptionsQuery from '../../queries/useCarpoolOptionsQuery'
import {
  useBookingConfigurationQuery,
  type UseBookingConfigurationQueryData,
} from '../../Settings/BookingManagement/api/useBookingConfigurationQuery'
import { BookingStatus } from '../../utils/constants'
import { useCarpoolLinksMeta } from '../../utils/hooks'
import useCreateBookingMutation from './api/useCreateBookingMutation'
import {
  useEditBookingMutation,
  type UseEditBookingMutationVariables,
} from './api/useEditBookingMutation'
import type { ApiErrorWithAvailableSchedule } from './api/utils'
import AvailableTimeSlots from './AvailableTimeSlots'
import useAvailableTimeSlots from './hooks/useAvailableTimeSlots'
import useIssuanceFormOptions from './hooks/useIssuanceFormOptions'
import useVehicleAvailability from './hooks/useVehicleAvailability'
import {
  bookingSchemaMetaUtils,
  createBookingSchemaMeta,
  issuanceRequestSearchParamsSchema,
  type BookingOnSubmitValidSchema,
  type BookingSchema,
} from './schema'
import { BookingTypeToggleButton, DrawerRoot } from './utils'
import VehicleBookingScheduler, {
  type VehicleBookingSchedulerHandle,
  type VehicleBookingSchedulerProps,
} from './VehicleBookingScheduler'
import type { SelectByMetaWithResources } from './VehicleBookingScheduler/types'
import {
  getBookingResourceEntityIdMeta,
  type BookingResourceId,
} from './VehicleBookingScheduler/vehicleBookingSchedulerEventHandler'

const FORM_WIDTH = 400

function IssuanceRequestDrawer({ onClose }: { onClose: () => void }) {
  const validatedParams = useValidatedSearchParams(
    () => issuanceRequestSearchParamsSchema,
  )
  const issuanceListQuery = useIssuanceListQuery()
  const bookingConfigurationQuery = useBookingConfigurationQuery()
  const locationsQuery = useLocationsQuery()
  const driversWithCarpoolMetaQuery = useDriversWithCarpoolMetaQuery()
  const carpoolCreateBookingsSetting = useTypedSelector(getCarpoolCreateBookingsSetting)

  if (
    !bookingConfigurationQuery.data ||
    !locationsQuery.data ||
    !driversWithCarpoolMetaQuery.data
  ) {
    return (
      <DrawerRoot onClose={onClose}>
        <CircularProgressDelayedCentered />
      </DrawerRoot>
    )
  }

  return (
    <>
      {match(validatedParams)
        .with({ status: 'invalid' }, () => null)
        .with({ status: 'valid', data: { type: 'add' } }, () => (
          <DrawerRoot onClose={onClose}>
            {carpoolCreateBookingsSetting ? (
              <Content
                onClose={onClose}
                meta={{ type: 'add' }}
                bookingConfigurationQueryData={bookingConfigurationQuery.data}
                locationsQueryData={locationsQuery.data}
                driversWithCarpoolMetaQueryData={driversWithCarpoolMetaQuery.data}
                bookings={issuanceListQuery.data?.bookings ?? []}
              />
            ) : (
              <Typography sx={{ margin: 'auto' }}>
                {ctIntl.formatMessage({
                  id: 'carpool.bookingDetails.noCreateBookingRequestPermissionText',
                })}
              </Typography>
            )}
          </DrawerRoot>
        ))
        .with({ status: 'valid', data: { type: 'edit' } }, ({ data: { id } }) => (
          <>
            {match(issuanceListQuery)
              .with({ status: 'pending' }, () => (
                <DrawerRoot onClose={onClose}>
                  <CircularProgressDelayedCentered />
                </DrawerRoot>
              ))
              .with({ status: 'error' }, () => <></>)
              .with({ status: 'success' }, ({ data: queryData }) => {
                const booking = queryData.bookings.find(
                  (b) => String(b.id) === String(id),
                )

                if (!booking) {
                  return (
                    <Typography color="error">
                      {ctIntl.formatMessage({ id: 'Not found' })}
                    </Typography>
                  )
                }

                return (
                  <DrawerRoot onClose={onClose}>
                    <Content
                      bookingConfigurationQueryData={bookingConfigurationQuery.data}
                      locationsQueryData={locationsQuery.data}
                      driversWithCarpoolMetaQueryData={driversWithCarpoolMetaQuery.data}
                      bookings={queryData.bookings}
                      onClose={onClose}
                      meta={{
                        type: 'edit',
                        initialValues: {
                          driverId: booking.driverId,
                          bookingPurposeId: booking.purposeId,
                          requestDescription: booking.purposeDescription,
                          pickupTime: booking.startDate,
                          dropoffTime: booking.endDate,
                          locationId: booking.pickupLocationId,
                          selectByMeta: booking.vehicleId
                            ? {
                                type: 'specific_vehicle',
                                vehicleId: booking.vehicleId,
                              }
                            : {
                                type: 'auto_booking',
                                bookingVehicleCategoryId: booking.vehicleCategoryId,
                              },
                        },
                        bookingId: booking.id,
                        bookingStatusId: booking.statusId as BookingStatus,
                        isKeyCollected: !!booking.keyCollectionDate,
                      }}
                    />
                  </DrawerRoot>
                )
              })
              .exhaustive()}
          </>
        ))
        .exhaustive()}
    </>
  )
}

type ContentProps = {
  onClose: () => void
  bookingConfigurationQueryData: UseBookingConfigurationQueryData
  locationsQueryData: UseLocationsQueryData
  driversWithCarpoolMetaQueryData: UseDriversWithCarpoolMetaQueryData
  bookings: FetchIssuanceList.Return['bookings']
  meta:
    | {
        type: 'add'
      }
    | {
        type: 'edit'
        initialValues: Except<
          BookingSchema,
          'selectByMeta' | 'pickupTime' | 'dropoffTime'
        > & {
          pickupTime: string
          dropoffTime: string
          selectByMeta:
            | {
                type: 'specific_vehicle'
                vehicleId: VehicleId
              }
            | {
                type: 'auto_booking'
                bookingVehicleCategoryId: BookingVehicleCategoryId | null
              }
        }

        bookingId: number
        bookingStatusId: BookingStatus
        isKeyCollected: boolean
      }
}

type ComputedAvailableBookingTypes =
  | 'auto_booking_only'
  | 'specific_vehicle_only'
  | 'auto_booking_and_specific_vehicle'

function Content({
  onClose,
  meta,
  bookingConfigurationQueryData,
  locationsQueryData,
  driversWithCarpoolMetaQueryData,
  bookings,
}: ContentProps) {
  const history = useHistory()
  const { vehicleManagementLinksMeta } = useCarpoolLinksMeta()

  const {
    availableTimeSlots,
    selectedTimeSlotId,
    setSelectedTimeSlotId,
    handleAddEditBookingError,
    handleSelectTimeSlot,
  } = useAvailableTimeSlots()

  const carpoolEditBookings = useTypedSelector(getCarpoolEditBookingsSetting)

  const {
    allDriversById,
    canDriverBookCarpool,
    activeDriversWithCarpoolSetupComplete,
  } = driversWithCarpoolMetaQueryData

  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)
  const carpoolAllowBackDateBooking = useTypedSelector(getCarpoolAllowBackDateBooking)
  const carpoolOptions = useCarpoolOptionsQuery()
  const schedulerRef = useRef<VehicleBookingSchedulerHandle>(null)

  const { advanceBooking, vehicleManagementConfig } = bookingConfigurationQueryData

  const getComputedAvailableBookingTypes = useCallback(
    (driverId: DriverId | null): ComputedAvailableBookingTypes => {
      const driver = driverId ? (allDriversById.get(driverId) ?? null) : null
      if (!driver) {
        return 'auto_booking_and_specific_vehicle'
      }

      const bookCarpool = canDriverBookCarpool(driver)
      return bookCarpool.status === 'can_book'
        ? bookCarpool.allowedBookingTypes
        : // We already verify that the drivers sent to this function are drivers that can book
          // However, we provide a fallback anyways
          'specific_vehicle_only'
    },
    [canDriverBookCarpool, allDriversById],
  )

  const initialValues = useMemo((): BookingSchema => {
    if (meta.type === 'edit') {
      return {
        ...meta.initialValues,
        locationId: vehicleManagementConfig.vehicleManagementLocation
          ? meta.initialValues.locationId
          : null,
        bookingPurposeId: vehicleManagementConfig.vehicleCategoriesAndBookingPurposes
          ? meta.initialValues.bookingPurposeId
          : null,
        dropoffTime: new Date(meta.initialValues.dropoffTime),
        pickupTime: new Date(meta.initialValues.pickupTime),
      }
    }
    // Get initial value with null driverId
    const computedAvailableBookingTypes = getComputedAvailableBookingTypes(null)
    return {
      driverId: null,
      bookingPurposeId: null,
      requestDescription: '',
      pickupTime: null,
      dropoffTime: null,
      locationId: null,
      selectByMeta: match(computedAvailableBookingTypes)
        .returnType<BookingSchema['selectByMeta']>()
        .with('auto_booking_only', () => ({
          type: 'auto_booking',
          bookingVehicleCategoryId: null,
        }))
        .with('specific_vehicle_only', 'auto_booking_and_specific_vehicle', () => ({
          type: 'specific_vehicle',
          vehicleId: null,
        }))
        .exhaustive(),
    }
  }, [
    meta,
    getComputedAvailableBookingTypes,
    vehicleManagementConfig.vehicleManagementLocation,
    vehicleManagementConfig.vehicleCategoriesAndBookingPurposes,
  ])

  const { schema, isFieldAlwaysHidden } = useMemo(
    () =>
      createBookingSchemaMeta({
        carpoolBookingInAdvance: advanceBooking.requestBookingAheadOfTime.value,
        carpoolBookingInAdvanceUnit: advanceBooking.requestBookingAheadOfTime.unit,
        carpoolMaximumBookingTime: advanceBooking.maxDuration.value,
        carpoolMaximumBookingTimeUnit: advanceBooking.maxDuration.unit,
        carpoolAllowBackDateBooking,
        vehicleCategoriesAndBookingPurposes:
          vehicleManagementConfig.vehicleCategoriesAndBookingPurposes,
        vehicleManagementLocation: vehicleManagementConfig.vehicleManagementLocation,
      }),
    [carpoolAllowBackDateBooking, advanceBooking, vehicleManagementConfig],
  )

  const issuanceForm = useForm({
    defaultValues: bookingSchemaMetaUtils.createDefaultValues(initialValues),
    validators: {
      onChange: bookingSchemaMetaUtils.castSchema(schema),
      onMount: bookingSchemaMetaUtils.castSchema(schema),
    },
    onSubmit: ({ value: value_ }) => {
      const values = value_ as BookingOnSubmitValidSchema
      const {
        driverId,
        bookingPurposeId,
        requestDescription,
        dropoffTime,
        locationId,
        pickupTime,
        selectByMeta,
      } = values

      const mutationBaseVariables: Except<
        UseEditBookingMutationVariables,
        'bookingId'
      > = {
        driverId,
        bookingPurposeId,
        bookingRequestDescription: requestDescription ?? '',
        pickupDate: pickupTime as Date,
        dropoffDate: dropoffTime as Date,
        locationId,
        ...(selectByMeta.type === 'specific_vehicle'
          ? {
              type: 'specific_vehicle',
              vehicleId: selectByMeta.vehicleId,
            }
          : {
              type: 'auto_booking',
              bookingVehicleCategoryId: selectByMeta.bookingVehicleCategoryId,
            }),
      }

      if (meta.type === 'edit') {
        editBookingMutation.mutate(
          {
            bookingId: meta.bookingId,
            ...mutationBaseVariables,
          },
          {
            onSuccess: () => onClose(),
          },
        )
      } else {
        createBookingMutation.mutate(mutationBaseVariables, {
          onSuccess: () => onClose(),
        })
      }
    },
  })

  const selectByMeta = useStore(
    issuanceForm.store,
    (state) => state.values.selectByMeta,
  )
  const pickupTimeMillisValue = useStore(
    issuanceForm.store,
    (state) => state.values.pickupTime?.getTime() ?? null,
  )
  const dropoffTimeMillisValue = useStore(
    issuanceForm.store,
    (state) => state.values.dropoffTime?.getTime() ?? null,
  )
  const selectedBookingPurposeId = useStore(
    issuanceForm.store,
    (state) => state.values.bookingPurposeId,
  )
  const selectedDriverId = useStore(
    issuanceForm.store,
    (state) => state.values.driverId,
  )
  const selectedLocationId = useStore(
    issuanceForm.store,
    (state) => state.values.locationId,
  )

  const isFormValid = useStore(issuanceForm.store, (state) => state.isValid)

  const disabled = useMemo(() => {
    if (meta.type === 'add') {
      return false
    }

    // User is editing a booking

    if (!carpoolEditBookings) {
      return true
    }

    if (
      // Kept old logic from old  carpool. May need reviewing here
      meta.bookingStatusId !== BookingStatus.BOOKING_STATUS_FREE &&
      meta.bookingStatusId !== BookingStatus.BOOKING_STATUS_REQUESTED &&
      meta.bookingStatusId !== BookingStatus.BOOKING_STATUS_EXPIRING_APPROVAL
    ) {
      return true
    }

    return false
  }, [meta, carpoolEditBookings])

  const currentAvailableBookingTypes =
    getComputedAvailableBookingTypes(selectedDriverId)

  const driverOptions = useMemo(
    () => activeDriversWithCarpoolSetupComplete,
    [activeDriversWithCarpoolSetupComplete],
  )

  const createBookingMutation = useCreateBookingMutation({
    onError(error) {
      handleAddEditBookingError(error as ApiErrorWithAvailableSchedule)
    },
  })

  const editBookingMutation = useEditBookingMutation({
    onError(error) {
      handleAddEditBookingError(error as ApiErrorWithAvailableSchedule)
    },
  })

  const {
    bookingPurposeOptions,
    bookingVehicleCategoriesOptions,
    availableVehicleOptions,
    emptyVehicleErrorMessage,
    availableVehiclesSet,
  } = useIssuanceFormOptions({
    carpoolOptions,
    selectedBookingPurposeId,
    selectedDriverId,
    selectedLocationId,
    translateFacilitiesTerm,
    bookingConfigurationQueryData,
    allDriversById,
  })

  const isLoadingWhenSubmit =
    meta.type === 'edit'
      ? editBookingMutation.isPending
      : createBookingMutation.isPending

  const maxPickupDateTime = useMemo(() => {
    if (advanceBooking.requestBookingAheadOfTime.value > 0) {
      return DateTime.now().plus({
        [advanceBooking.requestBookingAheadOfTime.unit]:
          advanceBooking.requestBookingAheadOfTime.value,
      })
    }
    return undefined
  }, [advanceBooking.requestBookingAheadOfTime])

  const minPickupDateTime = useMemo(
    () => (carpoolAllowBackDateBooking ? undefined : DateTime.now()),
    [carpoolAllowBackDateBooking],
  )

  const onMount = useEffectEvent(() => {
    if (driverOptions.array.length === 1) {
      setFieldValueAndValidate(issuanceForm, 'driverId', driverOptions.array[0].id)
    }
    if (bookingPurposeOptions.array.length === 1) {
      setFieldValueAndValidate(
        issuanceForm,
        'bookingPurposeId',
        bookingPurposeOptions.array[0].id,
      )
    }
    if (locationOptions.array.length === 1) {
      setFieldValueAndValidate(issuanceForm, 'locationId', locationOptions.array[0].id)
    }

    match(initialValues.selectByMeta)
      .with({ type: 'specific_vehicle' }, () => {
        if (availableVehicleOptions.array.length === 1) {
          setFieldValueAndValidate(issuanceForm, 'selectByMeta', {
            type: 'specific_vehicle',
            vehicleId: availableVehicleOptions.array[0].id,
          })
        }
      })
      .with({ type: 'auto_booking' }, () => {
        if (bookingVehicleCategoriesOptions.array.length === 1) {
          setFieldValueAndValidate(issuanceForm, 'selectByMeta', {
            type: 'auto_booking',
            bookingVehicleCategoryId: bookingVehicleCategoriesOptions.array[0].id,
          })
        }
      })
      .exhaustive()
  })

  useEffect(() => {
    onMount()
  }, [])

  const resetVehicleRegistrationFieldIfNeeded = useEffectEvent(() => {
    if (
      selectByMeta.type === 'specific_vehicle' &&
      selectByMeta.vehicleId &&
      !carpoolOptions.isLoading
    ) {
      const exists = availableVehicleOptions.byId.get(selectByMeta.vehicleId)
      if (!exists) {
        setFieldValueAndValidate(issuanceForm, 'selectByMeta', {
          type: 'specific_vehicle',
          vehicleId: null,
        })
        schedulerRef.current?.onFormResourceChange(null)
      }
    }
  })

  useEffect(() => {
    resetVehicleRegistrationFieldIfNeeded()
  }, [availableVehicleOptions.byId])

  const resetVehicleCategoryFieldIfNeeded = useEffectEvent(() => {
    if (
      selectByMeta.type === 'auto_booking' &&
      selectByMeta.bookingVehicleCategoryId &&
      !carpoolOptions.isLoading
    ) {
      const exists = bookingVehicleCategoriesOptions.byId.get(
        selectByMeta.bookingVehicleCategoryId,
      )
      if (!exists) {
        setFieldValueAndValidate(issuanceForm, 'selectByMeta', {
          type: 'auto_booking',
          bookingVehicleCategoryId: null,
        })
        schedulerRef.current?.onFormResourceChange(null)
      }
    }
  })

  useEffect(() => {
    resetVehicleCategoryFieldIfNeeded()
  }, [bookingVehicleCategoriesOptions.array])

  const selectByMetaWithResources = useMemo((): SelectByMetaWithResources => {
    if (selectByMeta.type === 'specific_vehicle') {
      return {
        type: 'specific_vehicle',
        selectedVehicleId: selectByMeta.vehicleId,
        resources: availableVehicleOptions.array.map((o) => ({
          id: `vehicle_${o.id}`,
          name: o.label,
        })),
      }
    }

    return isFieldAlwaysHidden('selectByMeta.bookingVehicleCategoryId')
      ? {
          type: 'auto_booking',
          meta: 'autobooking_categories_disabled',
        }
      : {
          type: 'auto_booking',
          meta: {
            selectedBookingVehicleCategoryId: selectByMeta.bookingVehicleCategoryId,
            resources: bookingVehicleCategoriesOptions.array.map((o) => ({
              id: `category_${o.id}`,
              name: o.label,
              vehicleIds: o.vehicleIds,
            })),
          },
        }
  }, [
    selectByMeta,
    bookingVehicleCategoriesOptions.array,
    availableVehicleOptions.array,
    isFieldAlwaysHidden,
  ])

  const handleSchedulerResourceChange = (resourceId: BookingResourceId | null) => {
    if (resourceId === null) {
      setFieldValueAndValidate(
        issuanceForm,
        'selectByMeta',
        selectByMeta.type === 'auto_booking'
          ? {
              type: 'auto_booking',
              bookingVehicleCategoryId: null,
            }
          : { type: 'specific_vehicle', vehicleId: null },
      )

      return
    }

    const resourceEntityIdMeta = getBookingResourceEntityIdMeta(resourceId)
    if (
      resourceEntityIdMeta.type ===
      'implicitly_all_available_vehicles_due_to_no_category'
    ) {
      return
    }

    setFieldValueAndValidate(
      issuanceForm,
      'selectByMeta',
      resourceEntityIdMeta.type === 'vehicle'
        ? {
            type: 'specific_vehicle',
            vehicleId: resourceEntityIdMeta.id,
          }
        : {
            type: 'auto_booking',
            bookingVehicleCategoryId: resourceEntityIdMeta.id,
          },
    )
  }

  const locationOptions = useMemo(
    (): UseLocationsQueryData['locationsOptionsMeta'] =>
      locationsQueryData.locationsOptionsMeta,
    [locationsQueryData],
  )

  const handleDriverChange = (newDriverId: DriverId | null) => {
    setFieldValueAndValidate(issuanceForm, 'driverId', newDriverId)

    if (newDriverId) {
      const driverBookingTypes = getComputedAvailableBookingTypes(newDriverId)

      // If driver only has one option, make sure the form matches it
      if (
        driverBookingTypes === 'auto_booking_only' &&
        selectByMeta.type !== 'auto_booking'
      ) {
        setFieldValueAndValidate(issuanceForm, 'selectByMeta', {
          type: 'auto_booking',
          bookingVehicleCategoryId: null,
        })
        schedulerRef.current?.onFormResourceChange(null)
      } else if (
        driverBookingTypes === 'specific_vehicle_only' &&
        selectByMeta.type !== 'specific_vehicle'
      ) {
        setFieldValueAndValidate(issuanceForm, 'selectByMeta', {
          type: 'specific_vehicle',
          vehicleId: null,
        })
        schedulerRef.current?.onFormResourceChange(null)
      }
    }
  }

  const hasVehicleOptionsEmptyError =
    !disabled && R.isNonNullish(emptyVehicleErrorMessage?.message)

  const schedulerInitialSelection =
    useMemo((): VehicleBookingSchedulerProps['initialSelection'] => {
      if (meta.type === 'add') {
        return undefined
      }

      const resourceId = ((): NonNullable<
        VehicleBookingSchedulerProps['initialSelection']
      >['resourceId'] => {
        if (meta.initialValues.selectByMeta.type === 'specific_vehicle') {
          return `vehicle_${meta.initialValues.selectByMeta.vehicleId}`
        }

        return meta.initialValues.selectByMeta.bookingVehicleCategoryId
          ? `category_${meta.initialValues.selectByMeta.bookingVehicleCategoryId}`
          : 'implicitly_all_available_vehicles_due_to_no_category'
      })()

      return {
        type: 'edit',
        start: DateTime.fromJSDate(initialValues.pickupTime as Date),
        end: DateTime.fromJSDate(initialValues.dropoffTime as Date),
        resourceId,
        bookingId: meta.bookingId,
      }
    }, [meta, initialValues.pickupTime, initialValues.dropoffTime])

  const { isAnyVehicleAvailable } = useVehicleAvailability({
    pickupTimeMillisValue,
    dropoffTimeMillisValue,
    availableVehiclesSet,
    bookings,
    selectByMeta,
    meta,
    bookingVehicleCategoriesOptions,
  })

  return (
    <KarooFormStateContextProvider
      value={{ readOnly: (meta.type === 'edit' && meta.isKeyCollected) || disabled }}
    >
      <Box
        sx={{
          display: 'flex',
          flex: 1,
          height: '100%',
        }}
      >
        <Box
          sx={{
            width: `calc(100% - ${FORM_WIDTH}px)`,
            flexShrink: 0,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}
        >
          <VehicleBookingScheduler
            ref={schedulerRef}
            selectByMetaWithResources={selectByMetaWithResources}
            initialSelection={schedulerInitialSelection}
            onPickupTimeChange={(newTime: Date | null) => {
              setFieldValueAndValidate(issuanceForm, 'pickupTime', newTime)
            }}
            onDropoffTimeChange={(newTime: Date | null) => {
              setFieldValueAndValidate(issuanceForm, 'dropoffTime', newTime)
            }}
            onResourceChange={handleSchedulerResourceChange}
            emptySchedulerMessageKey={'carpool.noVehicle.criteria'}
            disabled={disabled}
            maxPickupDateTime={maxPickupDateTime}
            minPickupDateTime={minPickupDateTime}
            maxDuration={advanceBooking.maxDuration}
            availableVehiclesSet={availableVehiclesSet}
          />
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            p: 3,
            pt: 4,
            width: `${FORM_WIDTH}px`,
            minWidth: `${FORM_WIDTH}px`,
          }}
        >
          <Stack
            sx={{ mb: 3 }}
            justifyContent="space-between"
            flexDirection="row"
            alignItems="start"
          >
            {meta.type === 'edit' ? (
              <Box mb={2}>
                <Typography variant="h6">
                  {ctIntl.formatMessage({
                    id: disabled
                      ? 'carpool.bookingDetails.booking'
                      : 'carpool.list.addEditBooking.header',
                  })}
                </Typography>
                <Typography
                  sx={{ opacity: 0.6 }}
                  variant="h6"
                >
                  {meta.bookingId}
                </Typography>
              </Box>
            ) : (
              <Typography variant="h6">
                {ctIntl.formatMessage(
                  { id: 'carpool.issuanceRequestDrawer.newRequest' },
                  { values: { carpoolAppName: 'Carpool' } },
                )}
              </Typography>
            )}

            <IconButton
              onClick={onClose}
              aria-label="close"
              sx={{ mt: -0.5 }}
            >
              <CloseIcon />
            </IconButton>
          </Stack>

          <Box
            sx={{
              flex: 1,
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              pt: 1,
              mr: -3,
              pr: 3,
              gap: 2,
            }}
          >
            <Stack gap={2}>
              <issuanceForm.Field name="driverId">
                {(field) => {
                  const { isDirty, errors } = field.state.meta
                  const showError = isDirty && errors.length > 0
                  return (
                    <Autocomplete
                      size="small"
                      {...getAutocompleteVirtualizedProps({
                        options: driverOptions.array,
                      })}
                      onChange={(_, newValue) =>
                        handleDriverChange(newValue ? newValue.id : null)
                      }
                      value={
                        field.state.value
                          ? (driverOptions.byId.get(field.state.value) ?? null)
                          : null
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          required
                          label={ctIntl.formatMessage({ id: 'Driver' })}
                          error={showError}
                          helperText={
                            showError
                              ? ctIntl.formatMessage({ id: errors[0]?.message || '' })
                              : undefined
                          }
                        />
                      )}
                    />
                  )
                }}
              </issuanceForm.Field>

              <issuanceForm.Field name="requestDescription">
                {(field) => {
                  const { isDirty, errors } = field.state.meta
                  const showError = isDirty && errors.length > 0
                  return (
                    <TextField
                      disabled={disabled}
                      size="small"
                      value={field.state.value || ''}
                      onChange={(e) => field.handleChange(e.target.value)}
                      label={ctIntl.formatMessage({
                        id: 'carpool.requestDescription',
                      })}
                      fullWidth
                      variant="standard"
                      error={showError}
                      helperText={
                        showError
                          ? ctIntl.formatMessage({ id: errors[0]?.message || '' })
                          : undefined
                      }
                    />
                  )
                }}
              </issuanceForm.Field>

              <Stack
                gap={0}
                sx={{ my: 1 }}
              >
                <Typography variant="subtitle2">
                  {ctIntl.formatMessage({ id: 'carpool.bookingDetails.title' })}
                </Typography>
                <Typography variant="caption">
                  {ctIntl.formatMessage({
                    id: 'carpool.bookingDetails.instructions',
                  })}
                </Typography>
              </Stack>
              {currentAvailableBookingTypes === 'auto_booking_and_specific_vehicle' && (
                <issuanceForm.Field name="selectByMeta">
                  {(field) => {
                    const { isDirty, errors } = field.state.meta
                    const showError = isDirty && errors.length > 0
                    return (
                      <>
                        <ButtonGroup
                          variant="outlined"
                          sx={{ mb: 1, width: '100%' }}
                        >
                          <BookingTypeToggleButton
                            label={ctIntl.formatMessage({
                              id: 'Specific vehicle',
                            })}
                            isSelected={field.state.value.type === 'specific_vehicle'}
                            disabled={disabled}
                            onClick={() => {
                              field.handleChange({
                                type: 'specific_vehicle',
                                vehicleId: null,
                              })
                              schedulerRef.current?.onFormResourceChange(null)
                            }}
                          />
                          <BookingTypeToggleButton
                            label={ctIntl.formatMessage({
                              id: 'carpool.vehicleSelection.autoAllocated',
                            })}
                            isSelected={field.state.value.type === 'auto_booking'}
                            disabled={disabled}
                            onClick={() => {
                              field.handleChange({
                                type: 'auto_booking',
                                bookingVehicleCategoryId: null,
                              })
                              schedulerRef.current?.onFormResourceChange(
                                'implicitly_all_available_vehicles_due_to_no_category',
                              )
                            }}
                          />
                        </ButtonGroup>

                        {showError && (
                          <FormHelperText
                            error
                            sx={{ pb: 1 }}
                          >
                            {ctIntl.formatMessage({ id: errors[0]?.message || '' })}
                          </FormHelperText>
                        )}
                      </>
                    )
                  }}
                </issuanceForm.Field>
              )}
              <issuanceForm.Field name="locationId">
                {(field) => {
                  if (isFieldAlwaysHidden('locationId')) {
                    return null
                  }
                  const { isDirty, errors } = field.state.meta
                  const showError = isDirty && errors.length > 0
                  return (
                    <Stack>
                      <Stack gap={1}>
                        <Autocomplete
                          size="small"
                          disabled={disabled}
                          {...getAutocompleteVirtualizedProps({
                            options: locationOptions.array,
                          })}
                          onChange={(_, newValue) => {
                            field.handleChange(newValue ? newValue.id : null)
                          }}
                          value={
                            field.state.value
                              ? (locationOptions.byId.get(field.state.value) ?? null)
                              : null
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              required
                              label={translateFacilitiesTerm('Facility')}
                              error={showError}
                              helperText={
                                showError
                                  ? ctIntl.formatMessage({
                                      id: errors[0]?.message || '',
                                    })
                                  : undefined
                              }
                            />
                          )}
                        />
                        {locationOptions.array.length === 0 && (
                          <Typography
                            variant="overline"
                            sx={{ cursor: 'pointer', mt: -2, ml: 1 }}
                            onClick={() =>
                              history.push(LIST.subMenusRoutes.FACILITIES.path)
                            }
                          >
                            {translateFacilitiesTerm('facilities.addFacility')}
                          </Typography>
                        )}
                      </Stack>
                      <FormHelperText sx={{ ml: 2 }}>
                        {ctIntl.formatMessage({
                          id: 'carpool.vehicleLocation.parkedQuestion',
                        })}
                      </FormHelperText>
                    </Stack>
                  )
                }}
              </issuanceForm.Field>

              <issuanceForm.Field name="bookingPurposeId">
                {(field) => {
                  if (isFieldAlwaysHidden('bookingPurposeId')) {
                    return null
                  }
                  const { isDirty, errors } = field.state.meta
                  const showError = isDirty && errors.length > 0
                  return (
                    <Stack>
                      <Stack gap={1}>
                        <Autocomplete
                          disabled={disabled}
                          size="small"
                          {...getAutocompleteVirtualizedProps({
                            options: bookingPurposeOptions.array,
                          })}
                          onChange={(_, newValue) =>
                            field.handleChange(newValue ? newValue.id : null)
                          }
                          value={
                            field.state.value
                              ? (bookingPurposeOptions.byId.get(field.state.value) ??
                                null)
                              : null
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              required
                              label={ctIntl.formatMessage({
                                id: 'carpool.bookingPurpose',
                              })}
                              error={showError}
                              helperText={
                                showError
                                  ? ctIntl.formatMessage({
                                      id: errors[0]?.message || '',
                                    })
                                  : undefined
                              }
                            />
                          )}
                        />
                        {isEmpty(bookingPurposeOptions.array) &&
                          !carpoolOptions.isLoading && (
                            <Typography
                              variant="overline"
                              sx={{ cursor: 'pointer', mt: -2, ml: 1 }}
                              onClick={() =>
                                history.push(
                                  vehicleManagementLinksMeta.viewByGroup.linkPath,
                                )
                              }
                            >
                              {ctIntl.formatMessage({
                                id: 'Add Request Purposes',
                              })}
                            </Typography>
                          )}
                      </Stack>
                      <FormHelperText sx={{ ml: 2 }}>
                        {ctIntl.formatMessage({ id: 'What is this booking for?' })}
                      </FormHelperText>
                    </Stack>
                  )
                }}
              </issuanceForm.Field>

              <issuanceForm.Field name="selectByMeta">
                {(field) => {
                  if (isFieldAlwaysHidden('selectByMeta.bookingVehicleCategoryId')) {
                    return null
                  }
                  if (field.state.value.type === 'specific_vehicle') {
                    return null
                  }
                  const vehicleCategoryIdValue =
                    field.state.value.bookingVehicleCategoryId
                  const { isDirty, errors } = field.state.meta
                  const showError = isDirty && errors.length > 0
                  return (
                    <Autocomplete
                      size="small"
                      data-testid="vehicle-category-select"
                      {...getAutocompleteVirtualizedProps({
                        options: bookingVehicleCategoriesOptions.array,
                      })}
                      disabled={disabled}
                      onChange={(_, newValue) => {
                        field.handleChange({
                          type: 'auto_booking',
                          bookingVehicleCategoryId: newValue ? newValue.id : null,
                        })
                        schedulerRef.current?.onFormResourceChange(
                          newValue ? `category_${newValue.id}` : null,
                        )
                      }}
                      value={
                        vehicleCategoryIdValue
                          ? (bookingVehicleCategoriesOptions.byId.get(
                              vehicleCategoryIdValue,
                            ) ?? null)
                          : null
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          required
                          label={ctIntl.formatMessage({ id: 'Selected category' })}
                          error={showError}
                          helperText={
                            showError
                              ? ctIntl.formatMessage({
                                  id: errors[0]?.message || '',
                                })
                              : undefined
                          }
                        />
                      )}
                    />
                  )
                }}
              </issuanceForm.Field>

              <issuanceForm.Field name="selectByMeta">
                {(field) => {
                  if (field.state.value.type === 'auto_booking') {
                    return null
                  }
                  const vehicleIdValue = field.state.value.vehicleId
                  const { isDirty, errors } = field.state.meta
                  const showError = isDirty && errors.length > 0
                  return (
                    <Autocomplete
                      size="small"
                      data-testid="vehicle-select"
                      {...getAutocompleteVirtualizedProps({
                        options: availableVehicleOptions.array,
                      })}
                      onChange={(_, newValue) => {
                        field.handleChange({
                          type: 'specific_vehicle',
                          vehicleId: newValue ? newValue.id : null,
                        })
                        schedulerRef.current?.onFormResourceChange(
                          newValue ? `vehicle_${newValue.id}` : null,
                        )
                      }}
                      disabled={disabled}
                      value={
                        vehicleIdValue
                          ? (availableVehicleOptions.byId.get(vehicleIdValue) ?? null)
                          : null
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          required
                          label={ctIntl.formatMessage({
                            id: 'Vehicle registration',
                          })}
                          error={showError}
                          helperText={
                            showError
                              ? ctIntl.formatMessage({
                                  id: errors[0]?.message || '',
                                })
                              : undefined
                          }
                        />
                      )}
                    />
                  )
                }}
              </issuanceForm.Field>

              {hasVehicleOptionsEmptyError && (
                <Stack>
                  <Typography
                    sx={(theme) => ({ pt: 1, color: theme.palette.error.main })}
                  >
                    {ctIntl.formatMessage({
                      id: emptyVehicleErrorMessage?.message ?? '',
                    })}
                  </Typography>
                  {(emptyVehicleErrorMessage?.fieldMessages ?? []).map(
                    (fieldMessage) => (
                      <Typography
                        key={fieldMessage}
                        sx={(theme) => ({
                          pt: 1,
                          color: theme.palette.error.main,
                          ml: 3,
                          display: 'list-item',
                        })}
                      >
                        {ctIntl.formatMessage({
                          id: fieldMessage,
                        })}
                      </Typography>
                    ),
                  )}
                </Stack>
              )}

              {!isEmpty(availableTimeSlots) && (
                <AvailableTimeSlots
                  availableTimeSlots={availableTimeSlots}
                  selectedTimeSlotId={selectedTimeSlotId}
                  handleSelectTimeSlot={handleSelectTimeSlot}
                  onFormSelectAvailableTimeSlotChip={(timeRange) => {
                    setFieldValueAndValidate(
                      issuanceForm,
                      'pickupTime',
                      timeRange.startTime,
                    )
                    setFieldValueAndValidate(
                      issuanceForm,
                      'dropoffTime',
                      timeRange.endTime,
                    )
                    schedulerRef.current?.onFormSelectAvailableTimeSlotChip(timeRange)
                  }}
                />
              )}
            </Stack>

            <Stack
              gap={0}
              sx={{ my: 1 }}
            >
              <Typography variant="subtitle2">
                {ctIntl.formatMessage({ id: 'carpool.bookingTimings.title' })}
              </Typography>
              <Typography variant="caption">
                {ctIntl.formatMessage({
                  id: 'carpool.bookingTimings.instructions',
                })}
              </Typography>
            </Stack>
            <issuanceForm.Field name="pickupTime">
              {(field) => {
                const { isDirty, errors } = field.state.meta
                const showError = isDirty && errors.length > 0
                const value = field.state.value
                  ? DateTime.fromJSDate(field.state.value)
                  : null
                return (
                  <DateTimePicker
                    disabled={disabled}
                    value={value}
                    slotProps={{
                      textField: {
                        error: showError,
                        helperText: showError
                          ? ctIntl.formatMessage({ id: errors[0]?.message || '' })
                          : undefined,
                        required: true,
                      },
                    }}
                    label={ctIntl.formatMessage({ id: 'carpool.pickupTime' })}
                    onChange={(val) => {
                      const newValue = val ? val.toJSDate() : null
                      field.handleChange(newValue)
                      setSelectedTimeSlotId(null)
                      schedulerRef.current?.onFormPickupDateTimePickerChange(newValue)
                      // If user picks a new time, also re-validate
                      if (dropoffTimeMillisValue) {
                        issuanceForm.fieldInfo.dropoffTime.instance?.validate('change')
                      }
                      if (carpoolAllowBackDateBooking) {
                        issuanceForm.fieldInfo.selectByMeta.instance?.validate('change')
                      }
                    }}
                    maxDateTime={maxPickupDateTime}
                    minDateTime={minPickupDateTime}
                  />
                )
              }}
            </issuanceForm.Field>

            <issuanceForm.Field name="dropoffTime">
              {(field) => {
                const { isDirty, errors } = field.state.meta
                const showError = isDirty && errors.length > 0

                const value = field.state.value
                  ? DateTime.fromJSDate(field.state.value)
                  : null
                const minTime = pickupTimeMillisValue
                  ? DateTime.fromMillis(pickupTimeMillisValue)
                  : maxPickupDateTime
                const maxTime =
                  advanceBooking.maxDuration.value > 0 && pickupTimeMillisValue
                    ? DateTime.fromMillis(pickupTimeMillisValue).plus({
                        [advanceBooking.maxDuration.unit]:
                          advanceBooking.maxDuration.value,
                      })
                    : undefined

                return (
                  <DateTimePicker
                    disabled={disabled}
                    value={value}
                    slotProps={{
                      textField: {
                        error: showError,
                        helperText: showError
                          ? ctIntl.formatMessage({ id: errors[0]?.message || '' })
                          : '',
                        required: true,
                      },
                    }}
                    label={ctIntl.formatMessage({ id: 'carpool.dropoffTime' })}
                    onChange={(val) => {
                      const newValue = val ? val.toJSDate() : null
                      field.handleChange(newValue)
                      setSelectedTimeSlotId(null)
                      schedulerRef.current?.onFormDropoffDateTimePickerChange(newValue)
                    }}
                    minDateTime={minTime}
                    maxDateTime={maxTime}
                  />
                )
              }}
            </issuanceForm.Field>
          </Box>

          {!isAnyVehicleAvailable && (
            <Alert severity="error">
              <Typography>
                {ctIntl.formatMessage({
                  id: 'carpool.booking.noVehicleAvailable',
                })}
              </Typography>
            </Alert>
          )}

          <Box
            sx={{ display: 'flex', justifyContent: 'space-between', gap: 1.5, pt: 2 }}
          >
            <Button
              onClick={() => onClose()}
              variant="outlined"
              color="secondary"
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>
            <Button
              color="primary"
              variant="contained"
              disabled={
                !isFormValid ||
                isLoadingWhenSubmit ||
                disabled ||
                hasVehicleOptionsEmptyError ||
                !isAnyVehicleAvailable
              }
              loading={isLoadingWhenSubmit}
              onClick={() => issuanceForm.handleSubmit()}
              sx={{ flexGrow: 1 }}
            >
              {ctIntl.formatMessage({
                id:
                  selectByMeta.type === 'specific_vehicle'
                    ? 'carpool.booking.submit.specificVehicle'
                    : 'carpool.booking.submit.autoBook',
              })}
            </Button>
          </Box>
        </Box>
      </Box>
    </KarooFormStateContextProvider>
  )
}

export default IssuanceRequestDrawer
