import { useContext, useMemo } from 'react'
import { isEmpty } from 'lodash'
import { Autocomplete, Button, Stack, TextField, Typography } from '@karoo-ui/core'

import type { BookingVehicleCategoryId, DriverId, VehicleId } from 'api/types'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import {
  useDriversQuery,
  type UseDriversQueryData,
} from 'src/modules/api/useDriversQuery'
import {
  useVehiclesWithCarpoolMetaQuery,
  type UseVehiclesWithCarpoolMetaQueryData,
} from 'src/modules/api/useVehiclesQuery'
import { ctIntl } from 'src/util-components/ctIntl'

import { CarpoolOptionsContext } from '../'
import useConfirmKeyReturnMutation from '../api/useConfirmKeyReturnMutation'

type Props = {
  onClose: () => void
  onConfirm?: () => void
  bookingDetails: {
    bookingId: number
    driverId: DriverId
    vehicleCategoryId: BookingVehicleCategoryId | null
    vehicleId: VehicleId | null
  }
}

type BookingCategoryAutocompleteOption = {
  id: BookingVehicleCategoryId
  label: string
  value: BookingVehicleCategoryId
}

function KeyReturnModal({
  onClose,
  bookingDetails: { bookingId, driverId, vehicleCategoryId, vehicleId },
}: Props) {
  const { carpoolOptions } = useContext(CarpoolOptionsContext)
  const driversQuery = useDriversQuery()
  const vehiclesWithCarpoolMetaQuery = useVehiclesWithCarpoolMetaQuery()

  const mutation = useConfirmKeyReturnMutation()

  const driverOptions = useMemo(
    (): UseDriversQueryData['driversOptionsMeta']['activeDriversMeta'] =>
      driversQuery.data?.driversOptionsMeta.activeDriversMeta ?? {
        array: [],
        byId: new Map(),
      },
    [driversQuery.data],
  )

  const vehicleOptionsMeta = useMemo(
    (): UseVehiclesWithCarpoolMetaQueryData['looseCarpoolBookingVehicleOptionsMeta'] =>
      vehiclesWithCarpoolMetaQuery.data?.looseCarpoolBookingVehicleOptionsMeta ?? {
        array: [],
        byId: new Map(),
      },
    [vehiclesWithCarpoolMetaQuery.data],
  )

  const bookingCategoriesOptions = useMemo(() => {
    const array: Array<BookingCategoryAutocompleteOption> = []
    const byId = new Map<
      BookingCategoryAutocompleteOption['id'],
      BookingCategoryAutocompleteOption
    >()

    if (!isEmpty(carpoolOptions)) {
      for (const i of carpoolOptions.bookingCategories) {
        const option = { id: i.id, label: i.name, value: i.id }
        array.push(option)
        byId.set(option.id, option)
      }
    }
    return {
      array,
      byId,
    }
  }, [carpoolOptions])

  return (
    <ConfirmationModal
      title="carpool.list.keyReturn"
      open
      hasFooterButtons={false}
      onClose={onClose}
    >
      <Stack
        gap={2}
        mb={2}
      >
        <Typography>
          {ctIntl.formatMessage(
            {
              id: 'carpool.list.keyReturn.confirmKeyCheck',
            },
            {
              values: {
                bookingNumber: bookingId,
              },
            },
          )}
        </Typography>

        <Autocomplete
          size="small"
          {...getAutocompleteVirtualizedProps({
            options: driverOptions.array,
          })}
          readOnly
          value={driverOptions.byId.get(driverId) ?? null}
          renderInput={(params) => (
            <TextField
              {...params}
              label={ctIntl.formatMessage({ id: 'Driver' })}
            />
          )}
        />

        <Autocomplete
          size="small"
          {...getAutocompleteVirtualizedProps({
            options: vehicleOptionsMeta.array,
          })}
          readOnly
          value={vehicleId ? (vehicleOptionsMeta.byId.get(vehicleId) ?? null) : null}
          renderInput={(params) => (
            <TextField
              {...params}
              label={ctIntl.formatMessage({
                id: 'Vehicle registration',
              })}
            />
          )}
        />

        {!!vehicleCategoryId && (
          <Autocomplete
            size="small"
            {...getAutocompleteVirtualizedProps({
              options: bookingCategoriesOptions.array,
            })}
            value={bookingCategoriesOptions.byId.get(vehicleCategoryId) ?? null}
            readOnly
            renderInput={(params) => (
              <TextField
                {...params}
                label={ctIntl.formatMessage({
                  id: 'Vehicle category',
                })}
              />
            )}
          />
        )}
      </Stack>

      <Stack
        direction="row"
        justifyContent="flex-end"
        spacing={0.5}
      >
        <Button
          onClick={onClose}
          color="secondary"
          variant="text"
        >
          {ctIntl.formatMessage({
            id: 'Close',
          })}
        </Button>
        <Button
          color="primary"
          variant="text"
          loading={mutation.isPending}
          onClick={() => {
            mutation.mutate(
              { bookingId },
              {
                onSuccess() {
                  onClose()
                },
              },
            )
          }}
        >
          {ctIntl.formatMessage({ id: 'Confirm' })}
        </Button>
      </Stack>
    </ConfirmationModal>
  )
}

export default KeyReturnModal
