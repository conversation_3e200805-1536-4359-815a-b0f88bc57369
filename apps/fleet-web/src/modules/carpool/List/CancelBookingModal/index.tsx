import { useContext, useMemo } from 'react'
import { isEmpty } from 'lodash'
import { Autocomplete, Button, Stack, TextField, Typography } from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod/v4'

import {
  bookingCancellationReasonIdSchema,
  type BookingCancellationReasonId,
} from 'api/types'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { messages } from 'src/shared/forms/messages'
import { ctIntl } from 'src/util-components/ctIntl'

import { CarpoolOptionsContext } from '../'
import useCancelBookingMutation from '../api/useCancelBookingMutation'

const schema = z.object({
  reasonId: bookingCancellationReasonIdSchema.asFormField({
    requiredErrorMsg: messages.required,
  }),
  notes: z.string().nullable(),
})

type Schema = z.infer<typeof schema>

type FormPossibleValues = {
  reasonId: Schema['reasonId'] | null
  notes: Schema['notes']
}

type Props = {
  onClose: () => void
  onConfirm?: (reason: string) => void
  bookingIds: Array<string | number>
}

type CancelReasonAutocompleteOption = { id: BookingCancellationReasonId; label: string }

function CancelBookingModal({ onClose, bookingIds }: Props) {
  const { carpoolOptions } = useContext(CarpoolOptionsContext)
  const cancelBookingMutation = useCancelBookingMutation()

  const initialValues: FormPossibleValues = {
    reasonId: null,
    notes: '',
  }

  const {
    control,
    handleSubmit,
    setValue: setFormValue,
  } = useForm<FormPossibleValues>({
    resolver: zodResolverV4(schema),
    mode: 'all',
    defaultValues: initialValues,
  })

  const cancelReasonOptions = useMemo(() => {
    const array: Array<CancelReasonAutocompleteOption> = []
    const byId = new Map<
      CancelReasonAutocompleteOption['id'],
      CancelReasonAutocompleteOption
    >()

    if (!isEmpty(carpoolOptions)) {
      for (const j of carpoolOptions.cancelReasons) {
        const option = { id: j.id, label: j.reason }
        array.push(option)
        byId.set(option.id, option)
      }
    }
    return {
      array,
      byId,
    }
  }, [carpoolOptions])

  const submitForm = handleSubmit((_values) => {
    const values = _values as Schema

    const { reasonId, notes } = values

    cancelBookingMutation.mutate(
      {
        bookingIds: bookingIds as Array<string>,
        bookingCancelReasonId: reasonId,
        bookingCancelNotes: notes as string,
      },
      {
        onSuccess() {
          onClose()
        },
      },
    )
  })

  return (
    <ConfirmationModal
      title="carpool.list.cancelBooking"
      open
      hasFooterButtons={false}
      onClose={onClose}
    >
      <form onSubmit={submitForm}>
        <Stack
          gap={2}
          mb={2}
        >
          <Typography mb={2}>
            {ctIntl.formatMessage(
              { id: 'carpool.list.cancelBooking.selectReason' },
              {
                values: {
                  bookingNumber: bookingIds.join(', '),
                },
              },
            )}
          </Typography>
          <Controller
            control={control}
            name="reasonId"
            render={({ field, fieldState }) => (
              <Autocomplete
                size="small"
                {...getAutocompleteVirtualizedProps({
                  options: cancelReasonOptions.array,
                })}
                onChange={(_, newValue) => {
                  setFormValue(field.name, newValue ? newValue.id : null, {
                    shouldValidate: true,
                  })
                }}
                value={
                  field.value
                    ? (cancelReasonOptions.byId.get(field.value) ?? null)
                    : null
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    required
                    label={ctIntl.formatMessage({
                      id: 'carpool.list.cancelBooking.selectReasonLabel',
                    })}
                    helperText={ctIntl.formatMessage({
                      id: fieldState.error?.message ?? '',
                    })}
                    error={!!fieldState.error}
                  />
                )}
              />
            )}
          />

          <TextFieldControlled
            size="small"
            ControllerProps={{
              name: 'notes',
              control,
            }}
            label={`${ctIntl.formatMessage({ id: 'Notes' })} (${ctIntl.formatMessage({
              id: 'Optional',
            })})`}
            fullWidth
            variant="standard"
          />
        </Stack>

        <Stack
          direction="row"
          justifyContent="flex-end"
          spacing={0.5}
        >
          <Button
            onClick={onClose}
            color="secondary"
            variant="text"
          >
            {ctIntl.formatMessage({
              id: 'Close',
            })}
          </Button>
          <Button
            color="primary"
            variant="text"
            type="submit"
            loading={cancelBookingMutation.isPending}
          >
            {ctIntl.formatMessage({ id: 'Confirm' })}
          </Button>
        </Stack>
      </form>
    </ConfirmationModal>
  )
}

export default CancelBookingModal
