import {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useState,
  type ReactElement,
} from 'react'
import { isEmpty, isNil, toString, uniq } from 'lodash'
import {
  Box,
  Button,
  CircularProgressDelayedCentered,
  ContainerWithTabsForDataGrid,
  DataGridAsTabItem,
  GridActionsCellItem,
  GridToolbarWithQuickFilter,
  LinearProgress,
  MenuItem,
  MenuList,
  Popover,
  Tooltip,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
  type GridColDef,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import InfoIcon from '@mui/icons-material/Info'
import KeyIcon from '@mui/icons-material/Key'
import ThreeDotsIcon from '@mui/icons-material/MoreVert'
import TaskAltOutlinedIcon from '@mui/icons-material/TaskAltOutlined'
import { DateTime } from 'luxon'
import PopupState, { bindPopover, bindTrigger } from 'material-ui-popup-state'
import { useHistory } from 'react-router'
import { match } from 'ts-pattern'
import type { ValueOf } from 'type-fest'

import type { BookingVehicleCategoryId, DriverId, VehicleId } from 'api/types'
import {
  getAuthenticatedUser,
  getCarpoolEditBookingsSetting,
  getSettings_UNSAFE,
} from 'duxs/user'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import {
  useVehiclesWithCarpoolMetaQuery,
  type UseVehiclesWithCarpoolMetaQueryData,
} from 'src/modules/api/useVehiclesQuery'
import { getIssuanceRequestDrawerMainPath } from 'src/modules/carpool/components/IssuanceRequestDrawer/schema'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import CarpoolStatusChip from '../components/CarpoolStatusChip'
import { NewCarpoolRequestButtonMeta } from '../components/NewCarpoolRequestButton'
import StatBar from '../components/stat-bar'
import useCarpoolOptionsQuery, {
  type FetchCarpoolOptionsParsedData,
} from '../queries/useCarpoolOptionsQuery'
import { BookingStartStopMethod } from '../Settings/BookingManagement/api/types'
import {
  useBookingConfigurationQuery,
  type UseBookingConfigurationQueryData,
} from '../Settings/BookingManagement/api/useBookingConfigurationQuery'
import { BookingStatus, CustomTabs } from '../utils/constants'
import useBookingSubmissionsQuery, {
  type FormattedSubmitInfo,
} from './api/useBookingSubmissionsQuery'
import useIssuanceListQuery, {
  type Booking,
  type FetchIssuanceList,
  type Metric,
} from './api/useIssuanceListQuery'
import ApproveBookingModal from './ApproveBookingModal'
import BookingDetailsDrawer from './BookingDetailsDrawer'
import BookingSubmissionsChip from './BookingSubmissionsChip'
import CancelBookingModal from './CancelBookingModal'
import ChangeToActiveConfirmationModal from './ChangeToActiveModal'
import CompleteBookingModal from './CompleteBookingModal'
import DeclineBookingModal from './DeclineBookingModal'
import ForceTerminateBookingModal from './ForceTerminateBookingModal'
import KeyCollectionModal from './KeyCollectionModal'
import KeyReturnModal from './KeyReturnModal'

const LIST_TABS = {
  SCHEDULED: 'scheduled',
  IN_PROGRESS: 'inProgress',
  HISTORY: 'history',
  CUSTOM: 'custom',
} as const

export const CarpoolOptionsContext = createContext({
  carpoolOptions: {} as FetchCarpoolOptionsParsedData,
})

const TAB_OPTIONS: Array<{
  label: string
  value: ValueOf<typeof LIST_TABS>
}> = [
  {
    label: ctIntl.formatMessage({ id: 'Scheduled' }),
    value: LIST_TABS.SCHEDULED,
  },
  {
    label: ctIntl.formatMessage({ id: 'In Progress' }),
    value: LIST_TABS.IN_PROGRESS,
  },
  {
    label: ctIntl.formatMessage({ id: 'History' }),
    value: LIST_TABS.HISTORY,
  },
]

const metricConfig: Array<{ key: Metric; label: string; tab?: CustomTabs }> = [
  { key: 'TOTAL', label: 'Total' },
  { key: 'ACTIVE', label: 'Active', tab: CustomTabs.ACTIVE },
  {
    key: 'ACTIVE_ALMOST_LATE',
    label: 'Active Almost Late',
    tab: CustomTabs.ACTIVE_ALMOST_LATE,
  },
  { key: 'ACTIVE_LATE', label: 'Active Late', tab: CustomTabs.ACTIVE_LATE },
  { key: 'REQUESTED', label: 'Requested', tab: CustomTabs.REQUESTED },
  {
    key: 'EXPIRING_APPROVAL',
    label: 'Expiring Approval',
    tab: CustomTabs.EXPIRING_APPROVAL,
  },
  { key: 'APPROVED', label: 'global.approved', tab: CustomTabs.APPROVED },
  {
    key: 'DECLINED',
    label: 'carpool.list.timeline.declined',
    tab: CustomTabs.DECLINED,
  },
  { key: 'CANCELLED', label: 'Cancelled', tab: CustomTabs.CANCELLED },
  { key: 'FREE', label: 'Free', tab: CustomTabs.FREE },
  { key: 'RETURNED', label: 'Returned', tab: CustomTabs.RETURNED },
  { key: 'RETURNED_LATE', label: 'Returned Late', tab: CustomTabs.RETURNED_LATE },
  {
    key: 'FORCE_TERMINATED',
    label: 'Force Terminated',
    tab: CustomTabs.FORCE_TERMINATED,
  },
] as const

const customTabAndStatusMapping = {
  [CustomTabs.APPROVED]: BookingStatus.BOOKING_STATUS_APPROVED,
  [CustomTabs.REQUESTED]: BookingStatus.BOOKING_STATUS_REQUESTED,
  [CustomTabs.DECLINED]: BookingStatus.BOOKING_STATUS_DECLINED,
  [CustomTabs.EXPIRING_APPROVAL]: BookingStatus.BOOKING_STATUS_EXPIRING_APPROVAL,
  [CustomTabs.CANCELLED]: BookingStatus.BOOKING_STATUS_CANCELLED,
  [CustomTabs.ACTIVE]: BookingStatus.BOOKING_STATUS_ACTIVE,
  [CustomTabs.ACTIVE_ALMOST_LATE]: BookingStatus.BOOKING_STATUS_ACTIVE_ALMOST_LATE,
  [CustomTabs.ACTIVE_LATE]: BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
  [CustomTabs.FREE]: BookingStatus.BOOKING_STATUS_FREE,
  [CustomTabs.RETURNED]: BookingStatus.BOOKING_STATUS_RETURNED,
  [CustomTabs.RETURNED_LATE]: BookingStatus.BOOKING_STATUS_RETURNED_LATE,
  [CustomTabs.FORCE_TERMINATED]: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
}

type IssuanceItem = Booking & {
  formSubmissions: Array<FormattedSubmitInfo>
}

function List() {
  const bookingConfigurationQuery = useBookingConfigurationQuery()
  const vehiclesWithCarpoolMetaQuery = useVehiclesWithCarpoolMetaQuery()

  if (!vehiclesWithCarpoolMetaQuery.data) {
    return <CircularProgressDelayedCentered />
  }

  return match(bookingConfigurationQuery)
    .with({ status: 'pending' }, () => <CircularProgressDelayedCentered />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, ({ data }) => (
      <Content
        bookingConfigurationQueryData={data}
        vehiclesWithCarpoolMetaQueryData={vehiclesWithCarpoolMetaQuery.data}
      />
    ))
    .exhaustive()
}

type ContentProps = {
  bookingConfigurationQueryData: UseBookingConfigurationQueryData
  vehiclesWithCarpoolMetaQueryData: UseVehiclesWithCarpoolMetaQueryData
}
const Content = ({
  bookingConfigurationQueryData,
  vehiclesWithCarpoolMetaQueryData,
}: ContentProps) => {
  const columnHelper = useDataGridColumnHelper<IssuanceItem>({ filterMode: 'client' })

  const categoriesIsEnabled =
    bookingConfigurationQueryData.vehicleManagementConfig
      .vehicleCategoriesAndBookingPurposes

  const { vehicleById } = vehiclesWithCarpoolMetaQueryData

  const history = useHistory()
  const [currentModal, setCurrentModal] = useState<
    | {
        type: 'key_return'
        data: {
          bookingDetails: {
            bookingId: number
            driverId: DriverId
            vehicleCategoryId: BookingVehicleCategoryId | null
            vehicleId: VehicleId | null
          }
        }
      }
    | {
        type: 'force_terminate_booking'
        data: { bookingIds: Array<string | number> }
      }
    | {
        type: 'cancel_booking'
        data: { bookingIds: Array<string | number> }
      }
    | {
        type: 'decline_booking'
        data: { bookingIds: Array<string | number> }
      }
    | {
        type: 'complete_booking'
        data: { bookingIds: Array<string> }
      }
    | {
        type: 'approve_booking'
        data: { bookingIds: Array<string | number> }
      }
    | {
        type: 'key_collection'
        data: {
          bookingDetails: {
            bookingId: number
            driverId: DriverId
            vehicleCategoryId: BookingVehicleCategoryId | null
            vehicleId: VehicleId | null
          }
        }
      }
    | {
        type: 'change_to_active'
        data: { bookingIds: Array<string | number> }
      }
    | null
  >(null)

  const [currentDrawer, setCurrentDrawer] = useState<{
    type: 'booking_details'
    data: {
      bookingId: number
      vehicleTimelineParams: {
        vehicleId: VehicleId | null
        startDate: string
        endDate: string
      }
    }
  } | null>(null)

  const [issuanceList, setIssuanceList] = useState<Array<IssuanceItem>>([])
  const [tabs, setTabs] = useState<
    ReadonlyArray<{
      label: string
      value: ValueOf<typeof LIST_TABS>
      icon?: ReactElement
      iconPosition?: 'bottom' | 'top' | 'end' | 'start' | undefined
    }>
  >(TAB_OPTIONS)
  const [currentTab, setCurrentTab] = useState(TAB_OPTIONS[0].value)
  const [customSelection, setCustomSelection] = useState<CustomTabs | ''>('')
  const [multiSelectedBookingIds, setMultiSelectedBookingIds] = useState<
    Array<string | number>
  >([])

  const {
    carpoolApproveBookings,
    carpoolDeclineBookings,
    carpoolChangeBookingToActive,
    carpoolCancelBooking,
    carpoolForceTerminateBooking,
    isAdmin,
  } = useTypedSelector(getSettings_UNSAFE)
  const carpoolEditBookings = useTypedSelector(getCarpoolEditBookingsSetting)
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)

  const issuanceListQuery = useIssuanceListQuery()
  const bookingSubmissionsQuery = useBookingSubmissionsQuery()
  const bookingOptionQuery = useCarpoolOptionsQuery()
  const carpoolVehiclesQuery = useVehiclesWithCarpoolMetaQuery()

  const keyCollectionRule =
    bookingConfigurationQueryData.availableMethods.find(
      (method) => method.type === BookingStartStopMethod.KEY_IS_COLLECTED_OR_RETURNED,
    )?.enabledMeta.status === 'enabled'

  useEffect(() => {
    if (issuanceListQuery.data) {
      const submissionMap = bookingSubmissionsQuery.data
      setIssuanceList(
        issuanceListQuery.data.bookings.map((issuance) => ({
          ...issuance,
          formSubmissions: submissionMap ? (submissionMap.get(issuance.id) ?? []) : [],
        })),
      )
    }
  }, [issuanceListQuery.data, bookingSubmissionsQuery.data])

  const columnsGetters = useMemo(
    () => ({
      bookingNumber: (booking: IssuanceItem) => booking.id,
      vehicle: (booking: IssuanceItem) => booking.vehicleRegistration ?? '',
      driver: (booking: IssuanceItem) => booking.driverName ?? '',
      vehicleCategory: (booking: IssuanceItem) => booking.vehicleCategory ?? '',
      purpose: (booking: IssuanceItem) => booking.purpose ?? '',
      requestDate: (booking: IssuanceItem) =>
        booking.requestDate ? new Date(booking.requestDate) : null,
      startDate: (booking: IssuanceItem) =>
        booking.startDate ? new Date(booking.startDate) : null,
      pickUpAt: (booking: IssuanceItem) =>
        booking.pickupIgnitionTime ? new Date(booking.pickupIgnitionTime) : null,
      endDate: (booking: IssuanceItem) =>
        booking.endDate ? new Date(booking.endDate) : null,
      returnedAt: (booking: IssuanceItem) => {
        if (
          [
            BookingStatus.BOOKING_STATUS_RETURNED,
            BookingStatus.BOOKING_STATUS_RETURNED_LATE,
            BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
          ].includes(booking.statusId as BookingStatus)
        ) {
          return booking.returnedIgnitionTime
            ? new Date(booking.returnedIgnitionTime)
            : null
        } else {
          return null
        }
      },
      statusId: (booking: IssuanceItem) => booking.statusId,
      status: (booking: IssuanceItem) => booking.status,
      approvedBy: (booking: IssuanceItem) => booking.approvedBy,
      declinedBy: (booking: IssuanceItem) => booking.declinedBy,
    }),
    [],
  )

  const canApproveBooking = useCallback(
    (pendingManagers: Array<Array<string>>) =>
      isAdmin ||
      isEmpty(pendingManagers) ||
      pendingManagers[0].includes(clientUserId ?? ''),
    [clientUserId, isAdmin],
  )

  const renderCompleteAction = useCallback(
    (bookingId: number) => (
      <Tooltip
        title={ctIntl.formatMessage({
          id: 'carpool.list.complete',
        })}
        arrow
        key="complete"
      >
        <span>
          <GridActionsCellItem
            label=""
            icon={<TaskAltOutlinedIcon />}
            onClick={() =>
              setCurrentModal({
                type: 'complete_booking',
                data: {
                  bookingIds: [toString(bookingId)],
                },
              })
            }
          />
        </span>
      </Tooltip>
    ),
    [],
  )

  const renderForceTerminateAction = useCallback(
    (bookingId: number) => (
      <Tooltip
        title={ctIntl.formatMessage({
          id: 'carpool.list.forceTerminateBooking',
        })}
        arrow
        key="forceTerminate"
      >
        <span>
          <GridActionsCellItem
            label=""
            icon={<CloseIcon />}
            onClick={() => {
              setCurrentModal({
                type: 'force_terminate_booking',
                data: {
                  bookingIds: [toString(bookingId)],
                },
              })
            }}
            disabled={!carpoolForceTerminateBooking}
          />
        </span>
      </Tooltip>
    ),
    [carpoolForceTerminateBooking],
  )

  const generateIssuanceActions = useCallback(
    (row: IssuanceItem) => {
      const {
        statusId,
        id: bookingId,
        driverId,
        vehicleId,
        vehicleCategoryId,
        startDate,
        endDate,
        keyCollectionDate,
        keyReturnDate,
        pickupIgnitionTime,
        returnedIgnitionTime,
      } = row
      const carpoolStartStopBookingMethod =
        carpoolVehiclesQuery.data && vehicleId
          ? carpoolVehiclesQuery.data.carpoolVehiclesMapById.get(vehicleId)
              ?.carpoolStartStopBookingMethod
          : null

      const actualStartAndEndTime = match(toString(statusId))
        .returnType<{
          startDate: string
          endDate: string
        }>()
        .with(
          BookingStatus.BOOKING_STATUS_ACTIVE,
          BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          BookingStatus.BOOKING_STATUS_ACTIVE_ALMOST_LATE,
          () => ({
            startDate: pickupIgnitionTime || startDate,
            endDate: DateTime.now().toString(),
          }),
        )
        .with(
          BookingStatus.BOOKING_STATUS_RETURNED,
          BookingStatus.BOOKING_STATUS_RETURNED_LATE,
          () => ({
            startDate: pickupIgnitionTime || startDate,
            endDate: returnedIgnitionTime || endDate,
          }),
        )
        .otherwise(() => ({
          startDate: pickupIgnitionTime || startDate,
          endDate,
        }))

      const vehicleTimelineParams = {
        vehicleId,
        ...actualStartAndEndTime,
      }

      if (
        [
          BookingStatus.BOOKING_STATUS_REQUESTED,
          BookingStatus.BOOKING_STATUS_EXPIRING_APPROVAL,
        ].includes(toString(statusId) as BookingStatus)
      ) {
        return [
          <GridActionsCellItem
            key="approve"
            label={ctIntl.formatMessage({ id: 'Approve' })}
            onClick={() =>
              setCurrentModal({
                type: 'approve_booking',
                data: {
                  bookingIds: [toString(bookingId)],
                },
              })
            }
            disabled={
              !carpoolApproveBookings || !canApproveBooking(row.pendingManagers)
            }
            showInMenu
          />,
          <GridActionsCellItem
            key="edit"
            label={ctIntl.formatMessage({ id: 'Edit' })}
            onClick={() =>
              history.push(
                getIssuanceRequestDrawerMainPath(history.location, {
                  type: 'edit',
                  id: toString(bookingId),
                }),
              )
            }
            disabled={!carpoolEditBookings}
            showInMenu
          />,
          <GridActionsCellItem
            key="decline"
            label={ctIntl.formatMessage({ id: 'Decline' })}
            onClick={() =>
              setCurrentModal({
                type: 'decline_booking',
                data: {
                  bookingIds: [toString(bookingId)],
                },
              })
            }
            disabled={!carpoolDeclineBookings}
            showInMenu
          />,
        ]
      }

      if (toString(statusId) === BookingStatus.BOOKING_STATUS_APPROVED) {
        return [
          keyCollectionRule &&
          carpoolStartStopBookingMethod === 'key_is_collected_or_returned' ? (
            <Tooltip
              title={ctIntl.formatMessage({
                id: isNil(keyCollectionDate)
                  ? 'carpool.list.collectKey'
                  : 'carpool.list.keyIsCollected',
              })}
              key="pickupKey"
            >
              <span>
                <GridActionsCellItem
                  icon={<KeyIcon />}
                  label=""
                  onClick={() =>
                    setCurrentModal({
                      type: 'key_collection',
                      data: {
                        bookingDetails: {
                          bookingId,
                          driverId,
                          vehicleCategoryId,
                          vehicleId,
                        },
                      },
                    })
                  }
                  disabled={!isNil(keyCollectionDate)}
                />
              </span>
            </Tooltip>
          ) : (
            <></>
          ),
          <GridActionsCellItem
            key="changeToActive"
            label={ctIntl.formatMessage({ id: 'carpool.list.changeToActive' })}
            onClick={() =>
              setCurrentModal({
                type: 'change_to_active',
                data: {
                  bookingIds: [toString(bookingId)],
                },
              })
            }
            showInMenu
            disabled={!carpoolChangeBookingToActive}
          />,
          <GridActionsCellItem
            key="edit"
            label={ctIntl.formatMessage({ id: 'carpool.list.viewBookingDetail' })}
            onClick={() =>
              history.push(
                getIssuanceRequestDrawerMainPath(history.location, {
                  type: 'edit',
                  id: toString(bookingId),
                }),
              )
            }
            showInMenu
          />,
          <GridActionsCellItem
            key="cancel"
            label={ctIntl.formatMessage({ id: 'Cancel' })}
            onClick={() =>
              setCurrentModal({
                type: 'cancel_booking',
                data: {
                  bookingIds: [toString(bookingId)],
                },
              })
            }
            showInMenu
            disabled={!carpoolCancelBooking}
          />,
        ]
      }
      if (
        [
          BookingStatus.BOOKING_STATUS_ACTIVE,
          BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          BookingStatus.BOOKING_STATUS_ACTIVE_ALMOST_LATE,
        ].includes(toString(statusId) as BookingStatus)
      ) {
        const additionalAction = (() => {
          const bookingVehicle = row.vehicleId ? vehicleById.get(row.vehicleId) : null
          if (!bookingVehicle) {
            return <></>
          }
          return match(bookingVehicle.carpoolStartStopBookingMethod)
            .with(
              'pre_post_driver_checklist_is_completed',
              'geofence_is_crossed',
              'vehicle_is_locked_or_unlocked_with_mikey',
              () => <>{renderForceTerminateAction(bookingId)}</>,
            )
            .with('key_is_collected_or_returned', () => (
              <>
                {renderForceTerminateAction(bookingId)}
                {keyCollectionRule ? (
                  <Tooltip
                    title={ctIntl.formatMessage({
                      id: 'carpool.list.returnKey',
                    })}
                    arrow
                    key="returnKey"
                  >
                    <span>
                      <GridActionsCellItem
                        icon={<KeyIcon />}
                        label=""
                        onClick={() => {
                          setCurrentModal({
                            type: 'key_return',
                            data: {
                              bookingDetails: {
                                bookingId,
                                driverId,
                                vehicleCategoryId,
                                vehicleId,
                              },
                            },
                          })
                        }}
                      />
                    </span>
                  </Tooltip>
                ) : (
                  <></>
                )}
              </>
            ))
            .with('manually_from_app', () => (
              <>
                {renderCompleteAction(bookingId)}
                {renderForceTerminateAction(bookingId)}
              </>
            ))
            .with(null, () => (
              <>
                {renderCompleteAction(bookingId)}
                {renderForceTerminateAction(bookingId)}
              </>
            ))
            .exhaustive()
        })()
        return [
          additionalAction,
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Info' })}
            key="info"
          >
            <span>
              <GridActionsCellItem
                icon={<InfoIcon />}
                label=""
                onClick={() =>
                  setCurrentDrawer({
                    type: 'booking_details',
                    data: {
                      bookingId,
                      vehicleTimelineParams,
                    },
                  })
                }
              />
            </span>
          </Tooltip>,
        ]
      }
      if (
        [
          BookingStatus.BOOKING_STATUS_CANCELLED,
          BookingStatus.BOOKING_STATUS_RETURNED,
          BookingStatus.BOOKING_STATUS_RETURNED_LATE,
        ].includes(toString(statusId) as BookingStatus)
      ) {
        return [
          keyCollectionRule ? (
            <Tooltip
              title={ctIntl.formatMessage({
                id: isNil(keyReturnDate)
                  ? 'carpool.list.returnKey'
                  : 'carpool.list.keyIsReturned',
              })}
              key="returnKey"
            >
              <span>
                <GridActionsCellItem
                  icon={<KeyIcon />}
                  label=""
                  onClick={() => {
                    setCurrentModal({
                      type: 'key_return',
                      data: {
                        bookingDetails: {
                          bookingId,
                          driverId,
                          vehicleCategoryId,
                          vehicleId,
                        },
                      },
                    })
                  }}
                  disabled={!isNil(keyReturnDate) || isNil(keyCollectionDate)}
                />
              </span>
            </Tooltip>
          ) : (
            <></>
          ),
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Info' })}
            key="info"
          >
            <span>
              <GridActionsCellItem
                icon={<InfoIcon />}
                label={ctIntl.formatMessage({ id: 'Info' })}
                onClick={() =>
                  setCurrentDrawer({
                    type: 'booking_details',
                    data: {
                      bookingId,
                      vehicleTimelineParams,
                    },
                  })
                }
              />
            </span>
          </Tooltip>,
        ]
      }

      if (
        [
          BookingStatus.BOOKING_STATUS_DECLINED,
          BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
        ].includes(toString(statusId) as BookingStatus)
      ) {
        return [
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Info' })}
            key="info"
          >
            <span>
              <GridActionsCellItem
                icon={<InfoIcon />}
                label={ctIntl.formatMessage({ id: 'Info' })}
                onClick={() =>
                  setCurrentDrawer({
                    type: 'booking_details',
                    data: {
                      bookingId,
                      vehicleTimelineParams,
                    },
                  })
                }
              />
            </span>
          </Tooltip>,
        ]
      }

      return []
    },
    [
      canApproveBooking,
      carpoolApproveBookings,
      carpoolCancelBooking,
      carpoolChangeBookingToActive,
      carpoolDeclineBookings,
      carpoolEditBookings,
      carpoolVehiclesQuery,
      history,
      keyCollectionRule,
      renderCompleteAction,
      renderForceTerminateAction,
      vehicleById,
    ],
  )

  const columns = useMemo(
    (): Array<GridColDef<IssuanceItem>> => [
      columnHelper.number((_, row) => columnsGetters.bookingNumber(row), {
        field: 'bookingNumber',
        headerName: ctIntl.formatMessage({ id: 'carpool.list.bookingNumber' }),
        align: 'left',
        headerAlign: 'left',
      }),
      columnHelper.string((_, row) => columnsGetters.vehicle(row), {
        field: 'vehicle',
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        flex: 1,
      }),
      columnHelper.string((_, row) => columnsGetters.driver(row), {
        field: 'driver',
        headerName: ctIntl.formatMessage({ id: 'Driver' }),
        flex: 1,
      }),
      ...(categoriesIsEnabled
        ? [
            columnHelper.string((_, row) => columnsGetters.vehicleCategory(row), {
              field: 'vehicleCategory',
              headerName: ctIntl.formatMessage({ id: 'carpool.vehicleCategory' }),
            }),
            columnHelper.string((_, row) => columnsGetters.purpose(row), {
              field: 'purpose',
              headerName: ctIntl.formatMessage({ id: 'carpool.list.purpose' }),
            }),
          ]
        : []),
      columnHelper.dateTime({
        field: 'requestDate',
        headerName: ctIntl.formatMessage({ id: 'Request Date' }),
        valueGetter: (_, row) => columnsGetters.requestDate(row),
      }),
      columnHelper.dateTime({
        field: 'startDate',
        headerName: ctIntl.formatMessage({ id: 'Start Date' }),
        valueGetter: (_, row) => columnsGetters.startDate(row),
      }),
      columnHelper.dateTime({
        field: 'pickUpAt',
        headerName: ctIntl.formatMessage({ id: 'Pick Up At' }),
        valueGetter: (_, row) => columnsGetters.pickUpAt(row),
      }),
      columnHelper.dateTime({
        field: 'endDate',
        headerName: ctIntl.formatMessage({ id: 'End Date' }),
        valueGetter: (_, row) => columnsGetters.endDate(row),
      }),
      columnHelper.dateTime({
        field: 'returnedAt',
        headerName: ctIntl.formatMessage({ id: 'Returned At' }),
        valueGetter: (_, row) => columnsGetters.returnedAt(row),
      }),
      columnHelper.valueGetter((_, row) => columnsGetters.status(row), {
        field: 'status',
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        renderCell: ({ row }) => (
          <CarpoolStatusChip
            bookingStatusId={columnsGetters.statusId(row) as BookingStatus}
            approvalProcess={{
              approvalsNeeded: row.approvalsNeeded,
              approvedCount: row.approvedCount,
            }}
          />
        ),
        minWidth: 200,
      }),
      columnHelper.string((_, row) => columnsGetters.approvedBy(row), {
        field: 'approvedBy',
        headerName: ctIntl.formatMessage({ id: 'Approved by' }),
      }),
      columnHelper.string((_, row) => columnsGetters.declinedBy(row), {
        field: 'declinedBy',
        headerName: ctIntl.formatMessage({ id: 'Declined by' }),
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        minWidth: 120,
        align: 'right',
        getActions: ({ row }) => [
          <BookingSubmissionsChip
            key="submissions-info"
            formSubmissions={row.formSubmissions}
            isLoading={bookingSubmissionsQuery.isPending}
          />,
          ...generateIssuanceActions(row),
        ],
      },
    ],
    [
      columnHelper,
      columnsGetters,
      bookingSubmissionsQuery.isPending,
      generateIssuanceActions,
      categoriesIsEnabled,
    ],
  )

  const handleCloseCustomTab = useCallback(() => {
    setCurrentTab(tabs[0].value)
    setTabs((prev) => {
      const newTabs = [...prev]
      newTabs.pop()

      return newTabs
    })
    setCustomSelection('')
  }, [tabs])

  const handleCustomSelect = useCallback(
    (target: CustomTabs) => {
      setTabs((prev) => [
        ...prev,
        {
          label: `${ctIntl.formatMessage({ id: 'Custom' })} - ${ctIntl.formatMessage({
            id: target,
          })}`,
          value: 'custom',
          icon: (
            <CloseIcon
              onClick={handleCloseCustomTab}
              fontSize="small"
            />
          ),
          iconPosition: 'end',
        },
      ])

      setCurrentTab('custom')
      setCustomSelection(target)
    },
    [handleCloseCustomTab],
  )

  const filteredData = useMemo(
    () =>
      match(currentTab)
        .with(LIST_TABS.SCHEDULED, () =>
          issuanceList.filter((booking) =>
            [
              BookingStatus.BOOKING_STATUS_APPROVED,
              BookingStatus.BOOKING_STATUS_REQUESTED,
              BookingStatus.BOOKING_STATUS_EXPIRING_APPROVAL,
            ].includes(columnsGetters.statusId(booking) as BookingStatus),
          ),
        )
        .with(LIST_TABS.IN_PROGRESS, () =>
          issuanceList.filter((booking) =>
            [
              BookingStatus.BOOKING_STATUS_ACTIVE,
              BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
              BookingStatus.BOOKING_STATUS_ACTIVE_ALMOST_LATE,
            ].includes(columnsGetters.statusId(booking) as BookingStatus),
          ),
        )
        .with(LIST_TABS.HISTORY, () =>
          issuanceList.filter((booking) =>
            [
              BookingStatus.BOOKING_STATUS_CANCELLED,
              BookingStatus.BOOKING_STATUS_DECLINED,
              BookingStatus.BOOKING_STATUS_RETURNED,
              BookingStatus.BOOKING_STATUS_RETURNED_LATE,
              BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
            ].includes(columnsGetters.statusId(booking) as BookingStatus),
          ),
        )
        .with(LIST_TABS.CUSTOM, () =>
          issuanceList.filter((booking) => {
            const status = customSelection
              ? customTabAndStatusMapping[customSelection]
              : null
            return status ? columnsGetters.statusId(booking) === status : false
          }),
        )
        .otherwise(() => []),
    [currentTab, customSelection, issuanceList, columnsGetters],
  )

  const getBulkActionOptions = () => {
    if (isEmpty(multiSelectedBookingIds)) {
      return []
    }

    const allStatus = uniq(
      issuanceList
        .filter((booking) => multiSelectedBookingIds.includes(booking.id))
        .map((booking) => columnsGetters.statusId(booking)),
    )

    if (!isEmpty(allStatus)) {
      if (
        allStatus.every((s) =>
          [BookingStatus.BOOKING_STATUS_APPROVED].includes(s as BookingStatus),
        )
      ) {
        return [
          <MenuItem
            key="change-to-active"
            onClick={() =>
              setCurrentModal({
                type: 'change_to_active',
                data: {
                  bookingIds: [...multiSelectedBookingIds],
                },
              })
            }
            disabled={!carpoolChangeBookingToActive}
          >
            {ctIntl.formatMessage({ id: 'carpool.list.changeToActive' })}
          </MenuItem>,
          <MenuItem
            key="cancel"
            onClick={() =>
              setCurrentModal({
                type: 'cancel_booking',
                data: {
                  bookingIds: [...multiSelectedBookingIds],
                },
              })
            }
            disabled={!carpoolCancelBooking}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </MenuItem>,
        ]
      }

      if (
        allStatus.every((s) =>
          [
            BookingStatus.BOOKING_STATUS_EXPIRING_APPROVAL,
            BookingStatus.BOOKING_STATUS_REQUESTED,
          ].includes(s as BookingStatus),
        )
      ) {
        const canApproveBookings = issuanceList
          .filter((booking) => multiSelectedBookingIds.includes(booking.id))
          .every((booking) => canApproveBooking(booking.pendingManagers))
        return [
          <MenuItem
            key="approve"
            onClick={() =>
              setCurrentModal({
                type: 'approve_booking',
                data: {
                  bookingIds: [...multiSelectedBookingIds],
                },
              })
            }
            disabled={!carpoolApproveBookings || !canApproveBookings}
          >
            {ctIntl.formatMessage({ id: 'Approve' })}
          </MenuItem>,
          <MenuItem
            key="decline"
            onClick={() =>
              setCurrentModal({
                type: 'decline_booking',
                data: {
                  bookingIds: [...multiSelectedBookingIds],
                },
              })
            }
          >
            {ctIntl.formatMessage({ id: 'Decline' })}
          </MenuItem>,
        ]
      }

      if (
        allStatus.every((s) =>
          [
            BookingStatus.BOOKING_STATUS_ACTIVE,
            BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
            BookingStatus.BOOKING_STATUS_ACTIVE_ALMOST_LATE,
          ].includes(s as BookingStatus),
        )
      ) {
        return [
          <MenuItem
            key="complete"
            onClick={() =>
              setCurrentModal({
                type: 'complete_booking',
                data: {
                  bookingIds: multiSelectedBookingIds.map((i) => i.toString()),
                },
              })
            }
          >
            {ctIntl.formatMessage({ id: 'Complete' })}
          </MenuItem>,
          <MenuItem
            key="forceTerminate"
            onClick={() =>
              setCurrentModal({
                type: 'force_terminate_booking',
                data: {
                  bookingIds: [...multiSelectedBookingIds],
                },
              })
            }
            disabled={!carpoolForceTerminateBooking}
          >
            {ctIntl.formatMessage({ id: 'carpool.list.forceTerminateBooking' })}
          </MenuItem>,
        ]
      }
    }

    return []
  }

  const handleSelectionModelChange = (newSelectionModel: GridRowSelectionModel) => {
    setMultiSelectedBookingIds(newSelectionModel as Array<string>)
  }

  const listMetrics = useMemo(() => {
    if (!isEmpty(issuanceListQuery.data?.metrics)) {
      const metrics = (issuanceListQuery.data as FetchIssuanceList.Return).metrics

      return metricConfig.reduce(
        (acc, { key, label, tab }) => {
          if (metrics[key]) {
            acc.push({
              label,
              value: metrics[key],
              ...(tab && { onClick: () => handleCustomSelect(tab) }),
            })
          }
          return acc
        },
        [] as Array<{ label: string; value: number; onClick?: () => void }>,
      )
    }

    return []
  }, [issuanceListQuery.data, handleCustomSelect])

  const shouldShowCheckboxSelection =
    currentTab !== LIST_TABS.HISTORY &&
    ![
      CustomTabs.DECLINED,
      CustomTabs.CANCELLED,
      CustomTabs.RETURNED,
      CustomTabs.RETURNED_LATE,
    ].includes(customSelection as CustomTabs)

  const carpoolOptionsContextValue = useMemo(
    () => ({
      carpoolOptions: bookingOptionQuery.data ?? ({} as FetchCarpoolOptionsParsedData),
    }),
    [bookingOptionQuery.data],
  )
  const handleCloseModal = () => {
    setCurrentModal(null)
    setMultiSelectedBookingIds([])
  }
  return (
    <CarpoolOptionsContext.Provider value={carpoolOptionsContextValue}>
      <PageWithMainTableContainer>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            mb: 2,
          }}
        >
          <Typography variant="h5">
            {ctIntl.formatMessage(
              { id: 'carpool.list.header' },
              {
                values: {
                  carpoolAppName: 'Carpool',
                },
              },
            )}
          </Typography>
          <NewCarpoolRequestButtonMeta>
            {(meta) => (meta.canCreateBookings ? meta.renderMainButton() : null)}
          </NewCarpoolRequestButtonMeta>
        </Box>
        <StatBar
          stats={listMetrics}
          isClickable={!customSelection}
          isLoading={issuanceListQuery.isPending}
        />

        <ContainerWithTabsForDataGrid
          renderTabs={() => (
            <ContainerWithTabsForDataGrid.Tabs
              value={currentTab}
              onChange={(_e, newValue) => setCurrentTab(newValue)}
            >
              {tabs.map(({ label, value, icon, iconPosition }) => (
                <ContainerWithTabsForDataGrid.Tab
                  key={value}
                  label={label}
                  value={value}
                  sx={{
                    height: '48px',
                    minHeight: '48px',
                  }}
                  {...(icon
                    ? {
                        icon,
                        iconPosition,
                      }
                    : {})}
                />
              ))}
            </ContainerWithTabsForDataGrid.Tabs>
          )}
        >
          <UserDataGridWithSavedSettingsOnIDB<IssuanceItem>
            Component={DataGridAsTabItem}
            dataGridId="currentIssuance"
            disableVirtualization
            disableRowSelectionOnClick
            loading={
              issuanceListQuery.isPending || carpoolVehiclesQuery.status === 'pending'
            }
            initialState={{
              pinnedColumns: {
                right: ['actions'],
              },
            }}
            autoPageSize
            pagination
            autosizeOnMount
            rows={filteredData}
            getRowId={useCallbackBranded(
              (row: (typeof filteredData)[number]) => row.id,
              [],
            )}
            columns={columns}
            checkboxSelection={shouldShowCheckboxSelection}
            onRowSelectionModelChange={handleSelectionModelChange}
            rowSelectionModel={multiSelectedBookingIds}
            slots={{
              toolbar: GridToolbarWithQuickFilter,
              loadingOverlay: LinearProgress,
            }}
            slotProps={{
              toolbar: GridToolbarWithQuickFilter.createProps({
                gridToolbarRightContent: shouldShowCheckboxSelection ? (
                  <PopupState
                    variant="popover"
                    popupId="bulk-action"
                  >
                    {(popupState) => {
                      const builkActionOptions = getBulkActionOptions()
                      return (
                        <div>
                          <Button
                            size="small"
                            variant="outlined"
                            color="secondary"
                            sx={{
                              width: '30px',
                              height: '30px',
                              minWidth: 'auto',
                            }}
                            disabled={isEmpty(builkActionOptions)}
                            {...bindTrigger(popupState)}
                          >
                            <ThreeDotsIcon />
                          </Button>
                          <Popover
                            {...bindPopover(popupState)}
                            anchorOrigin={{
                              vertical: 'bottom',
                              horizontal: 'center',
                            }}
                            transformOrigin={{
                              vertical: 'top',
                              horizontal: 'center',
                            }}
                          >
                            <MenuList>
                              {builkActionOptions.map((item) => item)}
                            </MenuList>
                          </Popover>
                        </div>
                      )
                    }}
                  </PopupState>
                ) : null,
              }),
            }}
          />
        </ContainerWithTabsForDataGrid>

        {/* Modal */}
        {currentModal?.type === 'change_to_active' && (
          <ChangeToActiveConfirmationModal
            onClose={handleCloseModal}
            onConfirm={() => {}}
            bookingIds={currentModal.data.bookingIds}
          />
        )}

        {currentModal?.type === 'key_collection' && (
          <KeyCollectionModal
            onClose={() => setCurrentModal(null)}
            bookingDetails={currentModal.data.bookingDetails}
          />
        )}

        {currentModal?.type === 'key_return' && (
          <KeyReturnModal
            onClose={() => setCurrentModal(null)}
            bookingDetails={currentModal.data.bookingDetails}
          />
        )}

        {currentModal?.type === 'approve_booking' && (
          <ApproveBookingModal
            onClose={handleCloseModal}
            bookingIds={currentModal.data.bookingIds}
          />
        )}

        {currentModal?.type === 'complete_booking' && (
          <CompleteBookingModal
            onClose={handleCloseModal}
            bookingIds={currentModal.data.bookingIds}
          />
        )}

        {currentModal?.type === 'decline_booking' && (
          <DeclineBookingModal
            onClose={handleCloseModal}
            bookingIds={currentModal.data.bookingIds}
          />
        )}

        {currentModal?.type === 'cancel_booking' && (
          <CancelBookingModal
            onClose={handleCloseModal}
            bookingIds={currentModal.data.bookingIds}
          />
        )}

        {currentModal?.type === 'force_terminate_booking' && (
          <ForceTerminateBookingModal
            onClose={handleCloseModal}
            bookingIds={currentModal.data.bookingIds}
          />
        )}

        {/* Drawer */}

        {currentDrawer?.type === 'booking_details' && (
          <BookingDetailsDrawer
            onClose={() => setCurrentDrawer(null)}
            bookingId={currentDrawer.data.bookingId}
            vehicleTimelineParams={currentDrawer.data.vehicleTimelineParams}
          />
        )}
      </PageWithMainTableContainer>
    </CarpoolOptionsContext.Provider>
  )
}

export default List
