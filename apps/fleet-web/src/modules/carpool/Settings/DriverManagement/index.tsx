import { useCallback, useMemo, useState } from 'react'
import {
  Autocomplete,
  Chip,
  CircularProgressDelayedCentered,
  DataGrid,
  Divider,
  FormControl,
  FormHelperText,
  GridActionsCellItem,
  gridExpandedRowCountSelector,
  GridRowModes,
  IconButton,
  InputLabel,
  LinearProgress,
  MenuItem,
  OverflowTypography,
  Select,
  Skeleton,
  Stack,
  Switch,
  TextField,
  Tooltip,
  useDataGridColumnHelper,
  useGridApiRef,
  useGridSelector,
  useTheme,
  type GridApi,
  type GridColDef,
  type GridRowModesModel,
} from '@karoo-ui/core'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import ModeOutlinedIcon from '@mui/icons-material/ModeOutlined'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import { useForm, useStore } from '@tanstack/react-form'
import { rgba } from 'polished'
import { FormattedMessage } from 'react-intl'
import { useHistory } from 'react-router-dom'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { IntRange } from 'type-fest'

import type { ClientUserId, CompanyDepartmentId, DriverId } from 'api/types'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { useEventHandler } from 'src/hooks/useEventHandler'
import {
  useDriversWithCarpoolMetaQuery,
  type UseDriversWithCarpoolMetaQueryReturnWithData,
} from 'src/modules/api/useDriversQuery'
import { CARPOOL } from 'src/modules/app/components/routes/carpool'
import { getDriverDetailsModalMainPath } from 'src/modules/app/GlobalModals/DriverDetails'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useDepartmentsOptionsQuery } from 'src/modules/settings/company/api/useDepartmentsQuery'
import { renderListItemUnit } from 'src/modules/settings/manage-users/shared/UserForm'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'

import { Stats } from 'cartrack-ui-kit'
import SettingPage from '../components/SettingPage'
import { useUpdateCarpoolDriverMutation } from './api/mutations'
import { driverEditSchema, type DriverEditSchema } from './schema'
import {
  DepartmentAllOptionValue,
  DRIVER_UI_STATUS,
  DRIVER_UI_STATUS_LABELS,
  getDriverFilterOptionsMeta,
  getDriverUIStatusOptionsMeta,
  renderDriverNameCell,
  type Driver,
  type DriverUIStatus,
  type StatusFilterValue,
} from './utils'

type UnitFilterOption = {
  name: string
  label: string
  value: CompanyDepartmentId
  unitLevel: IntRange<1, 5>
  unitManagers: Set<ClientUserId>
}

type DataGridRow = Driver

function AllDriversTable() {
  const driversWithCarpoolMetaQuery = useDriversWithCarpoolMetaQuery()

  return (
    <SettingPage
      title={CARPOOL.subMenusRoutes.SETTINGS.tabRoutes.DRIVER_MANAGEMENT.text}
      subTitle="carpool.settings.tab.driverManagement.subTitle"
    >
      {!driversWithCarpoolMetaQuery.data ? (
        <CircularProgressDelayedCentered />
      ) : (
        <Content driversWithCarpoolMetaQueryWithData={driversWithCarpoolMetaQuery} />
      )}
    </SettingPage>
  )
}

type ContentProps = {
  driversWithCarpoolMetaQueryWithData: UseDriversWithCarpoolMetaQueryReturnWithData
}
const Content = ({ driversWithCarpoolMetaQueryWithData }: ContentProps) => {
  const history = useHistory()
  const apiRef = useGridApiRef()
  const theme = useTheme()

  const driverFilterOptions = useMemo(() => getDriverFilterOptionsMeta().options, [])

  const [statusFilter, setStatusFilter] = useState<StatusFilterValue>(
    driverFilterOptions[0].value,
  )
  const [departmentFilter, setDepartmentFilter] = useState<CompanyDepartmentId | 'all'>(
    DepartmentAllOptionValue,
  )
  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'client' })
  const departmentsOptionsQuery = useDepartmentsOptionsQuery()
  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})

  const activeDrivers = useMemo(
    () => driversWithCarpoolMetaQueryWithData.data.activeDrivers,
    [driversWithCarpoolMetaQueryWithData],
  )

  const {
    getDriverCarpoolUIStatusMeta,
    counts: { driverUIStatusCounts },
  } = driversWithCarpoolMetaQueryWithData.data

  const filteredDrivers = useMemo((): Array<DataGridRow> => {
    let drivers = activeDrivers

    match(statusFilter)
      .with('all', () => {
        // keep all drivers
      })
      .with('active_carpool_driver', () => {
        drivers = drivers.filter(
          (d) => getDriverCarpoolUIStatusMeta(d).status === 'active_carpool_driver',
        )
      })
      .with('setup_incomplete', () => {
        drivers = drivers.filter(
          (d) => getDriverCarpoolUIStatusMeta(d).status === 'setup_incomplete',
        )
      })
      .with('non_carpool_driver', () => {
        drivers = drivers.filter(
          (d) => getDriverCarpoolUIStatusMeta(d).status === 'non_carpool_driver',
        )
      })
      .exhaustive()

    if (departmentFilter !== 'all') {
      drivers = drivers.filter((d) => d.departmentId === departmentFilter)
    }
    return drivers
  }, [activeDrivers, statusFilter, departmentFilter, getDriverCarpoolUIStatusMeta])

  const unitFilterOptions = useMemo(
    (): ReadonlyArray<UnitFilterOption> => [
      {
        name: ctIntl.formatMessage({
          id: 'carpool.settings.driverManagement.allDepartments',
        }),
        label: ctIntl.formatMessage({
          id: 'carpool.settings.driverManagement.allDepartments',
        }),
        // There is an issue with Autocomplete on "@mui/material": "7.0.1" that causes it
        // to not recognize a value that can be of union type. We cast it was a workaround
        value: DepartmentAllOptionValue as CompanyDepartmentId,
        unitLevel: 1 as IntRange<1, 5>,
        unitManagers: new Set(),
      },
      ...(departmentsOptionsQuery.data?.flattenedArray ?? []),
    ],
    [departmentsOptionsQuery.data?.flattenedArray],
  )

  const driverStatusOptionsMeta = useMemo(() => getDriverUIStatusOptionsMeta(), [])

  const createFormInitialValuesFromDriverId = useCallback(
    (driverId: DriverId): DriverEditSchema | undefined => {
      const driver = activeDrivers.find((d) => d.id === driverId)
      if (!driver) {
        return undefined
      }
      return {
        departmentId: driver.departmentId,
        allowSystemAutobooking: !!driver.carpoolMeta?.canAutoBook,
        allowSpecificVehicleBooking: !!driver.carpoolMeta?.canBookSpecificVehicles,
        canBookCarpool: !!driver.carpoolMeta?.canBookCarpool,
      }
    },
    [activeDrivers],
  )

  const form = useForm({
    defaultValues: undefined as DriverEditSchema | undefined,
    validators: { onChange: driverEditSchema },
  })

  const {
    isValid,
    isDirty,
    values: formValues,
  } = useStore(form.store, (state) => ({
    isValid: state.isValid,
    isDirty: state.isDirty,
    values: state.values,
  }))

  const statusChipColors: Record<DriverUIStatus, string> = useMemo(
    () => ({
      [DRIVER_UI_STATUS.ACTIVE_CARPOOL]: theme.palette.success.dark,
      [DRIVER_UI_STATUS.SETUP_INCOMPLETE]: theme.palette.warning.dark,
      [DRIVER_UI_STATUS.NON_CARPOOL]: theme.palette.text.secondary,
    }),
    [theme.palette],
  )

  const updateDriverMutation = useUpdateCarpoolDriverMutation()

  const onRowCheckButtonClick = useEventHandler((id: DriverId) => {
    updateDriverMutation.mutate(
      {
        driverId: id,
        departmentId: formValues.departmentId,
        allowSystemAutobooking: formValues.allowSystemAutobooking,
        allowSpecificVehicleBooking: formValues.allowSpecificVehicleBooking,
        canBookCarpool: formValues.canBookCarpool,
      },
      {
        onSuccess: () => {
          setRowModesModel({
            ...rowModesModel,
            [id]: {
              mode: GridRowModes.View,
              // Since we are recreating rows indirectly (by updating drivers list), we need to make sure that
              // we don't revert the changes made by the mutation when returning to view mode.
              ignoreModifications: true,
            },
          })
        },
        onError: () => {
          // do nothing
        },
      },
    )
  })

  const onRowCancelButtonClick = useEventHandler((id: DriverId) => {
    setRowModesModel({
      ...rowModesModel,
      [id]: { mode: GridRowModes.View, ignoreModifications: true },
    })
  })

  const onRowEditButtonClick = useEventHandler((row: Driver) => {
    // Make sure we reset the form for the selected driver
    // This is specially important when we try to edit a driver but another driver was already being edited
    form.reset(createFormInitialValuesFromDriverId(row.id))

    setRowModesModel((oldModel) => ({
      ...R.mapValues(oldModel, (value, key) => {
        if (key === row.id) {
          return value
        }
        return { mode: GridRowModes.View, ignoreModifications: true }
      }),
      [row.id]: { mode: GridRowModes.Edit },
    }))
  })

  const handleAllowBookingChange = useEventHandler(
    ({
      event,
      onFieldChange,
    }: {
      event: React.ChangeEvent<HTMLInputElement>
      onFieldChange: (value: boolean) => void
    }) => {
      const newValue = event.target.checked
      onFieldChange(newValue)
      if (newValue === true) {
        form.fieldInfo.canBookCarpool.instance?.handleChange(true)
      }
    },
  )

  const driverColumns = useMemo(
    () =>
      [
        columnHelper.string((_, row) => row.name, {
          field: 'driverName',
          headerName: ctIntl.formatMessage({ id: 'Driver Name' }),
          flex: 1,
          renderCell: ({ row }) => renderDriverNameCell({ driver: row }),
        }),
        columnHelper.singleSelect(
          (_, row) => getDriverCarpoolUIStatusMeta(row).status,
          {
            field: 'status',
            headerName: ctIntl.formatMessage({ id: 'Status' }),
            flex: 1,
            valueOptions: driverStatusOptionsMeta.statusOptions,
            renderCell: ({ row }) => {
              const statusMeta = getDriverCarpoolUIStatusMeta(row)

              const status = statusMeta.status
              const resultChip = (
                <Chip
                  size="small"
                  label={ctIntl.formatMessage({
                    id: DRIVER_UI_STATUS_LABELS[status].msgId,
                  })}
                  sx={{
                    color: statusChipColors[status],
                    backgroundColor: rgba(statusChipColors[status], 0.12),
                  }}
                />
              )

              if (status !== 'setup_incomplete') {
                return resultChip
              }

              const missingFields: Array<string> = []
              for (const reason of statusMeta.reasons) {
                match(reason)
                  .with(
                    'driver_needs_to_be_in_a_department_due_to_check_for_departments_being_enabled',
                    () => {
                      missingFields.push(ctIntl.formatMessage({ id: 'Department' }))
                    },
                  )
                  .with(
                    'driver_needs_to_have_at_least_one_license_due_to_check_for_licenses_being_enabled',
                    () => {
                      missingFields.push(ctIntl.formatMessage({ id: 'License' }))
                    },
                  )
                  .with(
                    'has_can_book_carpool_true_but_missing_a_booking_permission',
                    () =>
                      missingFields.push(
                        ctIntl.formatMessage({
                          id: 'carpool.settings.driverManagement.driverMissingFields.atLeastOneBookingPermission',
                        }),
                      ),
                  )
                  .with('has_booking_type_but_can_book_carpool_is_false', () => {
                    missingFields.push(
                      ctIntl.formatMessage(
                        {
                          id: 'carpool.settings.driverManagement.driverMissingFields.hasABookingTypeButCanBookIsDisabled',
                        },
                        {
                          values: {
                            canBookColumnHeader: ctIntl.formatMessage({
                              id: 'carpool.settings.driverManagement.canBookCarpool',
                            }),
                          },
                        },
                      ),
                    )
                  })
                  .exhaustive()
              }

              return (
                <Tooltip
                  disableInteractive
                  title={
                    <FormattedMessage
                      id="carpool.settings.driverManagement.driverMissingFields"
                      values={{
                        reasons: missingFields.map((field) => (
                          <li key={field}>
                            <span>{field}</span>
                          </li>
                        )),
                        br: <br />,
                      }}
                    />
                  }
                >
                  {resultChip}
                </Tooltip>
              )
            },
          },
        ),
        columnHelper.string(
          (_, row) =>
            row.departmentId
              ? (departmentsOptionsQuery.data?.allById.get(row.departmentId)?.label ??
                null)
              : null,
          {
            field: 'departmentId',
            headerName: ctIntl.formatMessage({ id: 'Department' }),
            flex: 1,
            editable: true,
            renderCell: ({ row }) =>
              match(departmentsOptionsQuery)
                .with({ status: 'pending' }, () => (
                  <Skeleton
                    variant="text"
                    sx={{ width: '100%', fontSize: '1rem' }}
                  />
                ))
                .with({ status: 'error' }, () => null)
                .with({ status: 'success' }, ({ data: { allById: byId } }) =>
                  row.departmentId ? (byId.get(row.departmentId)?.label ?? null) : null,
                )
                .exhaustive(),
            renderEditCell: () => {
              const unitOptions = departmentsOptionsQuery.data?.flattenedArray ?? []

              return (
                <form.Field name="departmentId">
                  {(field) => {
                    const currentOption =
                      field.state.value == null
                        ? null
                        : (unitOptions.find((opt) => opt.value === field.state.value) ??
                          null)

                    return (
                      <Autocomplete
                        sx={{ width: '100%' }}
                        options={unitOptions}
                        value={currentOption}
                        onChange={(_event, newValue) => {
                          field.handleChange(newValue?.value ?? null)
                        }}
                        getOptionLabel={(option) => option.label || ''}
                        renderInput={(paramsInput) => (
                          <TextField
                            {...paramsInput}
                            size="small"
                          />
                        )}
                        isOptionEqualToValue={(option, value) =>
                          option.value === value.value
                        }
                        renderOption={renderListItemUnit}
                      />
                    )
                  }}
                </form.Field>
              )
            },
          },
        ),
        columnHelper.boolean((_, row) => row.carpoolMeta?.canAutoBook, {
          field: 'allowSystemAutobooking',
          headerName: ctIntl.formatMessage({
            id: 'carpool.settings.driverManagement.allowSystemAutobooking',
          }),
          flex: 1,
          editable: true,
          renderCell: ({ value }) => (
            <Switch
              checked={value ?? false}
              readOnly
              sx={{
                pointerEvents: 'none',
              }}
            />
          ),
          renderEditCell: () => (
            <form.Field name="allowSystemAutobooking">
              {(field) => (
                <Switch
                  checked={field.state.value}
                  onChange={(e) =>
                    handleAllowBookingChange({
                      event: e,
                      onFieldChange: (newValue: boolean) =>
                        field.handleChange(newValue),
                    })
                  }
                />
              )}
            </form.Field>
          ),
        }),
        columnHelper.boolean((_, row) => row.carpoolMeta?.canBookSpecificVehicles, {
          field: 'allowSpecificVehicleBooking',
          headerName: ctIntl.formatMessage({
            id: 'carpool.settings.driverManagement.allowSpecificVehicleBooking',
          }),
          flex: 1,
          editable: true,
          renderCell: ({ value }) => (
            <Switch
              checked={value ?? false}
              readOnly
              sx={{
                pointerEvents: 'none',
              }}
            />
          ),
          renderEditCell: () => (
            <form.Field name="allowSpecificVehicleBooking">
              {(field) => (
                <Switch
                  checked={field.state.value}
                  onChange={(e) =>
                    handleAllowBookingChange({
                      event: e,
                      onFieldChange: (newValue: boolean) =>
                        field.handleChange(newValue),
                    })
                  }
                />
              )}
            </form.Field>
          ),
        }),
        columnHelper.boolean((_, row) => row.carpoolMeta?.canBookCarpool, {
          field: 'canBookCarpool',
          headerName: ctIntl.formatMessage({
            id: 'carpool.settings.driverManagement.canBookCarpool',
          }),
          flex: 1,
          editable: true,
          renderCell: ({ value }) => (
            <Switch
              checked={value ?? false}
              readOnly
              sx={{
                pointerEvents: 'none',
              }}
            />
          ),
          renderEditCell: () => (
            <form.Field name="canBookCarpool">
              {(field) => {
                const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
                  const newValue = event.target.checked
                  field.handleChange(newValue)
                  if (!newValue) {
                    // When disabling carpool booking, also disable the other two toggles
                    form.fieldInfo.allowSystemAutobooking.instance?.handleChange(false)
                    form.fieldInfo.allowSpecificVehicleBooking.instance?.handleChange(
                      false,
                    )
                  }
                }
                return (
                  <Stack
                    direction="column"
                    alignItems="center"
                    width="100%"
                  >
                    <Switch
                      checked={field.state.value}
                      onChange={handleChange}
                    />
                    {field.state.meta.errors[0]?.message && (
                      <FormHelperText
                        error
                        sx={{ width: '100%' }}
                      >
                        <OverflowTypography
                          typographyProps={{ sx: { fontSize: '12px' } }}
                        >
                          {ctIntl.formatMessage({
                            id: field.state.meta.errors[0].message,
                          })}
                        </OverflowTypography>
                      </FormHelperText>
                    )}
                  </Stack>
                )
              }}
            </form.Field>
          ),
        }),
        {
          field: 'actions',
          type: 'actions',
          headerName: ctIntl.formatMessage({ id: 'Actions' }),
          getActions: ({ id, row }) => {
            const isInEditMode = rowModesModel[id]?.mode === GridRowModes.Edit
            if (isInEditMode) {
              return [
                <Tooltip
                  key="save"
                  title={ctIntl.formatMessage({ id: 'Save' })}
                >
                  <span>
                    <GridActionsCellItem
                      disabled={!isValid || !isDirty || updateDriverMutation.isPending}
                      icon={<CheckIcon />}
                      label={ctIntl.formatMessage({ id: 'Save' })}
                      onClick={() => onRowCheckButtonClick(row.id)}
                      sx={{ color: 'success.main' }}
                    />
                  </span>
                </Tooltip>,
                <Tooltip
                  key="cancel"
                  title={ctIntl.formatMessage({ id: 'Cancel' })}
                >
                  <span>
                    <GridActionsCellItem
                      icon={<CloseIcon />}
                      disabled={updateDriverMutation.isPending}
                      label={ctIntl.formatMessage({ id: 'Cancel' })}
                      onClick={() => onRowCancelButtonClick(row.id)}
                      sx={{ color: 'error.main' }}
                    />
                  </span>
                </Tooltip>,
              ]
            }
            return [
              <IconButton
                disabled={!row.userPermissions.edit}
                onClick={() => onRowEditButtonClick(row)}
                key="edit"
              >
                <ModeOutlinedIcon />
              </IconButton>,
              <IconButton
                onClick={() => {
                  history.push(getDriverDetailsModalMainPath(history.location, row.id))
                }}
                key="view"
              >
                <VisibilityOutlinedIcon />
              </IconButton>,
            ]
          },
        },
      ] satisfies Array<GridColDef<Driver>>,
    [
      columnHelper,
      driverStatusOptionsMeta.statusOptions,
      getDriverCarpoolUIStatusMeta,
      statusChipColors,
      departmentsOptionsQuery,
      form,
      handleAllowBookingChange,
      rowModesModel,
      isValid,
      isDirty,
      updateDriverMutation.isPending,
      onRowCheckButtonClick,
      onRowCancelButtonClick,
      onRowEditButtonClick,
      history,
    ],
  )

  return (
    <UserDataGridWithSavedSettingsOnIDB
      Component={DataGrid}
      dataGridId="carpool-drivers-list"
      data-testid="carpool-drivers-list"
      editMode="row"
      apiRef={apiRef}
      columns={driverColumns}
      rows={filteredDrivers}
      rowModesModel={rowModesModel}
      pagination
      pageSizeOptions={[25, 50, 100]}
      disableRowSelectionOnClick
      sx={{
        '& .MuiDataGrid-row': { cursor: 'pointer' },
      }}
      initialState={{
        pagination: { paginationModel: { pageSize: 25, page: 0 } },
      }}
      loading={updateDriverMutation.isPending}
      slots={{
        toolbar: KarooToolbar,
        loadingOverlay: LinearProgress,
        noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
      }}
      slotProps={{
        toolbar: KarooToolbar.createProps({
          slots: {
            searchFilter: { show: true },
            settingsButton: { show: true },
            filterButton: { show: true },
          },
          extraContent: {
            left: (
              <Stack
                direction="row"
                gap={1}
              >
                <FormControl sx={{ minWidth: '220px' }}>
                  <InputLabel>
                    {ctIntl.formatMessage({
                      id: 'carpool.settings.driverManagement.statusFilter',
                    })}
                  </InputLabel>
                  <Select
                    size="small"
                    value={statusFilter}
                    label={ctIntl.formatMessage({
                      id: 'carpool.settings.driverManagement.statusFilter',
                    })}
                    onChange={(e) =>
                      setStatusFilter(
                        e.target.value as (typeof driverFilterOptions)[number]['value'],
                      )
                    }
                  >
                    {driverFilterOptions.map((option) => (
                      <MenuItem
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Autocomplete
                  sx={{ minWidth: '220px' }}
                  // TODO: find a way to use virtualization here
                  options={unitFilterOptions}
                  loading={departmentsOptionsQuery.isLoading}
                  value={
                    unitFilterOptions.find(
                      (option) => option.value === departmentFilter,
                    ) || unitFilterOptions[0]
                  }
                  isOptionEqualToValue={(option, { value }) => option.value === value}
                  onChange={(_event, newValue) => {
                    setDepartmentFilter(
                      newValue ? newValue.value : DepartmentAllOptionValue,
                    )
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={ctIntl.formatMessage({
                        id: 'Department',
                      })}
                    />
                  )}
                  renderOption={renderListItemUnit}
                />
              </Stack>
            ),
            middle: (
              <Stack
                direction="row"
                gap={2}
              >
                <TotalDriversToolbar
                  apiRef={apiRef}
                  statusFilter={statusFilter}
                />
                <Divider
                  orientation="vertical"
                  variant="middle"
                  flexItem
                />
                <Stats
                  reversed
                  data={[
                    {
                      key: 'Departments',
                      value: departmentsOptionsQuery.data?.flattenedArray.length ?? 0,
                    },
                  ]}
                />
                <Divider
                  orientation="vertical"
                  variant="middle"
                  flexItem
                />
                <Stats
                  reversed
                  data={[
                    {
                      key: ctIntl.formatMessage({
                        id: DRIVER_UI_STATUS_LABELS[DRIVER_UI_STATUS.ACTIVE_CARPOOL]
                          .msgId,
                      }),
                      value: driverUIStatusCounts[DRIVER_UI_STATUS.ACTIVE_CARPOOL],
                    },
                  ]}
                />
              </Stack>
            ),
          },
        }),
        pagination: { showFirstButton: true, showLastButton: true },
      }}
    />
  )
}

export default AllDriversTable

type TotalDriversToolbarProps = {
  apiRef: React.MutableRefObject<GridApi>
  statusFilter: StatusFilterValue
}

function TotalDriversToolbar({ apiRef, statusFilter }: TotalDriversToolbarProps) {
  const rowCount = useGridSelector(apiRef, gridExpandedRowCountSelector)
  return (
    <Stats
      reversed
      data={[
        {
          key: match(statusFilter)
            .with('all', () => 'Total drivers')
            .with('active_carpool_driver', () =>
              ctIntl.formatMessage({
                id: DRIVER_UI_STATUS_LABELS[DRIVER_UI_STATUS.ACTIVE_CARPOOL].msgId,
              }),
            )
            .with('non_carpool_driver', () =>
              ctIntl.formatMessage({
                id: DRIVER_UI_STATUS_LABELS[DRIVER_UI_STATUS.NON_CARPOOL].msgId,
              }),
            )
            .with('setup_incomplete', () =>
              ctIntl.formatMessage({
                id: DRIVER_UI_STATUS_LABELS[DRIVER_UI_STATUS.SETUP_INCOMPLETE].msgId,
              }),
            )
            .exhaustive(),
          value: rowCount,
        },
      ]}
    />
  )
}
