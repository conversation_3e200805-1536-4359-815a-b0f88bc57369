import { useMemo, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  Divider,
  GridActionsCellItem,
  GridRowModes,
  LinearProgress,
  MenuItem,
  OverflowTypography,
  Skeleton,
  Stack,
  TextField,
  Tooltip,
  useCallbackB<PERSON>ed,
  useDataGridColumnHelper,
  type GridColDef,
  type GridRowHeightParams,
  type GridRowModesModel,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-tanstack-form'
import AddIcon from '@mui/icons-material/Add'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import ModeOutlinedIcon from '@mui/icons-material/ModeOutlined'
import { useForm, useStore } from '@tanstack/react-form'
import type {
  QueryObserverPlaceholderResult,
  QueryObserverSuccessResult,
} from '@tanstack/react-query'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import { z } from 'zod/v4'

import { geofenceIdSchema, vehicleIdSchema, type VehicleId } from 'api/types'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { useEventHandler } from 'src/hooks/useEventHandler'
import { useGeofencesQuery } from 'src/modules/api/useGeofencesQuery'
import {
  useVehiclesQuery,
  type UseVehiclesWithCarpoolMetaQueryData,
} from 'src/modules/api/useVehiclesQuery'
import {
  useLocationsQuery,
  useLocationTypes,
  useUpdateLocationMutation,
  type UseLocationsQueryData,
} from 'src/modules/lists/Facilities/api/queries'
import type { LocationTypeId } from 'src/modules/lists/Facilities/api/types'
import { mapLocationTypeToTranslation } from 'src/modules/lists/Facilities/utils'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { messages } from 'src/shared/forms/messages'
import { ctIntl } from 'src/util-components/ctIntl'
import { toMutable } from 'src/util-functions/functional-utils'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'

import { Stats } from 'cartrack-ui-kit'
import AssignVehicleComponent from '../../components/AssignVehicleComponent'
import RowDisplayWithTooltip from '../../components/RowDisplayWithTooltip'
import SectionCollapseDataGrid from '../../components/SectionCollapseDataGrid'
import type { VehicleWithAssignedItemOption } from '../../type'
import { useVehicleManagementContext } from '../VehicleManagementContext'
import type { FacilitiesMetaCounts } from './types'

type DataGridRow = UseLocationsQueryData['list'][number]

const columnsGetters = {
  name: (value: DataGridRow) => value.name,
  locationTypeId: (value: DataGridRow) => value.typeId,
  address: (value: DataGridRow) => value.address,
  geofenceName: (value: DataGridRow) => value.geofence?.name ?? null,
  vehicles: (value: DataGridRow) => value.vehicleIds,
} as const

const locationEditSchema = () =>
  z.object({
    name: z.string().min(1, ctIntl.formatMessage({ id: messages.required })),
    locationTypeId: z.string().min(1, ctIntl.formatMessage({ id: messages.required })),
    // type: generateFacilityTypeIdSchema(),
    address: z.string().min(1, ctIntl.formatMessage({ id: messages.required })),
    geofenceId: geofenceIdSchema.nullable(),
    vehicleIds: z
      .set(vehicleIdSchema)
      .min(1, ctIntl.formatMessage({ id: messages.required })),
  })

type LocationEditForm = z.infer<ReturnType<typeof locationEditSchema>>

const parseRowToFormData = (row: DataGridRow): LocationEditForm => ({
  name: row.name,
  locationTypeId: row.typeId,
  address: row.address ?? '',
  geofenceId: row.geofence?.id ?? null,
  vehicleIds: toMutable(row.vehicleIds),
})

export default function LocationDataGrid({
  carpoolLocationsSuccessQuery,
  vehiclesWithCarpoolMetaSuccessQuery,
  counts,
}: {
  carpoolLocationsSuccessQuery:
    | QueryObserverSuccessResult<UseLocationsQueryData>
    | QueryObserverPlaceholderResult<UseLocationsQueryData>
  vehiclesWithCarpoolMetaSuccessQuery: { data: UseVehiclesWithCarpoolMetaQueryData }
  counts: FacilitiesMetaCounts
}) {
  const { dispatch } = useVehicleManagementContext()
  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'client' })
  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})

  const vehiclesQuery = useVehiclesQuery()
  const geofencesQuery = useGeofencesQuery()
  const facilitiesQuery = useLocationsQuery()
  const locationTypesQuery = useLocationTypes()

  const updateLocationMutation = useUpdateLocationMutation()

  const rows = useMemo(
    (): ReadonlyArray<DataGridRow> => carpoolLocationsSuccessQuery.data.list,
    [carpoolLocationsSuccessQuery.data],
  )

  const formSchema = useMemo(() => locationEditSchema(), [])

  const form = useForm({
    defaultValues: undefined as LocationEditForm | undefined,
    validators: { onChange: formSchema },
  })

  const {
    isValid,
    isDirty,
    values: formValues,
    errors,
  } = useStore(form.store, (state) => ({
    isValid: state.isValid,
    isDirty: state.isDirty,
    values: state.values,
    errors: state.errors,
  }))

  const onRowCheckButtonClick = useEventHandler((row: DataGridRow) => {
    updateLocationMutation.mutate(
      {
        id: row.id,
        data: {
          name: formValues.name,
          address: formValues.address,
          geofenceId: formValues.geofenceId,
          typeId: formValues.locationTypeId as LocationTypeId,
          vehicleIds: Array.from(formValues.vehicleIds),
          description: row.description,
          coords: row.coords,
        },
      },
      {
        onSuccess() {
          setRowModesModel({
            ...rowModesModel,
            [row.id]: {
              mode: GridRowModes.View,
              // Since we are recreating rows indirectly (by updating rows array), we need to make sure that
              // we don't revert the changes made by the mutation when returning to view mode.
              ignoreModifications: true,
            },
          })
        },
      },
    )
  })

  const assignedVehicleWithLocationById = useMemo(() => {
    const assignedVehicleWithLocationById = new Map<
      VehicleId,
      VehicleWithAssignedItemOption
    >()

    if (!facilitiesQuery.data || !vehiclesWithCarpoolMetaSuccessQuery.data) {
      return assignedVehicleWithLocationById
    }

    for (const [vehicleId, facilityId] of carpoolLocationsSuccessQuery.data
      .locationAndVehicleJoinByVehicleId) {
      const vehicle =
        vehiclesWithCarpoolMetaSuccessQuery.data.vehiclesMapById.get(vehicleId)
      const facility = facilitiesQuery.data?.byId.get(facilityId)
      if (!vehicle || !facility) {
        continue
      }

      const vehicleData: VehicleWithAssignedItemOption = {
        value: vehicleId,
        label: vehicle?.registration ?? '',
        item: facility.name,
      }
      assignedVehicleWithLocationById.set(vehicleId, vehicleData)
    }

    return assignedVehicleWithLocationById
  }, [
    carpoolLocationsSuccessQuery.data.locationAndVehicleJoinByVehicleId,
    facilitiesQuery.data,
    vehiclesWithCarpoolMetaSuccessQuery.data,
  ])

  const locationTypesOptions = useMemo(
    () =>
      locationTypesQuery.data?.array.map((type) => ({
        value: type.id,
        label: mapLocationTypeToTranslation(type.identifier),
      })) ?? [],
    [locationTypesQuery.data?.array],
  )

  const onRowEditButtonClick = useEventHandler((row: DataGridRow) => {
    // Make sure we reset the form for the selected row
    // This is specially important when we try to edit a row but another row was already being edited
    form.reset(parseRowToFormData(row))

    setRowModesModel((oldModel) => ({
      ...R.mapValues(oldModel, (value, key) => {
        if (key === row.id) {
          return value
        }
        return { mode: GridRowModes.View, ignoreModifications: true }
      }),
      [row.id]: { mode: GridRowModes.Edit },
    }))
  })

  const locationColumns = useMemo(
    () =>
      [
        columnHelper.string((_, row) => columnsGetters.name(row), {
          field: 'name',
          headerName: ctIntl.formatMessage({ id: 'Name' }),
          editable: true,
          flex: 1,
          renderEditCell: () => (
            <form.Field name="name">
              {(field) => (
                <TextFieldControlled
                  field={field}
                  sx={{ flex: 1, backgroundColor: 'common.white', ml: 1 }}
                  data-testid="carpool-settings-location-name"
                  placeholder={ctIntl.formatMessage({ id: 'Name' })}
                />
              )}
            </form.Field>
          ),
        }),
        columnHelper.string(
          (_, row) =>
            locationTypesQuery.data?.byId.get(columnsGetters.locationTypeId(row))
              ?.identifier ?? '',
          {
            field: 'type',
            headerName: ctIntl.formatMessage({ id: 'Type' }),
            editable: true,
            flex: 1,
            renderEditCell: () => (
              <form.Field name="locationTypeId">
                {(field) => (
                  <TextFieldControlled
                    select
                    field={field}
                    sx={{ flex: 1, backgroundColor: 'common.white', ml: 1 }}
                    data-testid="carpool-settings-location-type"
                    placeholder={ctIntl.formatMessage({ id: 'Type' })}
                  >
                    {locationTypesOptions.map((type) => (
                      <MenuItem
                        key={type.value}
                        value={type.value}
                      >
                        {type.label}
                      </MenuItem>
                    ))}
                  </TextFieldControlled>
                )}
              </form.Field>
            ),
          },
        ),
        columnHelper.string((_, row) => columnsGetters.address(row), {
          field: 'address',
          headerName: ctIntl.formatMessage({ id: 'Address' }),
          editable: true,
          flex: 1,
          renderEditCell: () => (
            <form.Field name="address">
              {(field) => (
                <TextFieldControlled
                  field={field}
                  sx={{ flex: 1, backgroundColor: 'common.white', ml: 1 }}
                  data-testid="carpool-settings-location-address"
                  placeholder={ctIntl.formatMessage({ id: 'Address' })}
                />
              )}
            </form.Field>
          ),
        }),
        columnHelper.string((_, row) => columnsGetters.geofenceName(row), {
          field: 'geofence',
          headerName: ctIntl.formatMessage({ id: 'Geofence' }),
          editable: true,
          flex: 2,
          renderCell: ({ value }) =>
            match(geofencesQuery)
              .with({ status: 'pending' }, () => (
                <Skeleton
                  variant="text"
                  sx={{ width: '100%', fontSize: '1rem' }}
                  animation="wave"
                />
              ))
              .with({ status: 'success' }, () => value)
              .with({ status: 'error' }, () => null)
              .exhaustive(),
          renderEditCell: () => (
            <form.Field name="geofenceId">
              {(field) => (
                <Autocomplete
                  {...getAutocompleteVirtualizedProps({
                    options: geofencesQuery.data?.geofenceOptionsMeta.array ?? [],
                    renderRowSingleItemContent: ({ label }) => (
                      <OverflowTypography typographyProps={{ variant: 'body2' }}>
                        {label}
                      </OverflowTypography>
                    ),
                  })}
                  sx={{ width: '100%', ml: 1 }}
                  size="small"
                  value={
                    field.state.value
                      ? (geofencesQuery.data?.geofenceOptionsMeta.byId.get(
                          field.state.value,
                        ) ?? null)
                      : null
                  }
                  onChange={(_, newValue) => {
                    field.handleChange(newValue?.value ?? null)
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      data-testid="carpool-settings-location-geofence"
                      label={ctIntl.formatMessage({ id: 'Select Geofence' })}
                      sx={{ backgroundColor: 'common.white' }}
                    />
                  )}
                />
              )}
            </form.Field>
          ),
        }),
        columnHelper.string(
          (_, row) =>
            Array.from(columnsGetters.vehicles(row))
              .map((vId) => vehiclesQuery.data?.vehicleById.get(vId)?.name ?? '')
              .join(', '),
          {
            field: 'vehicles',
            headerName: ctIntl.formatMessage({ id: 'Vehicles' }),
            editable: true,
            flex: 3,
            renderCell: ({ row }) => {
              const value = columnsGetters.vehicles(row)
              if (!value) return null

              const arrayLabels = Array_filterMap(
                Array.from(value) as Array<VehicleId>,
                (id, { RemoveSymbol }) =>
                  vehiclesQuery.data?.vehicleById.get(id)?.name ?? RemoveSymbol,
              )
              return (
                <RowDisplayWithTooltip
                  labels={arrayLabels}
                  limit={3}
                />
              )
            },
            renderEditCell: () => (
              <form.Field name="vehicleIds">
                {(field) => (
                  <Box sx={{ ml: 1, width: '100%' }}>
                    <AssignVehicleComponent
                      itemName="Location"
                      assignedVehicleWithAdditionalFieldsById={
                        assignedVehicleWithLocationById
                      }
                      selectedValues={field.state.value}
                      onChange={(value) => {
                        field.handleChange(value)
                      }}
                    />
                  </Box>
                )}
              </form.Field>
            ),
          },
        ),
        {
          field: 'actions',
          type: 'actions',
          headerName: ctIntl.formatMessage({ id: 'Actions' }),
          align: 'right',
          getActions: ({ id, row }) => {
            const isInEditMode = rowModesModel[id]?.mode === GridRowModes.Edit

            if (isInEditMode) {
              return [
                <Tooltip
                  key="save"
                  title={ctIntl.formatMessage({ id: 'Save' })}
                >
                  <span>
                    <GridActionsCellItem
                      disabled={
                        !isValid || !isDirty || updateLocationMutation.isPending
                      }
                      icon={<CheckIcon />}
                      label={ctIntl.formatMessage({ id: 'Save' })}
                      onClick={() => onRowCheckButtonClick(row)}
                      sx={{ color: 'success.main' }}
                    />
                  </span>
                </Tooltip>,
                <Tooltip
                  key="cancel"
                  title={ctIntl.formatMessage({ id: 'Cancel' })}
                >
                  <span>
                    <GridActionsCellItem
                      disabled={updateLocationMutation.isPending}
                      icon={<CloseIcon />}
                      label={ctIntl.formatMessage({ id: 'Cancel' })}
                      onClick={() => {
                        // Make sure we reset the form for the selected row
                        // This is specially important when we try to edit a row but another row was already being edited
                        form.reset(parseRowToFormData(row))
                        setRowModesModel((prev) => ({
                          ...prev,
                          [id]: { mode: GridRowModes.View, ignoreModifications: true },
                        }))
                      }}
                      sx={{ color: 'error.main' }}
                    />
                  </span>
                </Tooltip>,
              ]
            }

            return [
              <Tooltip
                key="edit"
                title={ctIntl.formatMessage({ id: 'list.drivers.driverIDTags.edit' })}
              >
                <span>
                  <GridActionsCellItem
                    icon={<ModeOutlinedIcon />}
                    label={ctIntl.formatMessage({ id: 'Edit' })}
                    onClick={() => onRowEditButtonClick(row)}
                  />
                </span>
              </Tooltip>,
            ]
          },
        },
      ] satisfies Array<GridColDef<DataGridRow>>,
    [
      columnHelper,
      form,
      locationTypesQuery.data?.byId,
      locationTypesOptions,
      geofencesQuery,
      vehiclesQuery.data?.vehicleById,
      assignedVehicleWithLocationById,
      rowModesModel,
      isValid,
      isDirty,
      updateLocationMutation.isPending,
      onRowCheckButtonClick,
      onRowEditButtonClick,
    ],
  )

  return (
    <SectionCollapseDataGrid<DataGridRow>
      dataGridId="Carpool-Setting-VehicleManagement-Location-Datagrid"
      data-testid="Carpool-Setting-VehicleManagement-Location-Datagrid"
      editMode="row"
      rowModesModel={rowModesModel}
      columns={locationColumns}
      rows={rows}
      getRowHeight={useCallbackBranded(
        ({ id }: GridRowHeightParams<DataGridRow>) => {
          const isInEditMode = rowModesModel[id]?.mode === GridRowModes.Edit

          if (isInEditMode) {
            return Object.keys(errors).length > 0 ? 80 : 56 // Adjust height based on error
          }
          return null // Default height
        },
        [errors, rowModesModel],
      )}
      loading={
        carpoolLocationsSuccessQuery.isLoading ||
        geofencesQuery.status === 'pending' ||
        updateLocationMutation.isPending
      }
      slots={{
        toolbar: KarooToolbar,
        loadingOverlay: LinearProgress,
        noRowsOverlay: () => (
          <DataStatePlaceholder label="carpool.settings.tab.vehicleManagement.vehicleByGroup.section.location.dataGrid.emptyText" />
        ),
      }}
      slotProps={{
        toolbar: KarooToolbar.createProps({
          slots: {
            searchFilter: { show: true },
            settingsButton: { show: true },
            filterButton: { show: true },
          },
          extraContent: {
            right: (
              <Button
                startIcon={<AddIcon />}
                onClick={() => {
                  dispatch({ type: 'add_location' })
                }}
                color="primary"
                variant="outlined"
                size="small"
              >
                {ctIntl.formatMessage({ id: 'Add Location' })}
              </Button>
            ),
            middle: (
              <Stack
                direction="row"
                gap={2}
              >
                <Stats
                  reversed
                  data={[
                    {
                      key: 'carpool.settings.tab.vehicleManagement.vehicleByGroup.section.location.stat1',
                      value: rows.length,
                    },
                    {
                      key: 'Vehicles',
                      value: vehiclesQuery.data?.vehicles.length ?? 0,
                    },
                  ]}
                  divider={
                    <Divider
                      orientation="vertical"
                      variant="middle"
                      flexItem
                    />
                  }
                />
                {rows.length > 0 && (
                  <>
                    <Divider
                      orientation="vertical"
                      variant="middle"
                      flexItem
                    />
                    <Stats
                      reversed
                      data={[
                        {
                          key: 'carpool.settings.tab.vehicleManagement.vehicleByGroup.section.unassignedStat',
                          value: counts.vehiclesWithoutLocationCount,
                        },
                      ]}
                      boxLabelProps={{
                        sx: ({ palette }) => ({ color: palette.Alert.errorColor }),
                      }}
                      boxValueProps={{ sx: { color: 'error.dark' } }}
                    />
                  </>
                )}
              </Stack>
            ),
          },
        }),
      }}
    />
  )
}
