import { useMemo } from 'react'
import { groupBy } from 'lodash'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useParams } from 'react-router'
import { isNonNullish } from 'remeda'

import { getUsersRecordById, type UsersRecordById } from 'duxs/admin'
import { apiCallerNoX } from 'src/api/api-caller'
import type { PlanRecurrence } from 'src/modules/deliveryRevamp/api/plans/types'
import {
  STOP_STATUS_ID,
  STOP_STATUS_ID_TO_TIME_KEY,
  STOP_TYPE_ID,
} from 'src/modules/deliveryRevamp/constants/job'
import { useDeliveryMainPageContext } from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import {
  formatTimeWindow,
  TIME_WINDOW_FORMAT,
} from 'src/modules/deliveryRevamp/helpers'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import { useDeliverySettingsContext } from '../../contexts/DeliverySettingsContext'
import { parseRollOverData, type RolledOverData } from '../../helpers'
import { calcJobActualDuration } from '../../helpers/job'
import { DELIVERY_PAGES } from '../../types'
import { DeliveryDateTime } from '../../utils/deliveryDateTime'
import { createSchedule, DELIVERY_SYNC_DATA_INTERVAL } from '../constants'
//types
import type { OrderedStopApiOutput, RouteApiOutput } from './types'

type ISODateTimeString = string // 2024-08-13T23:59:59+08:00
export type RouteRecurringData =
  | {
      isFullRecurring: false
      routeRecurrence: null
      recurrenceStartDate: null
      routeName?: string
    }
  | {
      isFullRecurring: true
      routeRecurrence: PlanRecurrence
      recurrenceStartDate: string | null
      planName: string
    }

type StopForCalcOrdering = {
  latitude: RouteApiOutput['orderedStops'][number]['latitude']
  longitude: RouteApiOutput['orderedStops'][number]['longitude']
  stopStatusId: RouteApiOutput['orderedStops'][number]['stopStatusId']
}
export declare namespace RoutesList {
  type ApiInput =
    | {
        scheduleType: 'scheduled'
        filter: {
          from: ISODateTimeString
          to: ISODateTimeString
        }
      }
    | {
        scheduleType: 'unscheduled'
      }

  type ApiOutput = {
    routes: Array<RouteApiOutput>
  }
  type Return = ReturnType<typeof parseRoutesList>
  type Route = Return[number]
  type Stop = Return[number]['orderedStops'][number]
}

const createKey = (payload?: RoutesList.ApiInput) =>
  payload
    ? ['deliveryRevamp/routesList', payload]
    : (['deliveryRevamp/routesList'] as const)

export const useUpdateRouteInList = () => {
  const queryClient = useQueryClient()
  const {
    data: { selectedDateRange },
  } = useDeliveryMainPageContext()

  const payload = createSchedule(selectedDateRange, true)

  const updateRouteData = (routeId: string, routeData: Partial<RoutesList.Route>) => {
    queryClient.setQueryData(
      createKey(payload),
      (prevRoutes: Array<RoutesList.Route> | undefined) => {
        if (!prevRoutes) return prevRoutes

        return prevRoutes.map((item) =>
          item.routeId === routeId ? { ...item, ...routeData } : item,
        )
      },
    )
  }

  return { updateRouteData }
}

function useRoutesList() {
  const {
    data: { selectedDateRange },
  } = useDeliveryMainPageContext()
  const { page } = useParams<{ page: DELIVERY_PAGES }>()

  const payload = createSchedule(selectedDateRange, true)

  const usersById = useTypedSelector(getUsersRecordById)

  const { deliverySettings } = useDeliverySettingsContext()

  const rolledOverData = useMemo(() => {
    if (payload.scheduleType === 'unscheduled') return null
    const selectedDate = DeliveryDateTime.fromISO(payload.filter.from)
    const now = DeliveryDateTime.now()
    return {
      now,
      selectedDate,
      isPast: selectedDate.startOf('day') < now.startOf('day'),
      isToday: selectedDate.hasSame(now, 'day'),
      rolledOverDays: Number(deliverySettings.jobViewPreviousNumDays),
    } satisfies RolledOverData
  }, [deliverySettings.jobViewPreviousNumDays, payload])

  return useQuery<RoutesList.Return, Error>({
    queryKey: createKey(payload),
    ...(page === DELIVERY_PAGES.MAP && {
      refetchInterval: DELIVERY_SYNC_DATA_INTERVAL,
    }),
    queryFn: () => fetchRoutesList(payload, usersById, rolledOverData),
  })
}

async function fetchRoutesList(
  data: RoutesList.ApiInput,
  usersById: UsersRecordById,
  rolledOverData: RolledOverData,
) {
  return apiCallerNoX<RoutesList.ApiOutput>('delivery_timeline_route_list', {
    data,
  }).then((res) => parseRoutesList(res, usersById, rolledOverData))
}

const checkIsFullRecurringByOnePlan = (
  stops: RouteApiOutput['orderedStops'],
  planIds: Array<number>,
  hasRouteName: boolean,
  number: number,
): RouteRecurringData => {
  if (stops.length === 0)
    return {
      isFullRecurring: false,
      routeRecurrence: null,
      recurrenceStartDate: null,
    }

  const isAllStopsRecurring = stops.every((stop) => !!stop.recurrence)

  if (isAllStopsRecurring && planIds.length === 1 && stops[0].recurrence) {
    return {
      isFullRecurring: true,
      routeRecurrence: stops[0].recurrence,
      recurrenceStartDate: stops[0].scheduledDeliveryTs,
      planName: stops[0].planName as string,
    }
  }

  return {
    isFullRecurring: false,
    routeRecurrence: null,
    recurrenceStartDate: null,
    ...(!hasRouteName &&
      isAllStopsRecurring && {
        routeName: `${ctIntl.unsafe_formatMessage({
          id: 'Multiple recurring routes',
        })} ${number}`,
      }),
  }
}

const checkHasLateRisk = (
  curStop: OrderedStopApiOutput,
  preStop?: OrderedStopApiOutput,
  isGrouped?: boolean,
  hasPreStopLateRisk?: boolean,
  lastArrivedOrCompletedTime?: string | null,
) => {
  if (!preStop) return false
  if (!curStop.deliveryWindows || curStop.deliveryWindows.length === 0) return false
  const timeTo = curStop.deliveryWindows[0].timeTo
  const timeToInDateTime = DeliveryDateTime.fromFormat(timeTo, TIME_WINDOW_FORMAT)

  if (
    isGrouped &&
    hasPreStopLateRisk &&
    lastArrivedOrCompletedTime &&
    curStop.etaInSeconds
  ) {
    // This is special case for grouped stops. Refer #3798
    return (
      DeliveryDateTime.fromJSDate(new Date(lastArrivedOrCompletedTime)).plus({
        seconds: curStop.etaInSeconds,
      }) > timeToInDateTime
    )
  }

  if (
    ![STOP_STATUS_ID.ARRIVED, STOP_STATUS_ID.COMPLETED].includes(
      preStop.stopStatusId,
    ) ||
    ![STOP_STATUS_ID.CREATED, STOP_STATUS_ID.STARTED].includes(curStop.stopStatusId)
  )
    return false

  const preStopTime = preStop[STOP_STATUS_ID_TO_TIME_KEY[preStop.stopStatusId]]

  if (!preStopTime) return false

  let travelTimeFromPreStop = null
  if (isNonNullish(curStop.etaInSeconds) && isNonNullish(preStop.etaInSeconds)) {
    travelTimeFromPreStop = curStop.etaInSeconds - preStop.etaInSeconds
  }

  if (typeof travelTimeFromPreStop === 'number') {
    return (
      DeliveryDateTime.fromJSDate(new Date(preStopTime)).plus({
        seconds: travelTimeFromPreStop,
      }) > timeToInDateTime || DeliveryDateTime.now() > timeToInDateTime
    )
  }

  return false
}

const checkIsSameCoordsAndStatusWithPreStop = (
  curStop: StopForCalcOrdering,
  preStop?: StopForCalcOrdering,
) => {
  if (!preStop) return false
  const latLng: `${number},${number}` = `${curStop.latitude},${curStop.longitude}`
  const preStopLatLng = `${preStop.latitude},${preStop.longitude}`
  return latLng === preStopLatLng && curStop.stopStatusId === preStop.stopStatusId
}

export const calculateOrdering = ({
  currentOrdering,
  stop,
  preStop,
  nextStop,
}: {
  currentOrdering: number
  stop: StopForCalcOrdering
  preStop?: StopForCalcOrdering
  nextStop?: StopForCalcOrdering
}) => {
  let ordering = currentOrdering
  let isGrouped = checkIsSameCoordsAndStatusWithPreStop(stop, preStop)

  if (!isGrouped) {
    ordering++
  }

  // Handle the case that this stop is the first grouped stop
  isGrouped = isGrouped || checkIsSameCoordsAndStatusWithPreStop(stop, nextStop)

  return {
    ordering,
    isGrouped,
  }
}

const checkHasJobStartedWithPDCase = (
  curStop: OrderedStopApiOutput,
  preStop?: OrderedStopApiOutput,
): boolean => {
  if (curStop.stopTypeId !== STOP_TYPE_ID.DROPOFF) {
    return Boolean(curStop.activityStartedTs)
  }

  const isDropoffCompleted = curStop.stopStatusId === STOP_STATUS_ID.COMPLETED
  if (!preStop) return isDropoffCompleted

  if (preStop.jobId !== curStop.jobId) return isDropoffCompleted

  const pickupStartDate = preStop.activityStartedTs
    ? DeliveryDateTime.fromJSDate(new Date(preStop.activityStartedTs))
    : null

  if (!pickupStartDate) return Boolean(curStop.activityStartedTs)

  const daysSincePickup = pickupStartDate.diffNow('days').days

  return daysSincePickup < 0 ? isDropoffCompleted : true
}

const parseRoutesList = (
  { routes }: RoutesList.ApiOutput,
  usersById: UsersRecordById,
  rolledOverData: RolledOverData,
) => {
  let multiRecurringRoutesNumber = 1

  return routes.map((route) => {
    const jobIdMapToStopIds: Record<number, Array<number>> = {}
    const notStartedJobIds: Array<number> = []
    const startedJobIds: Array<number> = []
    const completedJobIds: Array<number> = []
    let completedJobs = 0
    let isOnRoute = false

    for (const stops of Object.values(groupBy(route.orderedStops, 'jobId'))) {
      jobIdMapToStopIds[stops[0].jobId] = stops.map((stop) => stop.stopId)

      if (stops.every((stop) => stop.stopStatusId === STOP_STATUS_ID.COMPLETED)) {
        completedJobs++
        completedJobIds.push(stops[0].jobId)
      } else if (
        stops.every((stop) =>
          [STOP_STATUS_ID.CREATED, STOP_STATUS_ID.REJECTED].includes(stop.stopStatusId),
        )
      ) {
        notStartedJobIds.push(stops[0].jobId)
      } else {
        startedJobIds.push(stops[0].jobId)
      }

      if (
        !isOnRoute &&
        stops.some((stop) =>
          [STOP_STATUS_ID.STARTED, STOP_STATUS_ID.ARRIVED].includes(stop.stopStatusId),
        )
      ) {
        isOnRoute = true
      }
    }

    const recurringData = checkIsFullRecurringByOnePlan(
      route.orderedStops,
      route.planIds,
      !!route.routeName,
      multiRecurringRoutesNumber,
    )

    if (!recurringData.isFullRecurring && recurringData.routeName) {
      multiRecurringRoutesNumber++
    }

    const uniquePlanId = route.planIds.length === 1 ? route.planIds[0] : null
    const notEmptyRecurringPlanIds: Array<number> = []

    let lateCount = 0
    let currentOrdering = 0

    const lastStartedIndex = route.orderedStops.findLastIndex(
      (s) => s.stopStatusId !== STOP_STATUS_ID.CREATED,
    )

    let hasPreStopLateRisk = false
    const lastArrivedOrCompletedStop = route.orderedStops.findLast((stop) =>
      [STOP_STATUS_ID.ARRIVED, STOP_STATUS_ID.COMPLETED].includes(stop.stopStatusId),
    )
    const lastArrivedOrCompletedTime = lastArrivedOrCompletedStop
      ? lastArrivedOrCompletedStop[
          STOP_STATUS_ID_TO_TIME_KEY[lastArrivedOrCompletedStop.stopStatusId]
        ]
      : null

    return {
      ...route,
      startedJobIds,
      notStartedJobIds,
      completedJobIds,
      assignableJobIds: [...startedJobIds, ...notStartedJobIds],
      jobIdMapToStopIds,
      orderedStops: route.orderedStops.map((stop, index, stops) => {
        const {
          isLate,
          formattedTimeWindow,
          earliestTimeWindowInDateTime,
          latestTimeWindowInDateTime,
        } = formatTimeWindow(
          stop.deliveryWindows,
          stop[STOP_STATUS_ID_TO_TIME_KEY[stop.stopStatusId]],
        )
        const latLng: `${number},${number}` = `${stop.latitude},${stop.longitude}`
        const preStop = route.orderedStops[index - 1]

        const { ordering, isGrouped } = calculateOrdering({
          currentOrdering,
          stop,
          preStop,
          nextStop: route.orderedStops[index + 1],
        })

        currentOrdering = ordering

        const hasLateRiskBasedOnPreStop = checkHasLateRisk(
          stop,
          preStop,
          isGrouped,
          hasPreStopLateRisk,
          lastArrivedOrCompletedTime,
        )

        if (
          isLate &&
          [STOP_STATUS_ID.ARRIVED, STOP_STATUS_ID.COMPLETED].includes(stop.stopStatusId)
        )
          lateCount++

        if (
          stop.planId &&
          route.planIds.includes(stop.planId) &&
          stop.recurrence &&
          !notEmptyRecurringPlanIds.includes(stop.planId)
        ) {
          notEmptyRecurringPlanIds.push(stop.planId)
        }

        let disableBeforeIndex = lastStartedIndex
        let disableAfterIndex = stops.length
        if (stop.stopTypeId === STOP_TYPE_ID.DROPOFF) {
          const pickupIndex = stops.findIndex(
            (s) => s.jobId === stop.jobId && s.stopTypeId === STOP_TYPE_ID.PICKUP,
          )
          disableBeforeIndex = Math.max(disableBeforeIndex, pickupIndex)
        }
        if (stop.stopTypeId === STOP_TYPE_ID.PICKUP) {
          const dropOffIndex = stops.findIndex(
            (s) => s.jobId === stop.jobId && s.stopTypeId === STOP_TYPE_ID.DROPOFF,
          )
          disableAfterIndex = Math.min(disableAfterIndex, dropOffIndex)
        }

        hasPreStopLateRisk = hasLateRiskBasedOnPreStop

        return {
          ...stop,
          ordering,
          actualDurationInMinutes: calcJobActualDuration(
            stop.activityCompletedTs,
            stop.activityArrivedTs,
          ),
          canReassign: !completedJobIds.includes(stop.jobId),
          subUser: stop.subuserId ? (usersById[stop.subuserId] ?? null) : null,
          planOrder:
            route.planIds.length > 1 &&
            stop.planId &&
            notEmptyRecurringPlanIds.includes(stop.planId)
              ? notEmptyRecurringPlanIds.indexOf(stop.planId) + 1
              : null,
          isLate,
          formattedTimeWindow,
          earliestTimeWindowInDateTime,
          latestTimeWindowInDateTime,
          hasLateRiskBasedOnPreStop,
          latLng,
          isGrouped,
          rolledOver: parseRollOverData({
            scheduledDate: stop.deliveryDate || stop.scheduledDeliveryTs,
            hasJobStarted: Boolean(stop.activityStartedTs),
            rolledOverData,
            routeId: route.routeId,
            hasJobStartedWithPDCase: checkHasJobStartedWithPDCase(stop, preStop),
          }),
          disableBeforeIndex,
          disableAfterIndex,
          indexInRoute: index,
        }
      }),
      deliveryDriverId: route.routeId.startsWith('driver_')
        ? route.routeId.split('_')[1]
        : null,
      planId: route.routeId.startsWith('plan_')
        ? Number(route.routeId.split('_')[1])
        : null,
      lateCount,
      completedJobs,
      totalJobs: startedJobIds.length + notStartedJobIds.length + completedJobs,
      uniquePlanId,
      ...(uniquePlanId &&
        route.orderedStops.length > 0 && {
          routeName: route.orderedStops[0].planName,
        }),
      ...recurringData,
      driverFullname: route.driverFullname,
      isOnRoute,
    }
  })
}

export default Object.assign(useRoutesList, {
  createKey,
})
