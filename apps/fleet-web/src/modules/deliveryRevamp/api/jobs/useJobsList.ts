import { useQuery } from '@tanstack/react-query'

import { getUsersRecordById, type UsersRecordById } from 'duxs/admin'
import { apiCallerNoX } from 'src/api/api-caller'
import { JOB_STATUS_ID } from 'src/modules/deliveryRevamp/constants/job'
import {
  useDeliveryMainPageContext,
  type DeliveryMainPageContextType,
} from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import { formatTimeWindow, normalizeData } from 'src/modules/deliveryRevamp/helpers'
import { useTypedSelector } from 'src/redux-hooks'

import { getDeliveryRevampSearchTermFilter } from '../../components/JobsLeftPanel/slice'
import { createSchedule, DELIVERY_SYNC_DATA_INTERVAL } from '../constants'
import type { JobApiOutput } from './types'

type ISODateTimeString = string

export declare namespace JobsList {
  type ApiInput =
    | {
        scheduleType: 'scheduled'
        filter: {
          from: ISODateTimeString
          to: ISODateTimeString
        }
      }
    | {
        scheduleType: 'unscheduled'
      }
  type ApiOutput = {
    jobs: Array<JobApiOutput>
  }
  type Return = ReturnType<typeof parseJobsList>
  type Job = Return['byId'][number]
  type Stop = Job['stops'][number]
}

const createKey = (payload?: JobsList.ApiInput) =>
  payload
    ? ['deliveryRevamp/jobsList', payload]
    : (['deliveryRevamp/jobsList'] as const)

function useJobsList(
  passedSelectedDateRange?: DeliveryMainPageContextType['data']['selectedDateRange'],
) {
  const {
    data: { selectedDateRange },
  } = useDeliveryMainPageContext()

  const payload = createSchedule(passedSelectedDateRange || selectedDateRange)

  const usersById = useTypedSelector(getUsersRecordById)

  const searchTerm = useTypedSelector(getDeliveryRevampSearchTermFilter)

  return useQuery({
    queryKey: createKey(payload),
    queryFn: () => fetchJobsList(payload, usersById),
    select: (data) => {
      if (searchTerm.trim() === '') return data
      return filterJobsBySearchTerm(data, searchTerm)
    },
    refetchInterval: DELIVERY_SYNC_DATA_INTERVAL,
  })
}

function filterJobsBySearchTerm(
  data: ReturnType<typeof parseJobsList>,
  searchTerm: string,
) {
  const lowercaseSearchTerm = searchTerm.toLowerCase().trim()
  const allIds = data.allIds.filter((id) => {
    const job = data.byId[id]
    return job.stops.some(
      (stop) =>
        stop.addressLine1.toLowerCase().includes(lowercaseSearchTerm) ||
        (stop.addressLine2 &&
          stop.addressLine2.toLowerCase().includes(lowercaseSearchTerm)) ||
        (stop.customerId &&
          stop.customerId.toLowerCase().includes(lowercaseSearchTerm)) ||
        (stop.customerName &&
          stop.customerName.toLowerCase().includes(lowercaseSearchTerm)) ||
        (stop.referenceNumber &&
          stop.referenceNumber.toLowerCase().includes(lowercaseSearchTerm)) ||
        (stop.orderId && stop.orderId.toLowerCase().includes(lowercaseSearchTerm)),
    )
  })
  return {
    ...data,
    allIds,
  }
}

async function fetchJobsList(data: JobsList.ApiInput, usersById: UsersRecordById) {
  return apiCallerNoX<JobsList.ApiOutput>('delivery_jobs_list_new', { data }).then(
    (res) => parseJobsList(res, usersById),
  )
}

const parseJobsList = ({ jobs }: JobsList.ApiOutput, usersById: UsersRecordById) =>
  normalizeData(
    jobs.map((job) => ({
      ...job,
      stops: job.stops.map((stop, index) => {
        const latLng: `${number},${number}` = `${stop.latitude},${stop.longitude}`
        return {
          ...stop,
          jobId: job.jobId,
          activityRejectedTs: job.rejectedTs,
          jobStatusId: job.jobStatusId,
          referenceNumber: job.referenceNumber,
          orderId: job.orderId,
          rejectedReason: job.rejectedReason,
          canReassign: JOB_STATUS_ID.CANCELED !== job.jobStatusId,
          ordering: index + 1, // If the BE starts sending this we can remove this parser
          subUser: job.subuserId ? (usersById[job.subuserId] ?? null) : null,
          latLng,
          isGrouped: false,
          ...formatTimeWindow(stop.deliveryWindows, null),
        }
      }),
    })),
    'jobId',
  )

export default Object.assign(useJobsList, {
  createKey,
})
