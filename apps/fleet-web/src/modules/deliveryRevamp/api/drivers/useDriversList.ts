import { useQuery, useQueryClient } from '@tanstack/react-query'
import type { DateTime } from 'luxon'
import { match, P } from 'ts-pattern'

import { getUsersRecordById, type UsersRecordById } from 'duxs/admin'
import { API_URL, apiCallerNoX } from 'src/api/api-caller'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import { DELIVERY_COLOR } from '../../constants/colors'
import { useDeliveryMainPageContext } from '../../contexts/DeliveryMainPageContext'
import { useDeliverySettingsContext } from '../../contexts/DeliverySettingsContext'
import {
  formatSinceDatetimeAndRelative,
  formatSpecialEquipments,
  normalizeData,
} from '../../helpers'
import { DeliveryDateTime } from '../../utils/deliveryDateTime'
import { DELIVERY_SYNC_DATA_INTERVAL } from '../constants'
import type { DeliverySettings } from '../settings/useDeliverySettingsQuery'
import type { DriverApiOutput } from './types'

export declare namespace DriversList {
  type Payload = DateTime | null
  // Diff inputs only affect jobs count
  type ApiInput = {
    withStats:
      | {
          from: string
          to: string
        }
      | {
          // Means unschedule
          from: null
          to: null
        }
  }
  type ApiOutput = {
    drivers: Array<DriverApiOutput>
  }
  type Return = ReturnType<typeof normalizeResponse>
  type Driver = ReturnType<typeof parseDriversDetails>
}

// Sort order:
enum SORT {
  ONLINE = 100,
  OFFLINE_MODE = 200,
  ON_BREAK = 300,
  NOT_ACTIVE = 400,
  OFFLINE = 500,
}

export const STATUS_COLOR = {
  OFFLINE: DELIVERY_COLOR.GREY6,
  NOT_ACTIVE: DELIVERY_COLOR.ORANGE,
  ON_BREAK: DELIVERY_COLOR.RED,
  ONLINE: DELIVERY_COLOR.GREEN,
} as const

// Must start with prefix 'driver' to be included in the driver entity invalidation
export const createKey = (data?: DriversList.ApiInput) =>
  ['driver', 'deliveryRevamp/getDeliveryDrivers', ...(data ? [data] : [])] as const

export const payloadToApiInput = (
  payload: DriversList.Payload,
): DriversList.ApiInput => ({
  withStats: payload
    ? {
        from: payload.startOf('day').toISO({
          suppressMilliseconds: true,
        }),
        to: payload
          .endOf('day')
          .startOf('second')
          .toISO({ suppressMilliseconds: true }),
      }
    : {
        from: null,
        to: null,
      },
})

const useDriversList = (payload?: DriversList.Payload) => {
  const usersById = useTypedSelector(getUsersRecordById)
  const { deliverySettings } = useDeliverySettingsContext()
  const {
    data: { selectedDateRange },
  } = useDeliveryMainPageContext()
  const finalPayload = payload || (selectedDateRange ? selectedDateRange.start : null)
  const data = payloadToApiInput(finalPayload)
  return useQuery<DriversList.Return, Error>({
    queryKey: createKey(data),
    queryFn: () => fetchDriversList(data, usersById, deliverySettings),
    ...(finalPayload &&
      finalPayload.hasSame(DeliveryDateTime.now(), 'day') && {
        refetchInterval: DELIVERY_SYNC_DATA_INTERVAL,
      }),
  })
}

const normalizeResponse = (
  res: DriversList.ApiOutput,
  usersById: UsersRecordById,
  deliverySettings: DeliverySettings.Return,
) =>
  normalizeData(
    parseDrivers(res.drivers, usersById, deliverySettings),
    'deliveryDriverId',
  )

const fetchDriversList = (
  data: DriversList.ApiInput,
  usersById: UsersRecordById,
  deliverySettings: DeliverySettings.Return,
) =>
  apiCallerNoX<DriversList.ApiOutput>('delivery_drivers_list', { data }).then((res) =>
    normalizeResponse(res, usersById, deliverySettings),
  )

function reduceToFloatBetweenZeroAndTen(num: number) {
  const logValue = Math.log10(num)
  const scaledValue = logValue % 10
  return scaledValue
}

function defineOrdering(sort: SORT, driver: DriverApiOutput) {
  const UP = 0
  const DOWN = 10
  const totalJobs = driver.stats.totalJobs || 0
  const startedJobs = driver.stats.startedJobs || 0
  let ordering = sort

  if ([SORT.ONLINE, SORT.OFFLINE_MODE, SORT.ON_BREAK].includes(sort)) {
    ordering += totalJobs === 0 ? UP : DOWN
    ordering += startedJobs > 0 ? DOWN : UP
    if (totalJobs > 0) {
      ordering += reduceToFloatBetweenZeroAndTen(totalJobs)
    }
  } else if ([SORT.NOT_ACTIVE, SORT.OFFLINE].includes(sort)) {
    ordering += totalJobs === 0 ? DOWN : UP
    ordering += startedJobs > 0 ? UP : DOWN
    if (totalJobs > 0) {
      ordering -= reduceToFloatBetweenZeroAndTen(totalJobs)
    }
  }

  return ordering
}

function checkIsNotActive(ts: string, mins: number) {
  return (
    Math.abs(DeliveryDateTime.fromJSDate(new Date(ts)).diffNow('minutes').minutes) >=
    mins
  )
}

export const parseDrivers = (
  res: DriversList.ApiOutput['drivers'],
  usersById: UsersRecordById,
  deliverySettings: DeliverySettings.Return,
) =>
  res
    .map((driver) => parseDriversDetails(driver, usersById, deliverySettings))
    .sort((a, b) => a.ordering - b.ordering)

export const parseDriversDetails = (
  driver: DriverApiOutput,
  usersById: UsersRecordById,
  deliverySettings: DeliverySettings.Return,
) => {
  let ordering = 0
  let isOnline = false
  let isNotActive = false
  let isOnBreak = false
  let isOffline = false
  const isOnRoute = driver.stats.startedJobs > 0
  const [statusColor, statusText, statusDesc, statusTimeDesc] = match(driver)
    .with({ offlineModeSinceTs: P.nonNullable }, ({ offlineModeSinceTs }) => {
      ordering += defineOrdering(SORT.OFFLINE_MODE, driver)
      return [
        STATUS_COLOR.OFFLINE,
        ctIntl.formatMessage({ id: 'Offline' }),
        ctIntl.formatMessage({ id: 'This driver switched to offline mode' }),
        formatSinceDatetimeAndRelative(offlineModeSinceTs),
      ]
    })
    .with({ isLoggedIn: false, offlineModeSinceTs: P.nullish }, ({ lastLogoutTs }) => {
      ordering += defineOrdering(SORT.OFFLINE, driver)
      isOffline = true
      return [
        STATUS_COLOR.OFFLINE,
        ctIntl.formatMessage({ id: 'Offline' }),
        ctIntl.formatMessage({ id: 'This driver has logged out of the app.' }),
        lastLogoutTs ? formatSinceDatetimeAndRelative(lastLogoutTs) : '',
      ]
    })
    .with({ onBreakSinceTs: P.nonNullable }, ({ onBreakSinceTs }) => {
      ordering += defineOrdering(SORT.ON_BREAK, driver)
      isOnBreak = true
      return [
        STATUS_COLOR.ON_BREAK,
        ctIntl.formatMessage({ id: 'On break' }),
        ctIntl.formatMessage({ id: 'This driver switched to ‘on break’' }),
        formatSinceDatetimeAndRelative(onBreakSinceTs),
      ]
    })
    .with(
      {
        isLoggedIn: true,
        lastOnlineTs: P.when(
          (lastOnlineTs) =>
            lastOnlineTs &&
            checkIsNotActive(
              lastOnlineTs,
              Number(deliverySettings.driverTimeoutOffline),
            ),
        ),
      },
      ({ lastOnlineTs }) => {
        ordering += defineOrdering(SORT.NOT_ACTIVE, driver)
        isNotActive = true
        return [
          STATUS_COLOR.NOT_ACTIVE,
          ctIntl.formatMessage({ id: 'delivery.driverStatus.notActive' }),
          ctIntl.formatMessage({ id: 'This driver has not been active.' }),
          match({ lastOnlineTs })
            .with({ lastOnlineTs: P.nonNullable }, ({ lastOnlineTs }) =>
              formatSinceDatetimeAndRelative(lastOnlineTs),
            )
            .otherwise(() => ''),
        ]
      },
    )
    .with(
      {
        isLoggedIn: true,
      },
      () => {
        ordering += defineOrdering(SORT.ONLINE, driver)
        isOnline = true
        return [STATUS_COLOR.ONLINE, ctIntl.formatMessage({ id: 'Online' }), '', '']
      },
    )
    .otherwise(() => [STATUS_COLOR.OFFLINE, '', '', ''])

  const isBusy =
    !isOffline &&
    (!isNotActive ||
      (isNotActive &&
        driver.lastOnlineTs &&
        DeliveryDateTime.fromJSDate(new Date(driver.lastOnlineTs)).hasSame(
          DeliveryDateTime.now(),
          'day',
        ))) &&
    driver.stats.pendingStops > Number(deliverySettings.driverMaxStopsBusy)

  return {
    ...driver,
    fullName: `${driver.firstName || ''} ${driver.lastName || ''}`.trim(),
    maxWeight: driver.itemContainer?.maxWeight,
    maxVolume: driver.itemContainer?.maxVolume,
    deliveryCapabilities:
      (driver.itemContainer &&
        driver.itemContainer.deliveryCapabilities &&
        driver.itemContainer.deliveryCapabilities.map((specialEquipment) => ({
          value: +specialEquipment.id,
          label: !specialEquipment.userId
            ? ctIntl.formatMessage({
                id: formatSpecialEquipments(specialEquipment.name),
              })
            : specialEquipment.name,
          name: !specialEquipment.userId
            ? ctIntl.formatMessage({
                id: formatSpecialEquipments(specialEquipment.name),
              })
            : specialEquipment.name,
        }))) ||
      [],
    subUser: driver.subuserId ? (usersById[driver.subuserId] ?? null) : null,
    ordering,
    statusColor: statusColor as (typeof STATUS_COLOR)[keyof typeof STATUS_COLOR],
    statusText,
    statusDesc,
    statusTimeDesc,
    isNotActive: isNotActive as boolean,
    isOnline: isOnline as boolean,
    isOnBreak: isOnBreak as boolean,
    isOnRoute,
    isBusy,
    avatarUrl: driver.fleetDriverId
      ? `${API_URL.replace('/index.php', '')}/driverpic?driver=${driver.fleetDriverId}`
      : undefined,
  }
}

export const useCachedDriversList = () => {
  const queryClient = useQueryClient()

  // Allows to get the drivers list through the query key without using exact match
  const query = queryClient.getQueriesData<DriversList.Return>({
    queryKey: createKey(),
  })

  if (query.length > 0) {
    const queryData = query[0][1]
    return queryData
  }

  return undefined
}

export default Object.assign(useDriversList, { createKey })
