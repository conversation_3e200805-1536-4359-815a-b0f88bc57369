import { clone, groupBy, isEmpty } from 'lodash'
import { match, P } from 'ts-pattern'

import {
  ITEM_TYPE_DESC,
  ITEM_TYPE_ID,
  JOB_STOP_TYPE_ID,
  LENGTH_OPTIONS,
  TODO_TYPE_ID,
} from 'src/modules/deliveryRevamp/constants/job'
import { UNIQ_ID } from 'src/modules/deliveryRevamp/helpers'

import type { JobImporterError, JobImporterInput, JobImporterOutPut } from '../../types'
import { range } from '../../utils'
import { getTodoTypeId, jobTypeVerify } from '../helper'

const getTodoIdByTodoKey = (todoKey: string) => {
  if (todoKey === 'itemSignature') {
    return TODO_TYPE_ID.SIGNATURE
  }
  if (todoKey === 'itemPod') {
    return TODO_TYPE_ID.POD
  }
  if (todoKey === 'itemNote') {
    return TODO_TYPE_ID.NOTE
  }
  return 1
}

const getItemTypeId = (itemType: string) => {
  switch (itemType?.toLowerCase()) {
    case 'package': {
      return ITEM_TYPE_ID.PACKAGE
    }
    case 'service': {
      return ITEM_TYPE_ID.SERVICE
    }
    case 'person': {
      return ITEM_TYPE_ID.PERSON
    }
    default: {
      return ITEM_TYPE_ID.PACKAGE
    }
  }
}
export const getItemTypeTag = (todoTypeId: TODO_TYPE_ID) => {
  switch (+todoTypeId) {
    case TODO_TYPE_ID.SIGNATURE: {
      return 'todoSignature'
    }
    case TODO_TYPE_ID.SCAN_TO_ATTACH: {
      return 'todoScanToAttach'
    }
    case TODO_TYPE_ID.POD: {
      return 'todoPod'
    }
    case TODO_TYPE_ID.CHECKBOX: {
      return 'todoCheckBox'
    }
    case TODO_TYPE_ID.NOTE: {
      return 'todoNote'
    }
    case TODO_TYPE_ID.EXPENSES: {
      return 'todoExpenses'
    }
    default: {
      return 'todoSignature'
    }
  }
}

const isPartOfTodoTypeId = (todoKey: string) =>
  Object.values(TODO_TYPE_ID).includes(
    todoKey.toString()?.includes('|') ? +todoKey.split('|')[0] : +todoKey,
  )

function getPriorityItemTodosInfo(input: string): Array<string> {
  if (!input.trim()) return []

  // handle cases like 125,125
  if (/^\d+(,\d+)*$/.test(input.trim())) {
    const splitted = input.split(',')
    return splitted.flatMap((numStr, index) =>
      numStr
        .trim()
        .split('')
        .map((n) => {
          const stopTypeId = match({ splittedLength: splitted.length, index })
            .with(
              { splittedLength: P.when((len) => len > 1), index: 0 },
              () => JOB_STOP_TYPE_ID.PICKUP,
            )
            .with(
              { splittedLength: P.when((len) => len > 1) },
              () => JOB_STOP_TYPE_ID.DROPOFF,
            )
            .otherwise(() => JOB_STOP_TYPE_ID.DROPOFF)
          return `${n}|${stopTypeId}|`
        }),
    )
  }

  // handle cases like (1, aa),(2, bb)
  const parts = input.split(/\),\s*\(/).map((part) => part.trim())

  let stopTypeId =
    input.trim().endsWith('),') || parts.length === 2
      ? JOB_STOP_TYPE_ID.PICKUP
      : JOB_STOP_TYPE_ID.DROPOFF

  const result: Array<string> = []

  const parsePart = (str: string, currentcurStopTypeId: number) => {
    const regex = /\((\d+),?\s*([^)]{1,100})\)/g
    let match
    while ((match = regex.exec(str)) !== null) {
      const todoTypeId = Number(match[1])
      const description = match[2].trim()
      const assignedcurStopTypeId = match[0].includes(',')
        ? currentcurStopTypeId
        : JOB_STOP_TYPE_ID.DROPOFF
      if (!Number.isNaN(todoTypeId)) {
        result.push(`${todoTypeId}|${assignedcurStopTypeId}|${description}`)
      }
    }
  }

  for (const [index, part] of parts.entries()) {
    parsePart(`(${part})`, stopTypeId)
    if (index === 0 && input.includes('),')) {
      stopTypeId = JOB_STOP_TYPE_ID.DROPOFF
    }
  }

  return result
}

// Item Generation
const generateItemLvlTodos = (
  taggedStopItemObject: JobImporterInput.ParsedObject,
  verifiedJobType: {
    isPickUpDropOff: boolean
    isSingleJobPickupDropOff: boolean
    isMultiJobPickupDropOff: boolean
    isSingleStopJob: boolean
    isMultiJobSingleStop: boolean
  },
  priorityItemTodoKey: string,
  key: string,
  tag?: string,
) => {
  const priorityTodos =
    getPriorityItemTodosInfo(
      taggedStopItemObject[priorityItemTodoKey]?.split('|')[0],
    ) || []

  if (verifiedJobType.isSingleStopJob || verifiedJobType.isMultiJobSingleStop) {
    const isPriorityTodoEmpty = isEmpty(priorityTodos)
    const valueInExcel: string | undefined = taggedStopItemObject[key].split('|')[0]
    const isCustomTodoDesc = isPriorityTodoEmpty && valueInExcel
    const itemTodos = (
      isCustomTodoDesc
        ? [valueInExcel]
        : priorityTodos.filter((todoKey: string) => isPartOfTodoTypeId(todoKey))
    )
      .map((todoKey: string, index: number) => {
        const formattedTodoKeyTodoTypeId = isPriorityTodoEmpty
          ? getTodoIdByTodoKey(key)
          : getTodoTypeId(
              todoKey.toString()?.includes('|') ? +todoKey.split('|')[0] : +todoKey,
            )
        return {
          todoTypeId: formattedTodoKeyTodoTypeId,
          cellLocation: isPriorityTodoEmpty
            ? taggedStopItemObject[key].split('|')[1]
            : taggedStopItemObject[priorityItemTodoKey].split('|')[1],
          stopTypeId: JOB_STOP_TYPE_ID.SINGLE,
          isRequired: true,
          tag: isPriorityTodoEmpty
            ? `${tag}`
            : `${getItemTypeTag(formattedTodoKeyTodoTypeId)}${index}${UNIQ_ID()}`,
          description: isCustomTodoDesc ? valueInExcel : todoKey.split('|')[2] || '',
        }
      })
      .filter((removeEmpty) => removeEmpty !== undefined)

    return itemTodos as Array<{
      todoTypeId: number
      cellLocation: any
      stopTypeId: number
      isRequired: boolean
      tag: string
      description: string
    }>
  }

  if (
    verifiedJobType.isMultiJobPickupDropOff ||
    verifiedJobType.isSingleJobPickupDropOff
  ) {
    const isPriorityTodoEmpty = isEmpty(priorityTodos.filter((i: string) => +i !== 0))

    const itemTodos = (
      isPriorityTodoEmpty
        ? range(+(taggedStopItemObject[key] || '').split('|')[0]).map((n) =>
            n.toString(),
          )
        : priorityTodos.filter((todoKey: string) => isPartOfTodoTypeId(todoKey))
    )
      .map((todoKey: string, index: number) => {
        const todoTypeId = isPriorityTodoEmpty
          ? getTodoIdByTodoKey(key)
          : getTodoTypeId(
              todoKey.toString()?.includes('|') ? +todoKey.split('|')[0] : +todoKey,
            )

        const stopTypeId =
          isPriorityTodoEmpty || !todoKey.toString()?.includes('|')
            ? JOB_STOP_TYPE_ID.DROPOFF
            : +todoKey.split('|')[1]

        if (
          JOB_STOP_TYPE_ID.DROPOFF === stopTypeId &&
          todoTypeId === TODO_TYPE_ID.SCAN_TO_ATTACH
        )
          return

        return {
          todoTypeId,
          cellLocation: isPriorityTodoEmpty
            ? taggedStopItemObject[key].split('|')[1]
            : taggedStopItemObject[priorityItemTodoKey].split('|')[1],
          stopTypeId,
          isRequired: true,
          tag: isPriorityTodoEmpty
            ? `${tag}`
            : `${getItemTypeTag(todoTypeId)}${index}${UNIQ_ID()}`,
          description: todoKey.split('|').length === 3 ? todoKey.split('|')[2] : '',
        }
      })
      .filter((removeEmpty) => removeEmpty !== undefined)
    return itemTodos as Array<{
      todoTypeId: number
      cellLocation: any
      stopTypeId: number
      isRequired: boolean
      tag: string
      description: string
    }>
  }
  return [] as Array<{
    todoTypeId: number
    cellLocation: any
    stopTypeId: number
    isRequired: boolean
    tag: string
    description: string
  }>
}

const createTodosArray = (
  item: JobImporterInput.ParsedObject,
  taggedDropOff: JobImporterInput.ParsedObject,
  verifiedJobType: ReturnType<typeof jobTypeVerify>,
  index: number,
) => {
  let todos: ReturnType<typeof generateItemLvlTodos> = []

  if (item.itemTodos || taggedDropOff) {
    todos = item.itemTodos //isPriorityItemTodos
      ? generateItemLvlTodos(
          taggedDropOff,
          verifiedJobType,
          'itemTodos',
          'itemSignature',
          `todoSignature${index}`,
        )
      : [
          ...generateItemLvlTodos(
            taggedDropOff,
            verifiedJobType,
            'itemTodos',
            'itemSignature',
            `todoSignature${index}`,
          ),
          ...generateItemLvlTodos(
            taggedDropOff,
            verifiedJobType,
            'itemTodos',
            'itemNote',
            `todoNote${index}`,
          ),
          ...generateItemLvlTodos(
            taggedDropOff,
            verifiedJobType,
            'itemTodos',
            'itemPod',
            `todoPod${index}`,
          ),
        ]
  }

  return taggedDropOff || item.itemTodos ? todos : []
}

const isGenerateItems = (data: JobImporterInput.ParsedObject) =>
  data.itemType ||
  data.itemTodos ||
  data.itemName ||
  data.itemQuantity ||
  data.itemWeight ||
  data.itemSize ||
  data.trackingCode ||
  data.itemSignature ||
  data.itemPod ||
  data.itemNote ||
  data.sku ||
  data.upc

// This means to use the itemTodos in Excel to apply to template's items if the template has items
const isReplaceable = (data: JobImporterInput.ParsedObject) =>
  !data.itemType && isGenerateItems(data) && { isReplaceable: true }

export const generateItems = (
  itemArray: Array<JobImporterInput.ParsedObject> = [],
  taggedArray: Array<JobImporterInput.ParsedObject> = [],
): {
  items: Array<JobImporterOutPut.Item>
  itemErrors: Array<JobImporterError.Error>
} => {
  const errors = [] as Array<JobImporterError.Error>
  const pickUpArr = itemArray.filter((item) => {
    const stopType = item?.stopType?.toLowerCase().trim()
    return ['p', 'pickup'].includes(stopType)
  })
  let dropOffArr = itemArray.filter((item) => {
    const stopType = item?.stopType?.toLowerCase().trim()
    return !['p', 'pickup'].includes(stopType)
  })

  const taggedPickUpArr = taggedArray.filter((item) => {
    const stopType = item?.stopType.split('|')[0]?.toLowerCase().trim()
    return ['p', 'pickup'].includes(stopType)
  })
  let taggedDropOffArr = taggedArray.filter((item) => {
    const stopType = item?.stopType.split('|')[0]?.toLowerCase().trim()
    return !['p', 'pickup'].includes(stopType)
  })

  const getActualStops = (key: string, value: Array<JobImporterInput.ParsedObject>) => {
    if (key === '') {
      return [...value]
    } else {
      const stopAllValues: JobImporterInput.ParsedObject = clone(value[0])
      for (const key of Object.keys(value[0])) {
        stopAllValues[key] = value?.find((n) => n[key])?.[key]
      }
      const rowLocation = value
        .map((stop) => stop?.rowLocation)
        .join(',')
        .toString()
      stopAllValues['rowLocation'] = rowLocation
      return [stopAllValues]
    }
  }

  const stopNoGroupedPickup = groupBy(pickUpArr, (n) =>
    n.orderNumber.trim()?.toLowerCase(),
  )
  const stopNoGroupedDropoff = groupBy(dropOffArr, (n) => (n?.stopNo || '').trim())
  let actualPickupStops = Object.entries(stopNoGroupedPickup).flatMap(([key, value]) =>
    getActualStops(key, value),
  )
  let actualDropoffStops = Object.entries(stopNoGroupedDropoff).flatMap(
    ([key, value]) => getActualStops(key, value),
  )

  //If pickup stops length > 0 BUT dropoff stops length is 0, treat is as SINGLE
  if (actualPickupStops.length > 0 && actualDropoffStops.length === 0) {
    actualDropoffStops = [...actualPickupStops]
    taggedDropOffArr = [...taggedPickUpArr]
    dropOffArr = [...pickUpArr]
    actualPickupStops = []
  }

  const verifiedJobType = jobTypeVerify(actualPickupStops, actualDropoffStops)
  const {
    isSingleJobPickupDropOff,
    isMultiJobPickupDropOff,
    isSingleStopJob,
    isMultiJobSingleStop,
  } = verifiedJobType

  if (isSingleJobPickupDropOff) {
    return errors.length > 0
      ? {
          items: [],
          itemErrors: errors,
        }
      : {
          items: dropOffArr
            .filter((dropOff) => isGenerateItems(dropOff))
            .map((item, index) => ({
              description:
                item.itemName || ITEM_TYPE_DESC[getItemTypeId(item.itemType)],
              weight: +(item.itemWeight ? item.itemWeight : '0'),
              rowLocation: item.rowLocation && item.rowLocation.toString(),
              itemTypeId: getItemTypeId(item.itemType),
              length: +item?.itemSize?.split(',')[0] || 0,
              width: +item?.itemSize?.split(',')[1] || 0,
              height: +item?.itemSize?.split(',')[2] || 0,
              dimensionId: LENGTH_OPTIONS.find((opt) => opt.label === item.itemSizeUnit)
                ?.value,
              ...(getItemTypeId(item.itemType) !== ITEM_TYPE_ID.SERVICE &&
              item?.trackingCode?.trim()
                ? { trackingNumber: item?.trackingCode }
                : {}),
              sku: item.sku,
              upc: item.upc,
              quantity: +item?.itemQuantity || 1,
              todos: createTodosArray(
                item,
                taggedDropOffArr[index],
                verifiedJobType,
                index,
              ),
              ...isReplaceable(item),
            })),
          itemErrors: errors,
        }
  }

  if (isMultiJobPickupDropOff) {
    return errors.length > 0
      ? {
          items: [],
          itemErrors: errors,
        }
      : {
          items: dropOffArr
            .filter((dropOff) => isGenerateItems(dropOff))
            .map((item, index) => ({
              description:
                item.itemName || ITEM_TYPE_DESC[getItemTypeId(item.itemType)],
              weight: +(item.itemWeight ? item.itemWeight : '0'),
              rowLocation: item?.rowLocation.toString(),
              itemTypeId: getItemTypeId(item.itemType),
              length: +item?.itemSize?.split(',')[0] || 0,
              width: +item?.itemSize?.split(',')[1] || 0,
              height: +item?.itemSize?.split(',')[2] || 0,
              ...(getItemTypeId(item.itemType) !== ITEM_TYPE_ID.SERVICE &&
              item?.trackingCode?.trim()
                ? { trackingNumber: item?.trackingCode }
                : {}),
              sku: item.sku,
              upc: item.upc,
              quantity: +item?.itemQuantity || 1,
              todos: createTodosArray(
                item,
                taggedDropOffArr[index],
                verifiedJobType,
                index,
              ),
              ...isReplaceable(item),
            })),
          itemErrors: errors,
        }
  }

  if (isSingleStopJob) {
    return errors.length > 0
      ? {
          items: [],
          itemErrors: errors,
        }
      : {
          items: dropOffArr
            .filter((dropOff) => isGenerateItems(dropOff))
            .map((item, index) => ({
              description:
                item.itemName || ITEM_TYPE_DESC[getItemTypeId(item.itemType)],
              weight: +(item.itemWeight ? item.itemWeight : '0'),
              rowLocation: item && item.rowLocation && item.rowLocation.toString(),
              itemTypeId: getItemTypeId(item.itemType),
              length: (item && item.itemSize && +item.itemSize.split(',')[0]) || 0,
              width: (item && item.itemSize && +item.itemSize.split(',')[1]) || 0,
              height: (item && item.itemSize && +item.itemSize.split(',')[2]) || 0,
              ...(getItemTypeId(item.itemType) !== ITEM_TYPE_ID.SERVICE &&
              item?.trackingCode?.trim()
                ? { trackingNumber: item?.trackingCode }
                : {}),
              sku: item.sku,
              upc: item.upc,
              quantity: +item?.itemQuantity || 1,
              todos: createTodosArray(
                item,
                taggedDropOffArr[index],
                verifiedJobType,
                index,
              ),
              ...isReplaceable(item),
            })),
          itemErrors: errors,
        }
  }

  if (isMultiJobSingleStop) {
    return errors.length > 0
      ? {
          items: [],
          itemErrors: errors,
        }
      : {
          items: dropOffArr
            .filter((dropOff) => isGenerateItems(dropOff))
            .map((item, index) => ({
              description:
                item.itemName || ITEM_TYPE_DESC[getItemTypeId(item.itemType)],
              weight: +(item.itemWeight ? item.itemWeight : '0'),
              rowLocation: item?.rowLocation.toString(),
              itemTypeId: getItemTypeId(item.itemType),
              length: +item?.itemSize?.split(',')[0] || 0,
              width: +item?.itemSize?.split(',')[1] || 0,
              height: +item?.itemSize?.split(',')[2] || 0,
              ...(getItemTypeId(item.itemType) !== ITEM_TYPE_ID.SERVICE &&
              item?.trackingCode?.trim()
                ? { trackingNumber: item?.trackingCode }
                : {}),
              sku: item.sku,
              upc: item.upc,
              quantity: +item?.itemQuantity || 1,
              todos: createTodosArray(
                item,
                taggedDropOffArr[index],
                verifiedJobType,
                index,
              ),
              ...isReplaceable(item),
            })),
          itemErrors: errors,
        }
  }

  return {
    items: [],
    itemErrors: errors,
  }
}
