import { useMemo, useState } from 'react'
import {
  Autocomplete,
  CircularProgress,
  createFilterOptions,
  IconButton,
  ListItemText,
  TextField,
  Tooltip,
  type TextFieldProps,
} from '@karoo-ui/core'
import DeleteIcon from '@mui/icons-material/DeleteOutlined'

import { useCreateDeliveryJobLabelsMutation } from 'src/modules/deliveryRevamp/api/job-labels/useCreateDeliveryJobLabelsMutation'
import { useDeleteDeliveryJobLabelsMutation } from 'src/modules/deliveryRevamp/api/job-labels/useDeleteDeliveryJobLabelsMutation'
import useDeliveryJobLabelsQuery from 'src/modules/deliveryRevamp/api/job-labels/useFetchDeliveryJobLabelsQuery'
import { ctIntl } from 'src/util-components/ctIntl'

const ADD_OPTION_VALUE = -1
const optionsFilter = createFilterOptions<number>()

type JobLabels = Array<number>

type Props = {
  value: JobLabels
  onChange: (value: JobLabels) => void
  disabled?: boolean
  textFieldProps?: TextFieldProps
}

const JobLabelsAutoComplete = ({
  value,
  onChange,
  disabled = false,
  textFieldProps = {},
}: Props) => {
  const [jobLabelToAdd, setJobLabelToAdd] = useState<string | null>(null)

  const { data: jobLabels, status: jobLabelsQueryStatus } = useDeliveryJobLabelsQuery()
  const { jobLabelsValues, jobLabelsById } = useMemo(() => {
    const jobLabelsValues: Array<number> = []

    // Job labels with userId should be at the top
    const sortedJobLabels = (jobLabels ?? []).sort((a, b) => {
      const userIdComparison = Number(a.userId) - Number(b.userId)
      if (userIdComparison !== 0) {
        return userIdComparison
      }
      return Number(a.id) - Number(b.id)
    })

    const jobLabelsById = sortedJobLabels.reduce<
      Record<string, (typeof sortedJobLabels)[number]>
    >((acc, option) => {
      jobLabelsValues.push(option.id)
      acc[option.id] = option
      return acc
    }, {})

    return {
      jobLabelsValues,
      jobLabelsById,
    }
  }, [jobLabels])

  const createJobLabelsMutation = useCreateDeliveryJobLabelsMutation()
  const deleteJobLabelsMutation = useDeleteDeliveryJobLabelsMutation()

  return (
    <Autocomplete
      fullWidth
      loading={
        jobLabelsQueryStatus === 'pending' ||
        createJobLabelsMutation.isPending ||
        deleteJobLabelsMutation.isPending
      }
      multiple
      limitTags={3}
      disableCloseOnSelect
      size="small"
      options={jobLabelsValues}
      value={value}
      onChange={(_event, selectedOptions, _reason, details) => {
        if (details?.option === ADD_OPTION_VALUE && jobLabelToAdd !== null) {
          createJobLabelsMutation.mutate(
            { labels: [jobLabelToAdd] },
            {
              onSuccess: (data) => {
                if (data.length > 0) {
                  const addedJobLabel = data[0]

                  onChange(
                    selectedOptions.map((option) => {
                      if (option === ADD_OPTION_VALUE) {
                        return addedJobLabel.id
                      }

                      return option
                    }),
                  )
                }
              },
            },
          )

          setJobLabelToAdd(null)
        } else {
          onChange(selectedOptions)
        }
      }}
      selectOnFocus
      clearOnBlur
      handleHomeEndKeys
      filterOptions={(options, params) => {
        const filtered = optionsFilter(options, params)

        const { inputValue } = params
        // Suggest the creation of a new value
        const isExisting = options.some(
          (option) => inputValue === params.getOptionLabel(option),
        )
        if (inputValue !== '' && !isExisting) {
          filtered.push(ADD_OPTION_VALUE)

          setJobLabelToAdd(inputValue)
        } else if (jobLabelToAdd !== null) {
          setJobLabelToAdd(null)
        }

        return filtered
      }}
      getOptionLabel={(option) => {
        const jobLabel = jobLabelsById[option]
        return jobLabel ? jobLabel.name : '...'
      }}
      renderOption={(props, item, state) => (
        <li
          {...props}
          key={item}
        >
          <ListItemText>
            {item === ADD_OPTION_VALUE
              ? ctIntl.formatMessage(
                  { id: 'global.autoComplete.addCustomItem' },
                  { values: { customItem: state.inputValue } },
                )
              : (jobLabelsById[item]?.name ?? '')}
          </ListItemText>
          {item !== ADD_OPTION_VALUE && !!jobLabelsById[item]?.userId && (
            <Tooltip title={ctIntl.formatMessage({ id: 'Delete' })}>
              <IconButton
                size="small"
                disableRipple
                onClick={(e) => {
                  e.stopPropagation()

                  deleteJobLabelsMutation.mutate(
                    { labelIds: [item] },
                    {
                      onSuccess: () => {
                        if (state.selected) {
                          onChange(value.filter((v) => v !== item))
                        }
                      },
                    },
                  )
                }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </li>
      )}
      disabled={disabled}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder={
            value.length === 0
              ? ctIntl.formatMessage({
                  id: 'Labels',
                })
              : ''
          }
          slotProps={{
            input: {
              ...params.InputProps,
              endAdornment: (
                <>
                  {createJobLabelsMutation.isPending ||
                  deleteJobLabelsMutation.isPending ? (
                    <CircularProgress
                      color="inherit"
                      size={20}
                    />
                  ) : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            },
          }}
          {...textFieldProps}
        />
      )}
    />
  )
}

export default JobLabelsAutoComplete
