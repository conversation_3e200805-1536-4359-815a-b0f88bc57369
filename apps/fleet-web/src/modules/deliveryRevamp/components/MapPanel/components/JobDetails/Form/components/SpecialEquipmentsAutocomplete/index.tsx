import { useMemo, useState } from 'react'
import {
  Autocomplete,
  CircularProgress,
  createFilterOptions,
  IconButton,
  ListItemText,
  TextField,
  Tooltip,
  type TextFieldProps,
} from '@karoo-ui/core'
import DeleteIcon from '@mui/icons-material/DeleteOutlined'

import { useCreateDeliverySpecialEquipmentsMutation } from 'src/modules/deliveryRevamp/api/special-equipments/useCreateDeliverySpecialEquipmentsMutation'
import { useDeleteDeliverySpecialEquipmentsMutation } from 'src/modules/deliveryRevamp/api/special-equipments/useDeleteDeliverySpecialEquipmentsMutation'
import { useSpecialEquipmentsOptions } from 'src/modules/deliveryRevamp/api/special-equipments/useFetchDeliverySpecialEquipmentsQuery'
import { ctIntl } from 'src/util-components/ctIntl'

const ADD_OPTION_VALUE = -1
const equipmentFilter = createFilterOptions<number>()

type SpecialEquipments = Array<number>

type Props = {
  value: SpecialEquipments
  onChange: (value: SpecialEquipments) => void
  disabled?: boolean
  textFieldProps?: TextFieldProps
}

const SpecialEquipmentsAutoComplete = ({
  value,
  onChange,
  disabled = false,
  textFieldProps = {},
}: Props) => {
  const [specialEquipmentToAdd, setSpecialEquipmentToAdd] = useState<string | null>(
    null,
  )

  const { data: specialEquipments, status: specialEquipmentsQueryStatus } =
    useSpecialEquipmentsOptions()

  const { specialEquipmentValues, specialEquipmentsById } = useMemo(() => {
    const specialEquipmentValues: Array<number> = []

    const specialEquipmentsById = specialEquipments
      ? specialEquipments.data.reduce<
          Record<string, (typeof specialEquipments.data)[number]>
        >((acc, option) => {
          specialEquipmentValues.push(option.value)
          acc[option.value] = option
          return acc
        }, {})
      : {}

    return {
      specialEquipmentValues,
      specialEquipmentsById,
    }
  }, [specialEquipments])

  const createSpecialEquipmentsMutation = useCreateDeliverySpecialEquipmentsMutation()
  const deleteSpecialEquipmentsMutation = useDeleteDeliverySpecialEquipmentsMutation()

  return (
    <Autocomplete
      fullWidth
      loading={
        specialEquipmentsQueryStatus === 'pending' ||
        createSpecialEquipmentsMutation.isPending ||
        deleteSpecialEquipmentsMutation.isPending
      }
      multiple
      limitTags={3}
      disableCloseOnSelect
      size="small"
      options={specialEquipmentValues}
      value={value}
      onChange={(_event, selectedOptions, _reason, details) => {
        if (details?.option === ADD_OPTION_VALUE && specialEquipmentToAdd !== null) {
          createSpecialEquipmentsMutation.mutate(
            { capabilityNames: [specialEquipmentToAdd] },
            {
              onSuccess: (data) => {
                if (data.length > 0) {
                  const addedEquipment = data[0]

                  onChange(
                    selectedOptions.map((option) => {
                      if (option === ADD_OPTION_VALUE) {
                        return addedEquipment.id
                      }

                      return option
                    }),
                  )
                }
              },
            },
          )

          setSpecialEquipmentToAdd(null)
        } else {
          onChange(selectedOptions)
        }
      }}
      selectOnFocus
      clearOnBlur
      handleHomeEndKeys
      filterOptions={(options, params) => {
        const filtered = equipmentFilter(options, params)

        const { inputValue } = params
        // Suggest the creation of a new value
        const isExisting = options.some(
          (option) => inputValue === params.getOptionLabel(option),
        )
        if (inputValue !== '' && !isExisting) {
          filtered.push(ADD_OPTION_VALUE)

          setSpecialEquipmentToAdd(inputValue)
        } else if (specialEquipmentToAdd !== null) {
          setSpecialEquipmentToAdd(null)
        }

        return filtered
      }}
      getOptionLabel={(option) => {
        const specialEquipment = specialEquipmentsById[option]
        return specialEquipment ? specialEquipment.label : '...'
      }}
      renderOption={(props, item, state) => (
        <li
          {...props}
          key={item}
        >
          <ListItemText>
            {item === ADD_OPTION_VALUE
              ? ctIntl.formatMessage(
                  { id: 'global.autoComplete.addCustomItem' },
                  { values: { customItem: state.inputValue } },
                )
              : (specialEquipmentsById[item]?.label ?? '')}
          </ListItemText>
          {item !== ADD_OPTION_VALUE && !!specialEquipmentsById[item]?.userId && (
            <Tooltip title={ctIntl.formatMessage({ id: 'Delete' })}>
              <IconButton
                size="small"
                disableRipple
                onClick={(e) => {
                  e.stopPropagation()

                  deleteSpecialEquipmentsMutation.mutate(
                    { capabilityIds: [item] },
                    {
                      onSuccess: () => {
                        if (state.selected) {
                          onChange(value.filter((v) => v !== item))
                        }
                      },
                    },
                  )
                }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </li>
      )}
      disabled={disabled}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder={
            value.length === 0
              ? ctIntl.formatMessage({
                  id: 'Special Equipment',
                })
              : ''
          }
          slotProps={{
            input: {
              ...params.InputProps,
              endAdornment: (
                <>
                  {createSpecialEquipmentsMutation.isPending ||
                  deleteSpecialEquipmentsMutation.isPending ? (
                    <CircularProgress
                      color="inherit"
                      size={20}
                    />
                  ) : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            },
          }}
          {...textFieldProps}
        />
      )}
    />
  )
}

export default SpecialEquipmentsAutoComplete
