import { useMemo, useState } from 'react'
import {
  Autocomplete,
  IconButton,
  InputAdornment,
  ListItemText,
  Stack,
  TextField,
} from '@karoo-ui/core'
import { TextFieldControlled, type UseControlledFormReturn } from '@karoo-ui/core-rhf'
import BuildCircleIcon from '@mui/icons-material/BuildCircleOutlined'
import CloseIcon from '@mui/icons-material/Close'
import DateRangeIcon from '@mui/icons-material/DateRange'
import LabelIcon from '@mui/icons-material/LabelOutlined'
import NumbersIcon from '@mui/icons-material/Numbers'
import SupervisedUserCircleIcon from '@mui/icons-material/SupervisedUserCircleOutlined'
import { Controller, useWatch } from 'react-hook-form'

import type { DriverId } from 'api/types'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import type { DriversList } from 'src/modules/deliveryRevamp/api/drivers/useDriversList'
import type { FetchDeliveryJobDetails } from 'src/modules/deliveryRevamp/api/jobs/useDeliveryJobDetails'
import SubUserAvatar from 'src/modules/deliveryRevamp/components/Avatar/SubUser'
import RolledOverInfo from 'src/modules/deliveryRevamp/components/JobCard/components/RolledOverInfo'
import { useDriversAndRoutesContext } from 'src/modules/deliveryRevamp/components/MapPanel/DriversAndRoutesProvider'
import { FORM_STATE } from 'src/modules/deliveryRevamp/constants/job'
import { useDeliveryMainPageContext } from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import { useDeliverySettingsContext } from 'src/modules/deliveryRevamp/contexts/DeliverySettingsContext'
import {
  createRouteId,
  parseRollOverData,
  type RolledOverData,
} from 'src/modules/deliveryRevamp/helpers'
import useCheckCurrentGlobalModal from 'src/modules/deliveryRevamp/hooks/useCheckCurrentGlobalModal'
import useGetSubUsers from 'src/modules/deliveryRevamp/hooks/useGetSubUsers'
import useSubUserDataAccessScope from 'src/modules/deliveryRevamp/hooks/useSubUserDataAccessScope'
import type { LabelValue } from 'src/modules/deliveryRevamp/types'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'

import ExtraFieldsButton from '../../../components/ExtraFieldsButton'
import JobFieldContainer from '../../../components/JobFieldContainer'
import JobSection from '../../../components/Section'
import DriverField from '../../components/DriverField'
import JobLabelsAutoComplete from '../../components/LabelsAutocomplete'
import OptimizationPriorityField from '../../components/OptimizationPriorityField'
import ScheduledDatePicker from '../../components/ScheduledDatePicker'
import SpecialEquipmentsAutoComplete from '../../components/SpecialEquipmentsAutocomplete'
import type { JobDetailsFormType } from '../../schema'
import { handleScheduledDateChange } from '../../utils'

type Props = {
  disabledDatePickWithMessage?: string
  form: UseControlledFormReturn<JobDetailsFormType>
  job?: FetchDeliveryJobDetails.Return[number]['formData']
}

const JobDetailsSection = ({ job, form, disabledDatePickWithMessage }: Props) => {
  const {
    data: { selectedDateRange },
  } = useDeliveryMainPageContext()

  const { deliverySettings } = useDeliverySettingsContext()

  const { doesSubUserHaveAccessToScope, canThisUserSeeSubuser } =
    useSubUserDataAccessScope()

  const isRecurringSetupOpen = useCheckCurrentGlobalModal('recurring')

  const { subUserOptions } = useGetSubUsers()
  const subUsersById = useMemo(
    () =>
      subUserOptions.reduce<Record<string, (typeof subUserOptions)[number]>>(
        (acc, option) => {
          acc[option.value as string] = option
          return acc
        },
        {},
      ),
    [subUserOptions],
  )

  const rolledOverData = useMemo(() => {
    if (!selectedDateRange?.start) return null

    const selectedDate = selectedDateRange.start
    const now = DeliveryDateTime.now()
    return {
      now,
      selectedDate,
      isPast: selectedDate.startOf('day') < now.startOf('day'),
      isToday: selectedDate.hasSame(now, 'day'),
      rolledOverDays: Number(deliverySettings.jobViewPreviousNumDays),
    } satisfies RolledOverData
  }, [deliverySettings.jobViewPreviousNumDays, selectedDateRange])

  const { driversList } = useDriversAndRoutesContext()

  const [shouldDisplayLabelsField, setShouldDisplayLabelsField] = useState(
    () => form.getValues('labels').length > 0,
  )
  const [shouldDisplaySpecialEquipmentsField, setShouldDisplaySpecialEquipmentsField] =
    useState(() => form.getValues('specialEquipments').length > 0)
  const [
    shouldDisplayOptimizationPriorityField,
    setShouldDisplayOptimizationPriorityField,
  ] = useState(() => form.getValues('optimizationPriority') !== null)

  const showOptimizationPriorityField = useMemo(
    () =>
      form.getValues('optimizationPriority') !== null ||
      shouldDisplayOptimizationPriorityField,
    [form, shouldDisplayOptimizationPriorityField],
  )

  const showLabelsField = useMemo(
    () => form.getValues('labels').length > 0 || shouldDisplayLabelsField,
    [form, shouldDisplayLabelsField],
  )

  const showSpecialEquipmentsField = useMemo(
    () =>
      form.getValues('specialEquipments').length > 0 ||
      shouldDisplaySpecialEquipmentsField,
    [form, shouldDisplaySpecialEquipmentsField],
  )

  const showSpecialEquipmentButton =
    !shouldDisplaySpecialEquipmentsField && !showSpecialEquipmentsField

  const showOptimizationPriorityButton =
    !shouldDisplayOptimizationPriorityField && !showOptimizationPriorityField

  const showLabelsButton = !shouldDisplayLabelsField && !showLabelsField

  const [formState, driverId, planId, scheduledDate, sendToDriverAt, allowedToStartAt] =
    useWatch({
      control: form.control,
      name: [
        'formState',
        'driverId',
        'planId',
        'scheduledDate',
        'sendToDriverAt',
        'allowedToStartAt',
      ],
    })

  const driverAssignedSubUserId = useMemo(() => {
    if (driversList && driverId) {
      return getDriverSubUserId(driversList, driverId as DriverId)
    }

    return null
  }, [driverId, driversList])

  return (
    <JobSection>
      <Stack>
        <JobSection.Title>
          {ctIntl.formatMessage({ id: 'Job Details' })}
        </JobSection.Title>
        {job && (job.driverId || job.planId) && (
          <RolledOverInfo
            rolledOver={parseRollOverData({
              scheduledDate,
              hasJobStarted: job.hasJobStarted,
              rolledOverData,
              routeId: createRouteId(driverId ?? undefined, planId ?? undefined),
              jobStatusId: job.statusId,
            })}
          />
        )}
      </Stack>
      <Stack gap={1}>
        {!disabledDatePickWithMessage ? (
          !isRecurringSetupOpen && (
            <JobFieldContainer>
              <JobFieldContainer.Icon>
                <DateRangeIcon />
              </JobFieldContainer.Icon>

              <Controller
                name="scheduledDate"
                control={form.control}
                render={({ field, fieldState }) => (
                  <ScheduledDatePicker
                    value={field.value}
                    onChange={(value) => {
                      field.onChange(value)

                      handleScheduledDateChange({
                        value,
                        form,
                        sendToDriverAt,
                        allowedToStartAt,
                      })
                    }}
                    textFieldProps={{
                      variant: 'outlined',
                      error: !!fieldState.error,
                      helperText: fieldState.error?.message,
                    }}
                  />
                )}
              />
            </JobFieldContainer>
          )
        ) : (
          <JobFieldContainer>
            <JobFieldContainer.Icon>
              <DateRangeIcon />
            </JobFieldContainer.Icon>
            <TextField
              fullWidth
              disabled
              placeholder={disabledDatePickWithMessage}
            />
          </JobFieldContainer>
        )}

        {!isRecurringSetupOpen && (
          <DriverField
            form={form}
            job={job}
          />
        )}

        {!isRecurringSetupOpen &&
          doesSubUserHaveAccessToScope() &&
          canThisUserSeeSubuser() && (
            <JobFieldContainer>
              <JobFieldContainer.Icon>
                <SupervisedUserCircleIcon />
              </JobFieldContainer.Icon>

              <Controller
                key={driverAssignedSubUserId}
                control={form.control}
                name="subuserId"
                render={({ field }) => (
                  <Autocomplete
                    fullWidth
                    disabled={Boolean(driverId)} // Any driver assigned to a job disables the sub-user field
                    value={
                      field.value
                        ? ((subUsersById[field.value] as LabelValue) ?? null)
                        : null
                    }
                    onChange={(_event, option) => {
                      field.onChange(option?.value ?? null)
                    }}
                    {...getAutocompleteVirtualizedProps({
                      options: subUserOptions as Array<LabelValue>,
                      renderRowSingleItemContent: ({ label }) => (
                        <Stack
                          direction="row"
                          alignItems="center"
                          gap={1}
                        >
                          <SubUserAvatar
                            variant="square"
                            username={label}
                          />
                          <ListItemText>{label}</ListItemText>
                        </Stack>
                      ),
                    })}
                    getOptionLabel={(option) => option.label ?? ''}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder={ctIntl.formatMessage({ id: 'Assign Sub-user' })}
                        slotProps={{
                          input: {
                            startAdornment:
                              field.value && subUsersById[field.value] ? (
                                <InputAdornment
                                  position="start"
                                  sx={{ m: 0, ml: 1 }}
                                >
                                  <SubUserAvatar
                                    variant="square"
                                    username={subUsersById[field.value].label ?? ''}
                                  />
                                </InputAdornment>
                              ) : null,
                          },
                        }}
                      />
                    )}
                  />
                )}
              />
            </JobFieldContainer>
          )}

        <JobFieldContainer>
          <JobFieldContainer.Icon>
            <NumbersIcon />
          </JobFieldContainer.Icon>

          <TextFieldControlled
            ControllerProps={{
              control: form.control,
              name: 'referenceNumber',
            }}
            placeholder={ctIntl.formatMessage({ id: 'Reference Number' })}
            fullWidth
          />
        </JobFieldContainer>

        {showOptimizationPriorityField && (
          <OptimizationPriorityField
            form={form}
            setShouldDisplayOptimizationPriorityField={
              setShouldDisplayOptimizationPriorityField
            }
          />
        )}

        {showLabelsField && (
          <JobFieldContainer>
            <JobFieldContainer.Icon>
              <LabelIcon />
            </JobFieldContainer.Icon>

            <Stack
              direction="row"
              alignItems="center"
              flex={1}
              gap={1}
            >
              <Controller
                control={form.control}
                name="labels"
                render={({ field, fieldState }) => (
                  <JobLabelsAutoComplete
                    value={field.value}
                    onChange={field.onChange}
                    textFieldProps={{
                      helperText: ctIntl.formatMessage({
                        id: fieldState.error?.message ?? '',
                      }),
                      error: !!fieldState.error,
                    }}
                  />
                )}
              />

              {formState !== FORM_STATE.STARTED && (
                <IconButton
                  onClick={() => {
                    setShouldDisplayLabelsField(false)
                    form.setValue('labels', [])
                  }}
                  disableRipple
                  size="small"
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              )}
            </Stack>
          </JobFieldContainer>
        )}

        {showSpecialEquipmentsField && (
          <JobFieldContainer>
            <JobFieldContainer.Icon>
              <BuildCircleIcon />
            </JobFieldContainer.Icon>

            <Stack
              direction="row"
              alignItems="center"
              flex={1}
              gap={1}
            >
              <Controller
                control={form.control}
                name="specialEquipments"
                render={({ field, fieldState }) => (
                  <SpecialEquipmentsAutoComplete
                    value={field.value}
                    onChange={field.onChange}
                    textFieldProps={{
                      helperText: ctIntl.formatMessage({
                        id: fieldState.error?.message ?? '',
                      }),
                      error: !!fieldState.error,
                    }}
                  />
                )}
              />
              {formState !== FORM_STATE.STARTED && (
                <IconButton
                  onClick={() => {
                    setShouldDisplaySpecialEquipmentsField(false)
                    form.setValue('specialEquipments', [])
                  }}
                  disableRipple
                  size="small"
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              )}
            </Stack>
          </JobFieldContainer>
        )}

        <Stack
          direction="row"
          flexWrap="wrap"
          columnGap={1}
        >
          {showLabelsButton && formState !== FORM_STATE.STARTED && (
            <ExtraFieldsButton onClick={() => setShouldDisplayLabelsField(true)}>
              {ctIntl.formatMessage({ id: 'Label' })}
            </ExtraFieldsButton>
          )}
          {showSpecialEquipmentButton && formState !== FORM_STATE.STARTED && (
            <ExtraFieldsButton
              onClick={() => setShouldDisplaySpecialEquipmentsField(true)}
            >
              {ctIntl.formatMessage({ id: 'Special Equipment' })}
            </ExtraFieldsButton>
          )}
          {showOptimizationPriorityButton && formState !== FORM_STATE.STARTED && (
            <ExtraFieldsButton
              onClick={() => setShouldDisplayOptimizationPriorityField(true)}
            >
              {ctIntl.formatMessage({ id: 'Optimization Priority' })}
            </ExtraFieldsButton>
          )}
        </Stack>
      </Stack>
    </JobSection>
  )
}

export default JobDetailsSection

export const getDriverSubUserId = (
  driversList: DriversList.Return,
  driverId: DriverId | null,
) => {
  if (driversList && driverId) {
    return driversList?.byId[driverId]?.subuserId
  }
  return null
}
