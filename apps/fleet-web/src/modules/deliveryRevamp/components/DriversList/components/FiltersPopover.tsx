import { memo, useEffect, useMemo, useRef, useState } from 'react'
import { isEqual } from 'lodash'
import {
  Autocomplete,
  Badge,
  Button,
  IconButton,
  ListItemText,
  Stack,
  TextField,
  TimePicker,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import FilterListIcon from '@mui/icons-material/FilterList'
import { Controller } from 'react-hook-form'

import { getDeliveryAppointmentsSetting } from 'duxs/user-sensitive-selectors'
import { useOnClickOutside } from 'src/hooks'
import type { DriversList } from 'src/modules/deliveryRevamp/api/drivers/useDriversList'
import Popover from 'src/modules/deliveryRevamp/components/Popover'
import { shiftDays } from 'src/modules/deliveryRevamp/utils'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import type useDriversFilters from '../useDriversFilters'
import { SHIFT_TIME_FORMAT } from '../useDriversFilters'
import StatusFilter from './StatusFilter'

type Props = {
  filtersProps: ReturnType<typeof useDriversFilters>
  drivers: DriversList.Return
}

function FiltersPopover({
  drivers,
  filtersProps: {
    appliedFiltersCount,
    form: { control, resetField, formState, reset },
    filters,
    deliveryCapabilities,
  },
}: Props) {
  const deliveryAppointmentsSetting = useTypedSelector(getDeliveryAppointmentsSetting)

  const selectedDeliveryCapabilities = useMemo(
    () => (value: Array<number>) =>
      deliveryCapabilities.filter((cap) => value.includes(cap.value)),
    [deliveryCapabilities],
  )

  const [shiftStartOpen, setShiftStartOpen] = useState(false)
  const [shiftEndOpen, setShiftEndOpen] = useState(false)
  const shiftStartRef = useRef<HTMLDivElement>(null)
  const shiftEndRef = useRef<HTMLDivElement>(null)
  useOnClickOutside(shiftStartRef, () => {
    setShiftStartOpen(false)
  })
  useOnClickOutside(shiftEndRef, () => {
    setShiftEndOpen(false)
  })

  const [anchorPosition, setAnchorPosition] = useState<
    | {
        top: number
        left: number
      }
    | undefined
  >()
  const buttonRef = useRef<HTMLButtonElement>(null)

  useEffect(() => {
    const id = requestAnimationFrame(() => {
      if (buttonRef.current) {
        const rect = buttonRef.current.getBoundingClientRect()
        setAnchorPosition({
          top: rect.top + rect.height,
          left: rect.left,
        })
      }
    })
    return () => cancelAnimationFrame(id)
  }, [])

  return (
    <Popover
      popoverProps={{
        anchorReference: 'anchorPosition',
        anchorPosition,
      }}
      content={
        <Stack
          sx={{ p: 2, width: '300px' }}
          gap={1}
        >
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
          >
            <Typography variant="subtitle2">
              {ctIntl.formatMessage({ id: 'Filters' })}
              {appliedFiltersCount > 0 && ` (${appliedFiltersCount})`}
            </Typography>
            <Button
              size="small"
              variant="text"
              disabled={appliedFiltersCount === 0}
              onClick={() => reset()}
            >
              {ctIntl.formatMessage({ id: 'Reset All' })}
            </Button>
          </Stack>
          <Controller
            name="driverStatus"
            control={control}
            render={({ field: { onChange, value, name } }) => (
              <Stack gap={0.5}>
                <Stack
                  direction="row"
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Typography sx={{ color: 'text.secondary' }}>
                    {ctIntl.formatMessage({ id: 'Driver Status' })}
                    {value.length > 0 && ` (${value.length})`}
                  </Typography>
                  <Button
                    size="small"
                    variant="text"
                    disabled={isEqual(value, formState.defaultValues?.[name])}
                    onClick={() => resetField(name)}
                  >
                    {ctIntl.formatMessage({ id: 'Reset' })}
                  </Button>
                </Stack>

                <StatusFilter
                  value={value}
                  onChange={onChange}
                  drivers={drivers}
                />
              </Stack>
            )}
          />
          <Controller
            name="capabilities"
            control={control}
            render={({ field: { onChange, value, name } }) => (
              <Stack gap={0.5}>
                <Stack
                  direction="row"
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Typography sx={{ color: 'text.secondary' }}>
                    {ctIntl.formatMessage({ id: 'Special Equipment' })}
                    {value.length > 0 && ` (${value.length})`}
                  </Typography>
                  <Button
                    size="small"
                    variant="text"
                    disabled={isEqual(value, formState.defaultValues?.[name])}
                    onClick={() => resetField(name)}
                  >
                    {ctIntl.formatMessage({ id: 'Reset' })}
                  </Button>
                </Stack>
                <Autocomplete
                  multiple
                  size="small"
                  value={selectedDeliveryCapabilities(value)}
                  options={deliveryCapabilities}
                  getOptionLabel={(option) => option.label}
                  onChange={(_, options) => onChange(options.map((opt) => opt.value))}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      slotProps={{
                        select: {
                          MenuProps: {
                            disableEnforceFocus: true,
                          },
                        },
                      }}
                      placeholder={
                        selectedDeliveryCapabilities(value).length === 0
                          ? ctIntl.formatMessage({
                              id: 'Special Equipment',
                            })
                          : undefined
                      }
                    />
                  )}
                  slotProps={{
                    paper: {
                      onClick: (e: React.MouseEvent<HTMLDivElement>) =>
                        e.stopPropagation(),
                      onMouseDown: (e: React.MouseEvent<HTMLDivElement>) =>
                        e.stopPropagation(),
                    },
                  }}
                />
              </Stack>
            )}
          />

          <Stack gap={0.5}>
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="space-between"
            >
              <Typography sx={{ color: 'text.secondary' }}>
                {ctIntl.formatMessage({
                  id: deliveryAppointmentsSetting
                    ? 'delivery.driverAvailability'
                    : 'delivery.driver.shiftHours',
                })}
              </Typography>
              <Button
                size="small"
                variant="text"
                disabled={
                  isEqual(
                    filters.shiftTimeStart,
                    formState.defaultValues?.shiftTimeStart,
                  ) &&
                  isEqual(
                    filters.shiftTimeEnd,
                    formState.defaultValues?.shiftTimeEnd,
                  ) &&
                  isEqual(filters.shiftDays, formState.defaultValues?.shiftDays)
                }
                onClick={() => {
                  resetField('shiftTimeStart')
                  resetField('shiftTimeEnd')
                  resetField('shiftDays')
                }}
              >
                {ctIntl.formatMessage({ id: 'Reset' })}
              </Button>
            </Stack>
            <Stack gap={1}>
              {deliveryAppointmentsSetting && (
                <Controller
                  control={control}
                  name="shiftDays"
                  render={({ field }) => (
                    <Autocomplete
                      fullWidth
                      multiple
                      size="small"
                      disableCloseOnSelect
                      options={shiftDays}
                      value={shiftDays.filter((day) =>
                        field.value?.includes(day.value),
                      )}
                      onChange={(_event, selectedOptions, _reason) => {
                        field.onChange(selectedOptions.map((opt) => opt.value))
                      }}
                      selectOnFocus
                      clearOnBlur
                      handleHomeEndKeys
                      renderOption={(props, item) => (
                        <li
                          {...props}
                          key={item.value}
                        >
                          <ListItemText>{item.label}</ListItemText>
                        </li>
                      )}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder={
                            field.value.length === 0
                              ? ctIntl.formatMessage({
                                  id: 'Shift Days',
                                })
                              : ''
                          }
                          slotProps={{
                            input: {
                              ...params.InputProps,
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
              )}
              <Stack
                direction="row"
                gap={1}
              >
                <Controller
                  name="shiftTimeStart"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <TimePicker
                      open={shiftStartOpen}
                      onOpen={() => setShiftStartOpen(true)}
                      onClose={() => setShiftStartOpen(false)}
                      label={ctIntl.formatMessage({ id: 'Start' })}
                      ampm={false}
                      value={
                        value
                          ? DeliveryDateTime.fromFormat(value, SHIFT_TIME_FORMAT)
                          : null
                      }
                      onChange={(time) =>
                        onChange(time ? time.toFormat(SHIFT_TIME_FORMAT) : null)
                      }
                      shouldDisableTime={(time) =>
                        !!filters.shiftTimeEnd &&
                        time >
                          DeliveryDateTime.fromFormat(
                            filters.shiftTimeEnd,
                            SHIFT_TIME_FORMAT,
                          )
                      }
                      slotProps={{
                        popper: {
                          disablePortal: true,
                          ref: shiftStartRef,
                        },
                      }}
                    />
                  )}
                />
                <Controller
                  name="shiftTimeEnd"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <TimePicker
                      open={shiftEndOpen}
                      onOpen={() => setShiftEndOpen(true)}
                      onClose={() => setShiftEndOpen(false)}
                      label={ctIntl.formatMessage({ id: 'End' })}
                      ampm={false}
                      value={
                        value
                          ? DeliveryDateTime.fromFormat(value, SHIFT_TIME_FORMAT)
                          : null
                      }
                      onChange={(time) =>
                        onChange(time ? time.toFormat(SHIFT_TIME_FORMAT) : null)
                      }
                      shouldDisableTime={(time) =>
                        !!filters.shiftTimeStart &&
                        time <
                          DeliveryDateTime.fromFormat(
                            filters.shiftTimeStart,
                            SHIFT_TIME_FORMAT,
                          )
                      }
                      slotProps={{
                        popper: {
                          disablePortal: true,
                          ref: shiftEndRef,
                        },
                      }}
                    />
                  )}
                />
              </Stack>
            </Stack>
          </Stack>
        </Stack>
      }
    >
      <IconButton
        size="small"
        ref={buttonRef}
      >
        <Badge
          color="primary"
          variant="dot"
          invisible={appliedFiltersCount === 0}
        >
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Filters' })}
            slotProps={{ popper: { disablePortal: true } }}
          >
            <FilterListIcon sx={{ fontSize: 'inherit' }} />
          </Tooltip>
        </Badge>
      </IconButton>
    </Popover>
  )
}

export default memo(FiltersPopover)
