import { useCallback, useEffect, useRef, useState, type MouseEvent } from 'react'
import { Box, Typography } from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { AutoSizer, List, ScrollSync, type ListRowRenderer } from 'react-virtualized'
import * as R from 'remeda'

import { useEffectEvent } from 'src/hooks/useEventHandler'
import useIntl from 'src/modules/components/connected/useIntl'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'

import type {
  DriverAvailabilitySchedulerState,
  ExistingSchedule,
  TimelineSelectionRange,
} from '../../helpers'
import { HOURS_ROW_HEIGHT, LEFT_PANEL_WIDTH, ROW_HEIGHT } from '../../utils'
import { EmptyScheduler, ResourceNameColumn, ResourceTimelineRow } from '../ResourceRow'

type SchedulerTimelineProps = {
  resources: Array<{ id: string; name: string }>
  existingSchedules: Map<string, Array<ExistingSchedule>> | Array<ExistingSchedule>
  internalState: DriverAvailabilitySchedulerState
  startOfDay: DateTime
  totalRangeMinutes: number
  numberOfDays: number
  disabled: boolean
  onMouseDown: (e: MouseEvent<HTMLDivElement>) => void
  onMouseMove: (e: MouseEvent<HTMLDivElement>) => void
  onMouseUp: () => void
  onTimelineSelectionClick: (
    e: React.MouseEvent<HTMLDivElement>,
    selection: TimelineSelectionRange,
  ) => void
  emptySchedulerMessageKey?: string
}

const CALENDAR_ONE_DAY_WIDTH = 1150
const SELECTED_TIMELINE_LEFT_PADDING = 330
const HOUR_LABEL_EMPTY_COLUMN_PADDING = 30

const SchedulerTimeline = ({
  resources,
  existingSchedules,
  internalState,
  startOfDay,
  totalRangeMinutes,
  numberOfDays,
  onMouseDown,
  onMouseMove,
  onMouseUp,
  onTimelineSelectionClick,
  emptySchedulerMessageKey,
}: SchedulerTimelineProps) => {
  const { hourFormat } = useIntl()
  const [horizontalScrollbarHeight, setHorizontalScrollbarHeight] = useState(0)
  const scrollContainerEl = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    if (scrollContainerEl.current) {
      const element = scrollContainerEl.current
      const scrollbarHeight = element.offsetHeight - element.clientHeight
      setHorizontalScrollbarHeight(scrollbarHeight)
    }
  }, [scrollContainerEl])

  const hasResources = !R.isEmpty(resources)

  const scrollToRelevantTime = useEffectEvent(() => {
    if (scrollContainerEl.current) {
      const timeToScrollTo = DeliveryDateTime.now()

      const isTimeInView =
        timeToScrollTo >= startOfDay &&
        timeToScrollTo <= startOfDay.plus({ days: numberOfDays })

      const minuteOfDay = isTimeInView
        ? timeToScrollTo.hour * 60 + timeToScrollTo.minute
        : 0

      const pixelsPerMinute =
        (CALENDAR_ONE_DAY_WIDTH * numberOfDays) / (1440 * numberOfDays)
      const scrollPosition = minuteOfDay * pixelsPerMinute

      scrollContainerEl.current.scrollLeft = Math.max(
        0,
        scrollPosition - SELECTED_TIMELINE_LEFT_PADDING,
      )
    }
  })

  useEffect(() => {
    scrollToRelevantTime()
  }, [scrollContainerEl])

  const resourceNameRenderer: ListRowRenderer = useCallback(
    ({ index, key, style }) => {
      const resource = resources[index]
      return (
        <div
          key={key}
          style={style}
        >
          <ResourceNameColumn resource={resource} />
        </div>
      )
    },
    [resources],
  )

  const timelineRowRenderer: ListRowRenderer = useCallback(
    ({ index, key, style }) => {
      const resource = resources[index]

      return (
        <div
          key={key}
          style={style}
        >
          <ResourceTimelineRow
            resourceId={resource.id}
            schedulesInRow={
              R.isArray(existingSchedules)
                ? existingSchedules
                : (existingSchedules.get(resource.id) ?? [])
            }
            internalState={internalState}
            startOfDay={startOfDay}
            totalRangeMinutes={totalRangeMinutes}
            onSelectionClick={onTimelineSelectionClick}
            numberOfDays={numberOfDays}
          />
        </div>
      )
    },
    [
      resources,
      existingSchedules,
      internalState,
      startOfDay,
      totalRangeMinutes,
      onTimelineSelectionClick,
      numberOfDays,
    ],
  )

  const emptyRowRenderer: ListRowRenderer = useCallback(
    ({ key, style }) => (
      <div
        key={key}
        style={style}
      />
    ),
    [],
  )

  const rowWidth = CALENDAR_ONE_DAY_WIDTH * numberOfDays
  const computedEndOfDay = startOfDay.plus({ days: numberOfDays })

  if (!hasResources && emptySchedulerMessageKey) {
    return (
      <EmptyScheduler
        message={ctIntl.formatMessage({
          id: emptySchedulerMessageKey,
        })}
        computedStartOfDay={startOfDay}
      />
    )
  }

  const formatDateTimeToHourVariant = (dateTime: DateTime) => {
    if (dateTime.hour % 12 === 0) {
      return dateTime.toLocaleString({ hour: 'numeric' })
    }

    return hourFormat === '12h' ? dateTime.toFormat('h') : dateTime.toFormat('H')
  }

  return (
    <Box
      sx={{
        width: '100%',
        userSelect: 'none',
        flex: 1,
        position: 'relative',
      }}
    >
      <ScrollSync>
        {({ onScroll, scrollTop }) => (
          <Box
            sx={{
              display: 'flex',
              height: '100%',
              position: 'relative',
            }}
          >
            <Box
              ref={scrollContainerEl}
              sx={{
                display: 'flex',
                width: '100%',
                height: '100%',
                overflowX: 'auto',
                overflowY: 'hidden',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  minWidth: rowWidth,
                  wdith: rowWidth,
                  height: '100%',
                  position: 'relative',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    height: `${HOURS_ROW_HEIGHT}px`,
                    flexShrink: 0,
                    alignItems: 'center',
                    position: 'sticky',
                    top: 0,
                    zIndex: 2,
                    backgroundColor: 'grey.100',
                  }}
                >
                  <Box
                    sx={{
                      width: `${LEFT_PANEL_WIDTH - HOUR_LABEL_EMPTY_COLUMN_PADDING}px `,
                      marginRight: `${HOUR_LABEL_EMPTY_COLUMN_PADDING}px`,
                      flexShrink: 0,
                      position: 'sticky',
                      left: 0,
                      zIndex: 3,
                      backgroundColor: 'grey.100',
                      height: '100%',
                    }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      left: LEFT_PANEL_WIDTH,
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        position: 'absolute',
                        top: -16,
                        left: 2,
                        ml: -1,
                        textTransform: 'uppercase',
                        whiteSpace: 'nowrap',
                        color: 'text.secondary',
                      }}
                    >
                      {startOfDay.toFormat('dd/MM')}
                    </Typography>
                  </Box>
                  {R.times(24 * numberOfDays, (i) => {
                    const hourLabel = startOfDay.plus({ hours: i })
                    const isMidnightOrNoon =
                      hourLabel.hour === 0 || hourLabel.hour === 12
                    const isNotFirstDay = i > 0

                    return (
                      <Box
                        key={i}
                        sx={(theme) => ({
                          flex: '1 1 0',
                          borderRight: `1px solid ${theme.palette.grey[300]}`,
                          position: 'relative',
                        })}
                      >
                        {isMidnightOrNoon && isNotFirstDay && (
                          <Typography
                            variant="caption"
                            sx={{
                              position: 'absolute',
                              top: -16,
                              left: 2,
                              ml: -1,
                              textTransform: 'uppercase',
                              whiteSpace: 'nowrap',
                              color: 'text.secondary',
                            }}
                          >
                            {hourLabel.toFormat('dd/MM')}
                          </Typography>
                        )}
                        <Typography
                          variant="caption"
                          sx={{
                            position: 'absolute',
                            top: 2,
                            left: 2,
                            ml: -1,
                            textTransform: 'uppercase',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {formatDateTimeToHourVariant(hourLabel)}
                        </Typography>
                      </Box>
                    )
                  })}
                  <Box
                    sx={{
                      position: 'absolute',
                      right: 0,
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        position: 'absolute',
                        top: -16,
                        left: 2,
                        ml: -1,
                        textTransform: 'uppercase',
                        whiteSpace: 'nowrap',
                        color: 'text.secondary',
                      }}
                    >
                      {computedEndOfDay.toFormat('dd/MM')}
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        position: 'absolute',
                        top: 2,
                        left: 2,
                        ml: -1,
                        textTransform: 'uppercase',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {formatDateTimeToHourVariant(computedEndOfDay)}
                    </Typography>
                  </Box>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    flex: 1,
                    position: 'relative',
                    height: `calc(100% - ${HOURS_ROW_HEIGHT}px)`,
                  }}
                >
                  <Box
                    sx={{
                      width: LEFT_PANEL_WIDTH,
                      flexShrink: 0,
                      position: 'sticky',
                      left: 0,
                      zIndex: 1,
                      height: '100%',
                    }}
                  >
                    <AutoSizer>
                      {({ width, height }) => (
                        <List
                          width={width}
                          height={height}
                          rowCount={resources.length}
                          rowHeight={ROW_HEIGHT}
                          rowRenderer={resourceNameRenderer}
                          scrollTop={scrollTop}
                          onScroll={onScroll}
                          style={{
                            overflowY: 'auto',
                            overflowX: 'hidden',
                            scrollbarWidth: 'none',
                            msOverflowStyle: 'none',
                          }}
                          overscanRowCount={3}
                        />
                      )}
                    </AutoSizer>
                  </Box>
                  <Box
                    sx={{
                      flex: 1,
                      position: 'relative',
                      height: '100%',
                    }}
                    onMouseDown={onMouseDown}
                    onMouseMove={onMouseMove}
                    onMouseUp={onMouseUp}
                  >
                    <AutoSizer>
                      {({ width, height }) => (
                        <List
                          width={width}
                          height={height}
                          rowCount={resources.length}
                          rowHeight={ROW_HEIGHT}
                          rowRenderer={timelineRowRenderer}
                          scrollTop={scrollTop}
                          onScroll={onScroll}
                          overscanRowCount={3}
                          style={{
                            overflowY: 'auto',
                            overflowX: 'hidden',
                            scrollbarWidth: 'none',
                            msOverflowStyle: 'none',
                          }}
                        />
                      )}
                    </AutoSizer>
                  </Box>
                </Box>
              </Box>
            </Box>
            <Box
              sx={(theme) => ({
                height: `calc(100% - ${HOURS_ROW_HEIGHT}px - ${horizontalScrollbarHeight}px)`,
                marginTop: `${HOURS_ROW_HEIGHT}px`,
                width: '20px',
                flexShrink: 0,
                position: 'sticky',
                left: 0,
                zIndex: 1,
                '& .ReactVirtualized__Grid__innerScrollContainer': {
                  backgroundColor: theme.palette.grey[100],
                },
              })}
            >
              <AutoSizer>
                {({ width, height }) => (
                  <List
                    width={width}
                    height={height}
                    rowCount={resources.length}
                    rowHeight={ROW_HEIGHT}
                    rowRenderer={emptyRowRenderer}
                    scrollTop={scrollTop}
                    onScroll={onScroll}
                    style={{ overflowY: 'auto', overflowX: 'hidden' }}
                    overscanRowCount={3}
                  />
                )}
              </AutoSizer>
            </Box>
          </Box>
        )}
      </ScrollSync>
    </Box>
  )
}

export default SchedulerTimeline
