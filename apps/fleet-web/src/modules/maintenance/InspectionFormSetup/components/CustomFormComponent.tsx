import { useMemo } from 'react'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Divider,
  IconButton,
  MenuItem,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import { TextFieldControlled, useControlledForm } from '@karoo-ui/core-rhf'
import CloseIcon from '@mui/icons-material/Close'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { Controller, useWatch } from 'react-hook-form'

import {
  globalGroupIdPrefix,
  globalVehicleTypePrefix,
  type VehicleGroupIdWithGPrefix,
  type VehicleTypeGlobalId,
} from 'api/types'
import {
  getVehicleGroupOptions,
  getVehiclesOptions,
  getVehicleTypeOptions,
} from 'duxs/vehicles'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
import { useEventHandler } from 'src/hooks/useEventHandler'
import { VEHICLE_ALL_VALUE } from 'src/modules/components/unconnected/MultipleSelect/shared'
import { VehicleAndGroupAndTypeMultipleWithAllSelect } from 'src/modules/components/unconnected/MultipleSelect/VehicleAndGroupAndTypeMultipleWithAllSelect'
import type { VehicleOrGroupOrTypeOption } from 'src/modules/components/unconnected/MultipleSelect/VehicleAndGroupAndTypeMultipleWithAllSelect/schema'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import FormSelect from 'src/util-components/react-hook-form/FormSelect'
import ScheduleTimePicker from 'src/util-components/schedule-time-picker'

import type { CustomFormResolvedType } from '../api/queries'
import { useCustomFormContext } from './CustomFormContext'
import FormQuestionComponent, { createQuestion } from './FormQuestionComponent'
import {
  CUSTOM_FORM_COMPLETED_TIME_OPTIONS,
  customFormDetailFormSchema,
  type CustomFormDetailForm,
  type CustomFormDetailPossibleValues,
} from './schema'

const formDefaultValues: CustomFormDetailPossibleValues = {
  name: '',
  description: '',
  questions: [createQuestion('radio')],
  formPlacement: '',
  vehiclesConfig: [],
  completedTime: null,
}

type Props = {
  data: CustomFormResolvedType | null
  isEditing: boolean
  onFormSubmit: (
    value: CustomFormDetailForm &
      Partial<Pick<CustomFormResolvedType, 'extraFormData'>>,
  ) => void
  isSubmitting: boolean
}

export default function CustomFormComponentContent({
  data,
  onFormSubmit,
  isSubmitting,
  isEditing,
}: Props) {
  const vehicleOptions = useTypedSelector(getVehiclesOptions)
  const vehicleGroupOptions = useTypedSelector(getVehicleGroupOptions)
  const vehicleTypeOptions = useTypedSelector(getVehicleTypeOptions)
  const { onDirtyStateChange, customFormPrerequisites, onCancelButtonClick } =
    useCustomFormContext()

  const initialFormValues = useMemo((): CustomFormDetailPossibleValues => {
    if (data === null) {
      return formDefaultValues
    }

    return {
      ...data,
      questions: data.questions,
      vehiclesConfig: ((): CustomFormDetailPossibleValues['vehiclesConfig'] => {
        if (data.vehiclesConfig.value === 'all') {
          return {
            ...VEHICLE_ALL_VALUE,
            label: ctIntl.formatMessage({ id: VEHICLE_ALL_VALUE.label }),
          }
        }

        return [
          ...data.vehiclesConfig.value.vehicleIds.map((id) => ({
            label: vehicleOptions.find((v) => v.value === id)?.name || '',
            type: 'vehicle' as const,
            value: id,
          })),
          ...data.vehiclesConfig.value.groupIds.map((id) => ({
            value: `${globalGroupIdPrefix}${id}` satisfies VehicleGroupIdWithGPrefix,
            type: 'vehicleGroup' as const,
            label: vehicleGroupOptions.find((g) => g.value === id)?.name || '',
          })),
          ...data.vehiclesConfig.value.vehicleTypeIds.map((id) => ({
            value: `${globalVehicleTypePrefix}${id}` satisfies VehicleTypeGlobalId,
            type: 'vehicleType' as const,
            label: vehicleTypeOptions.find((t) => t.value === id)?.name || '',
          })),
        ] satisfies ReadonlyArray<VehicleOrGroupOrTypeOption>
      })(),
      formPlacement:
        'placementTypes' in customFormPrerequisites
          ? (customFormPrerequisites.placementTypes.find(
              (type) => data.formPlacement === type.id,
            )?.name ?? '')
          : '',
      completedTime: data.completedTime ?? null,
    }
  }, [
    customFormPrerequisites,
    data,
    vehicleGroupOptions,
    vehicleOptions,
    vehicleTypeOptions,
  ])

  const form = useControlledForm<CustomFormDetailPossibleValues>({
    resolver: zodResolverV4(customFormDetailFormSchema()),
    mode: 'all',
    defaultValues: initialFormValues,
  })

  const {
    control,
    setValue: setFormValue,
    handleSubmit,
    formState: { isDirty },
  } = form

  const watchedCompleteTimeType = useWatch({ name: 'completedTime.type', control })

  const allVehiclesConfigOptions = useMemo(
    () => [
      ...vehicleOptions.map(
        (o): VehicleOrGroupOrTypeOption =>
          o.value === 'all'
            ? {
                label: o.name,
                value: 'all',
                type: 'all',
              }
            : {
                label: o.name,
                value: o.value,
                type: 'vehicle',
              },
      ),
      ...vehicleGroupOptions.map(
        (o): VehicleOrGroupOrTypeOption => ({
          label: o.name,
          value: `${globalGroupIdPrefix}${o.value}`,
          type: 'vehicleGroup',
        }),
      ),
      ...vehicleTypeOptions.map(
        (o): VehicleOrGroupOrTypeOption => ({
          label: o.name,
          value: `vehicleType-${o.value}`,
          type: 'vehicleType',
        }),
      ),
    ],
    [vehicleOptions, vehicleGroupOptions, vehicleTypeOptions],
  )

  const onSubmit = handleSubmit((_values) => {
    const values = _values as CustomFormDetailForm
    onFormSubmit({
      ...values,
      formPlacement:
        'placementTypes' in customFormPrerequisites
          ? (customFormPrerequisites.placementTypes.find(
              (type) => values.formPlacement === type.name,
            )?.id ?? '')
          : '',
      extraFormData: data?.extraFormData,
    })
  })

  return (
    <>
      <OnFieldsDirty
        isDirty={isDirty}
        onUpdate={onDirtyStateChange}
      />
      <Box
        sx={{
          py: 3,
          gap: 1,
          height: '100%',
          overflowY: 'auto',
          display: 'grid',
          gridTemplateRows:
            'minmax(32px, min-content) minmax(45px, min-content) 1fr 48px',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            px: 3,
          }}
        >
          <Typography
            variant="h6"
            data-testid="customForm-title"
          >
            {ctIntl.formatMessage({
              id: isEditing ? 'Edit Custom Form' : 'New Custom Form',
            })}
          </Typography>
          <IconButton
            onClick={onCancelButtonClick}
            data-testid="customForm-close"
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <Stack
          sx={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            alignItems: 'start',
            gap: 2,
            px: 3,
          }}
        >
          <TextFieldControlled
            ControllerProps={{ control, name: 'name' }}
            data-testid="customForm-name"
            variant="standard"
            label={`${ctIntl.formatMessage({ id: 'Form Name' })} *`}
          />
          <TextFieldControlled
            ControllerProps={{ control, name: 'description' }}
            data-testid="customForm-description"
            variant="standard"
            label={`${ctIntl.formatMessage({
              id: 'Description',
            })} (${ctIntl.formatMessage({ id: 'optional' })})`}
          />
        </Stack>
        <Stack
          sx={{
            height: '100%',
            overflowY: 'auto',
            display: 'grid',
            gridTemplateRows: '1fr minmax(48px, min-content)',
          }}
        >
          <Accordion
            sx={{ boxShadow: 'none', px: 2, overflowY: 'auto' }}
            defaultExpanded
          >
            <AccordionSummary
              data-testid="customForm-questions"
              expandIcon={
                <IconButton>
                  <ExpandMoreIcon />
                </IconButton>
              }
              sx={{
                '&.Mui-expanded': {
                  minHeight: '48px',
                },
                '.MuiAccordionSummary-content.Mui-expanded': {
                  margin: '12px 0',
                },
              }}
            >
              <Typography
                variant="subtitle2"
                data-testid="customForm-questions-title"
              >
                1. {ctIntl.formatMessage({ id: 'Questions' })}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormQuestionComponent form={form} />
            </AccordionDetails>
          </Accordion>
          <Accordion sx={{ boxShadow: 'none', px: 3 }}>
            <AccordionSummary
              data-testid="customForm-config"
              expandIcon={
                <IconButton>
                  <ExpandMoreIcon />
                </IconButton>
              }
              sx={{
                '&.Mui-expanded': {
                  minHeight: '48px',
                },
                '.MuiAccordionSummary-content.Mui-expanded': {
                  margin: '12px 0',
                },
              }}
            >
              <Typography
                variant="subtitle2"
                data-testid="customForm-config-title"
              >
                2. {ctIntl.formatMessage({ id: 'Configuration' })}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Stack
                sx={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  alignItems: 'center',
                  rowGap: 3,
                }}
              >
                <Stack
                  direction="row"
                  gap={1}
                  alignItems="center"
                >
                  <IntlTypography
                    msgProps={{
                      id: 'Which vehicles are eligible to submit this form?',
                    }}
                  />
                  <Tooltip
                    placement="top"
                    title={ctIntl.formatMessage({
                      id: 'customForms.config.vehicles.tooltip',
                    })}
                  >
                    <InfoOutlinedIcon color="action" />
                  </Tooltip>
                </Stack>
                <Controller
                  control={control}
                  name="vehiclesConfig"
                  render={({ field, fieldState }) => (
                    <VehicleAndGroupAndTypeMultipleWithAllSelect
                      dataTestId="customForms-Vehicles"
                      promptName={ctIntl.formatMessage({ id: 'Select Vehicles' })}
                      options={allVehiclesConfigOptions}
                      selectedValues={field.value}
                      onChange={(newValue) => {
                        if (newValue) {
                          setFormValue(field.name, newValue)
                        } else {
                          setFormValue(field.name, [])
                        }
                      }}
                      error={
                        fieldState.error ? { msg: fieldState.error?.message } : null
                      }
                    />
                  )}
                />
                <IntlTypography msgProps={{ id: 'Select form placement and usage' }} />
                <Controller
                  control={control}
                  name="formPlacement"
                  render={({ field, fieldState }) => {
                    const errorMsg = fieldState.error?.message
                    return (
                      <TextField
                        select
                        data-testid="customForm-config-placement"
                        value={field.value ?? null} // null is needed so that the component is always controlled. Undefined could make it an uncontrolled component.
                        label={ctIntl.formatMessage({ id: 'Select Placement' })}
                        onChange={(e) => {
                          field.onChange(e.target.value)
                        }}
                        error={!!errorMsg}
                        helperText={
                          errorMsg ? ctIntl.formatMessage({ id: errorMsg }) : undefined
                        }
                      >
                        {[
                          {
                            label: 'Maintenance -> Vehicle Inspection',
                            value: 'maintenance-vehicle-inspection',
                            info: 'customForms.config.placement.vehicleInspection.tooltip',
                          },
                        ].map((entry, index) => (
                          <MenuItem
                            data-testid={`customForm-config-placement-${index}`}
                            value={entry.value}
                            key={entry.value}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                              }}
                              gap={1}
                            >
                              {entry.label}
                              <Tooltip
                                placement="left"
                                title={ctIntl.formatMessage({ id: entry.info })}
                              >
                                <InfoOutlinedIcon color="action" />
                              </Tooltip>
                            </Box>
                          </MenuItem>
                        ))}
                      </TextField>
                    )
                  }}
                />
                <Stack
                  direction="row"
                  gap={1}
                  alignItems="center"
                  alignSelf="flex-start"
                >
                  <IntlTypography
                    msgProps={{ id: 'customForms.config.completeTime.title' }}
                  />
                  <Tooltip
                    placement="top"
                    title={ctIntl.formatMessage({
                      id: 'customForms.config.completeTime.tooltip',
                    })}
                  >
                    <InfoOutlinedIcon color="action" />
                  </Tooltip>
                </Stack>
                <Stack
                  gap={2}
                  alignSelf="flex-start"
                >
                  <FormSelect
                    formProps={{ name: 'completedTime.type', control }}
                    label=""
                    labelId="customForm-completed-time-type"
                    dataTestId="customForm-completed-time-type"
                    list={CUSTOM_FORM_COMPLETED_TIME_OPTIONS}
                  />
                  {watchedCompleteTimeType === 'specific_time' && (
                    <Controller
                      control={control}
                      name="completedTime.value"
                      render={({ field, fieldState }) => (
                        <ScheduleTimePicker
                          dataTestId="customForm-completed-time-value"
                          {...field}
                          label={ctIntl.formatMessage({ id: 'Time' })}
                          slotProps={{
                            textField: {
                              error: !!fieldState.error,
                              helperText: fieldState.error?.message,
                            },
                          }}
                        />
                      )}
                    />
                  )}
                </Stack>
              </Stack>
            </AccordionDetails>
          </Accordion>
        </Stack>
        <Stack>
          <Divider />
          <Stack
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingTop: 2,
              px: 3,
            }}
          >
            <Button
              data-testid="customForm-cancel"
              onClick={onCancelButtonClick}
              variant="outlined"
              color="secondary"
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>
            <Button
              data-testid="customForm-save"
              variant="contained"
              onClick={onSubmit}
              loading={isSubmitting}
            >
              {ctIntl.formatMessage({ id: isEditing ? 'Save Changes' : 'Create Form' })}
            </Button>
          </Stack>
        </Stack>
      </Box>
    </>
  )
}

// The reason why this is a component instead of a hook is to avoid re-rendering the entire form when the fields are updated.
// This way, only this component will re-render when the fields are updated.
function OnFieldsDirty({
  isDirty,
  onUpdate: onUpdateProp,
}: {
  isDirty: boolean
  onUpdate: (isDirty: boolean) => void
}) {
  const onUpdate = useEventHandler(onUpdateProp)

  useEffectExceptOnMount(() => {
    onUpdate(isDirty)
  }, [isDirty, onUpdate])

  return null
}
