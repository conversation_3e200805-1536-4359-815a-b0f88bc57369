import * as R from 'remeda'
import type { Except } from 'type-fest'
import { z } from 'zod/v4'

import { createVehicleGroupTypeMultipleSchema } from 'src/modules/components/unconnected/MultipleSelect/VehicleAndGroupAndTypeMultipleWithAllSelect/schema'
import { messages } from 'src/shared/forms/messages'
import type { FormListItemType } from 'src/util-components/react-hook-form/FormSelect'

import { customFormRadioElementOptionValueSchema } from '../api/types'

export const CUSTOM_FORM_QUESTION_LIMIT = 50
export const CUSTOM_FORM_QUESTION_OPTIONS_LIMIT = 10
export const CUSTOM_FORM_TEXT_FIELD_LIMIT = 512

export const customFormDetailsSearchParamsSchema = z.object({
  modal: z.literal('customFormDetails'),
  params: z.discriminatedUnion('form', [
    z.object({
      form: z.literal('add'),
    }),
    z.object({
      form: z.literal('edit'),
      id: z.string(),
    }),
    z.object({
      form: z.literal('duplicate'),
      id: z.string(),
    }),
  ]),
})

const customFormTriggerTimeValueSchema = () =>
  z.object({ hour: z.number(), minute: z.number() }, { message: messages.required })

type CustomFormTriggerTimeValue = z.infer<
  ReturnType<typeof customFormTriggerTimeValueSchema>
>

const questionBaseSchema = z.object({
  id: z.string(),
  question: z.string().min(1, messages.required),
  defaultValue: z.string().nullable(),
  required: z.boolean(),
  image: z.string().nullable(),
  hasCommentSection: z.boolean(),
  subtitle: z.string().nullable(),
})

const questionTypeSpecificSchema = z.discriminatedUnion('type', [
  //radio
  z
    .object({
      type: z.literal('radio'),
      options: z
        .array(
          z.object({
            value: customFormRadioElementOptionValueSchema,
            label: z.string().min(1, { message: messages.required }).max(512),
          }),
        )
        .max(CUSTOM_FORM_QUESTION_OPTIONS_LIMIT)
        .refine(
          (data) => new Set(data.map((option) => option.label)).size === data.length,
          { message: 'There are duplicated options, please double check it.' },
        )
        .refine((options) => options.length > 1, {
          error: 'customForms.error.Must have at least 2 options',
        }),
    })
    .merge(questionBaseSchema),
  // upload
  z.object({ type: z.literal('upload') }).merge(questionBaseSchema),
  // signature
  z.object({ type: z.literal('signature') }).merge(questionBaseSchema),
])

export const customFormDetailFormSchema = () =>
  z.object({
    name: z.string().min(1, messages.required).max(CUSTOM_FORM_TEXT_FIELD_LIMIT),
    description: z.string().max(CUSTOM_FORM_TEXT_FIELD_LIMIT).nullable(),
    questions: z
      .array(questionTypeSpecificSchema)
      .min(1, messages.required)
      .max(CUSTOM_FORM_QUESTION_LIMIT)
      .refine(
        (data) => {
          const uniqueItems = new Set()
          let hasDuplicated = false

          for (const item of data) {
            // Convert object to a string to use as a unique identifier
            if (item.type !== 'radio') {
              continue
            }
            const identifier = JSON.stringify({
              question: item.question,
              options: item.options,
            })
            // If the identifier is already in the set, we have a duplicate
            if (uniqueItems.has(identifier)) {
              hasDuplicated = true
            }

            // Otherwise, add the identifier to the set
            uniqueItems.add(identifier)
          }

          return !hasDuplicated
        },
        { message: 'There is duplicated questions, please double check it.' },
      ),
    formPlacement: z.string().min(1, messages.required),
    vehiclesConfig: createVehicleGroupTypeMultipleSchema().refine(
      (val) => (R.isArray(val) ? val.length > 0 : val.type === 'all'),
      { message: messages.required },
    ),
    completedTime: z.discriminatedUnion(
      'type',
      [
        z.object({ type: z.literal('any_time') }),
        z.object({ type: z.literal('during_linking') }),
        z.object({
          type: z.literal('specific_time'),
          value: customFormTriggerTimeValueSchema(),
        }),
      ],
      {
        error: (issue) => ({
          message:
            issue.code === 'invalid_union' ? messages.required : (issue.message ?? ''),
        }),
      },
    ),
  })

export type CustomFormDetailForm = z.infer<
  ReturnType<typeof customFormDetailFormSchema>
>

export type CustomFormDetailFormCompletedTime = CustomFormDetailForm['completedTime']

export type CustomFormDetailPossibleValues = Except<
  CustomFormDetailForm,
  'name' | 'formPlacement' | 'completedTime'
> & {
  name: CustomFormDetailForm['name'] | null
  formPlacement: CustomFormDetailForm['formPlacement'] | ''
  completedTime:
    | { type: 'any_time' | 'during_linking' }
    | {
        type: 'specific_time'
        value: CustomFormTriggerTimeValue | null
      }
    | null
}

export const CUSTOM_FORM_COMPLETED_TIME_OPTIONS = [
  {
    value: 'any_time',
    label: 'Anytime/Optional',
  },
  {
    value: 'specific_time',
    label: 'Specific Time',
  },
  {
    value: 'during_linking',
    label: 'During Linking',
  },
] as const satisfies ReadonlyArray<FormListItemType>
