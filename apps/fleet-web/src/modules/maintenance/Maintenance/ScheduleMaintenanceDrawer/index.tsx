import { useContext, useMemo } from 'react'
import { isEmpty, isNil } from 'lodash'
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  DateTimePicker,
  Divider,
  FormControlLabel,
  FormGroup,
  Stack,
  TextField,
  Typography,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import { DateTime } from 'luxon'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { z, ZodIssueCode } from 'zod/v4'

import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import DrawerBase from 'src/modules/carpool/components/drawer-base'
import { ctIntl } from 'src/util-components/ctIntl'

import { MAINTENANCE_STATUS_ID, MaintenanceOptionsContext } from '..'
import { showRequiredMessageOnAllErrorTypes } from '../../../carpool/utils/helpers'

const schema = z
  .object({
    vehicleRegistration: z.string({
      error: showRequiredMessageOnAllErrorTypes,
    }),
    type: z.string({
      error: showRequiredMessageOnAllErrorTypes,
    }),
    maintenancePartName: z.string({
      error: showRequiredMessageOnAllErrorTypes,
    }),
    reason: z.string().nullable(),
    operationId: z.string({
      error: showRequiredMessageOnAllErrorTypes,
    }),
    maintenanceNotes: z.string().nullable(),
    startTime: z
      .date()
      .nullable()
      .refine(
        (date) => !date || date > new Date(Date.now()),
        'The time must be after current time',
      ),
    endTime: z
      .date()
      .nullable()
      .refine(
        (date) => !date || date > new Date(Date.now()),
        'The time must be after current time',
      ),
    hasStartImmediately: z.boolean(),
    isEndTimeUnknown: z.boolean(),
  })
  .superRefine((data, ctx) => {
    if (!!data.startTime && !!data.endTime && data.startTime > data.endTime) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        path: ['dropoffTime'],
        message: 'End time must be after start time',
      })
    }
  })

export type ScheduleMaintenanceFormValidSchema = z.infer<typeof schema>

export type ScheduleMaintenanceFormPossibleValues = {
  vehicleRegistration: ScheduleMaintenanceFormValidSchema['vehicleRegistration'] | null
  type: ScheduleMaintenanceFormValidSchema['type'] | null
  maintenancePartName: ScheduleMaintenanceFormValidSchema['maintenancePartName'] | null
  maintenanceNotes: ScheduleMaintenanceFormValidSchema['maintenanceNotes'] | null
  reason: ScheduleMaintenanceFormValidSchema['reason'] | null
  startTime: ScheduleMaintenanceFormValidSchema['startTime'] | null
  endTime: ScheduleMaintenanceFormValidSchema['endTime'] | null
  hasStartImmediately: ScheduleMaintenanceFormValidSchema['hasStartImmediately']
  isEndTimeUnknown: ScheduleMaintenanceFormValidSchema['isEndTimeUnknown']
  operationId: ScheduleMaintenanceFormValidSchema['operationId'] | null
}

type Props = {
  onClose: () => void
  onConfirm: ({
    isEdit,
    formValues,
  }: {
    isEdit: boolean
    formValues: ScheduleMaintenanceFormPossibleValues
  }) => void
  initialValues?: ScheduleMaintenanceFormPossibleValues | undefined
  maintenanceId?: string | undefined
  maintenanceStatusId?: string | undefined
  operationId?: string | undefined
  selectedVehicle?: string | null
  isLoadingWhenSubmit: boolean
}

type AutocompleteOption = { id: string; label: string }

function ScheduleMaintenanceDrawer({
  onClose,
  initialValues: initialValuesForEdit,
  maintenanceId,
  maintenanceStatusId,
  selectedVehicle,
  onConfirm,
  isLoadingWhenSubmit,
}: Props) {
  const isEdit = !isNil(initialValuesForEdit) && !!maintenanceId

  const { maintenanceOptions } = useContext(MaintenanceOptionsContext)

  const initialValues: ScheduleMaintenanceFormPossibleValues = isEdit
    ? initialValuesForEdit
    : {
        vehicleRegistration: null,
        type: null,
        maintenancePartName: null,
        reason: null,
        maintenanceNotes: null,
        startTime: null,
        endTime: null,
        hasStartImmediately: false,
        isEndTimeUnknown: false,
        operationId: null,
      }

  const {
    control,
    handleSubmit,
    // formState,
    setValue: setFormValue,
  } = useForm<ScheduleMaintenanceFormPossibleValues>({
    resolver: zodResolverV4(schema),
    mode: 'all',
    defaultValues: initialValues,
  })

  const hasStartImmediately = useWatch({ name: 'hasStartImmediately', control })
  const isEndTimeUnknown = useWatch({ name: 'isEndTimeUnknown', control })
  const maintenanceStartTime = useWatch({ name: 'startTime', control })

  const maintenanceTypeOptions = useMemo(() => {
    const array: Array<AutocompleteOption> = []
    const byId = new Map<string, AutocompleteOption>()

    if (!isEmpty(maintenanceOptions)) {
      for (const i of maintenanceOptions.maintenanceTypes) {
        const option = {
          id: i.id,
          label: ctIntl.formatMessage({ id: i.typeDescription }),
        }
        array.push(option)
        byId.set(option.id, option)
      }
    }
    return {
      array,
      byId,
    }
  }, [maintenanceOptions])

  const maintenanceVehicleOptions = useMemo(() => {
    const array: Array<AutocompleteOption> = []
    const byId = new Map<string, AutocompleteOption>()

    if (!isEmpty(maintenanceOptions)) {
      for (const i of maintenanceOptions.vehicles) {
        const option = { id: i.id, label: i.registration }
        array.push(option)
        byId.set(option.id, option)
      }
    }
    return {
      array,
      byId,
    }
  }, [maintenanceOptions])

  const maintenanceReasonOptions = useMemo(() => {
    const array: Array<AutocompleteOption> = []
    const byId = new Map<string, AutocompleteOption>()

    if (!isEmpty(maintenanceOptions)) {
      for (const i of maintenanceOptions.reasons) {
        const option = { id: i.id, label: i.reason }
        array.push(option)
        byId.set(option.id, option)
      }
    }
    return {
      array,
      byId,
    }
  }, [maintenanceOptions])

  const maintenanceOperationStatesOptions = useMemo(() => {
    const array: Array<AutocompleteOption> = []
    const byId = new Map<string, AutocompleteOption>()

    if (!isEmpty(maintenanceOptions)) {
      for (const i of maintenanceOptions.operationStates) {
        const option = { id: i.id, label: i.description }
        array.push(option)
        byId.set(option.id, option)
      }
    }
    return {
      array,
      byId,
    }
  }, [maintenanceOptions])

  const submitForm = handleSubmit((_values) => {
    const values = _values as ScheduleMaintenanceFormValidSchema

    onConfirm({
      formValues: values,
      isEdit,
    })
  })

  const isEditOnGoingMaintenance =
    isEdit && maintenanceStatusId === MAINTENANCE_STATUS_ID.IN_MAINTENANCE

  return (
    <DrawerBase
      open
      onClose={onClose}
      header={
        isEdit
          ? 'list.maintenance.scheduleMaintenance.editHeader'
          : 'list.maintenance.scheduleMaintenance.header'
      }
    >
      <Box>
        <Typography
          mb={2}
          mt={5}
        >
          {ctIntl.formatMessage({
            id: 'list.maintenance.scheduleMaintenance.maintenanceDetails',
          })}
        </Typography>
        <form onSubmit={submitForm}>
          <Stack
            gap={3}
            mb={3}
          >
            {isEdit ? (
              <Typography variant="h6">{selectedVehicle}</Typography>
            ) : (
              <Controller
                control={control}
                name="vehicleRegistration"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    size="small"
                    {...getAutocompleteVirtualizedProps({
                      options: maintenanceVehicleOptions.array,
                    })}
                    onChange={(_, newValue) => {
                      setFormValue(field.name, newValue ? newValue.id : null, {
                        shouldValidate: true,
                      })
                    }}
                    value={
                      field.value
                        ? (maintenanceVehicleOptions.byId.get(field.value) ?? null)
                        : null
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Vehicle' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                      />
                    )}
                  />
                )}
              />
            )}

            {!isEdit && (
              <Controller
                control={control}
                name="type"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    size="small"
                    disabled={isEditOnGoingMaintenance}
                    {...getAutocompleteVirtualizedProps({
                      options: maintenanceTypeOptions.array,
                    })}
                    onChange={(_, newValue) => {
                      setFormValue(field.name, newValue ? newValue.id : null, {
                        shouldValidate: true,
                      })
                    }}
                    value={
                      field.value
                        ? (maintenanceTypeOptions.byId.get(field.value) ?? null)
                        : null
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Type' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                      />
                    )}
                  />
                )}
              />
            )}

            <TextFieldControlled
              ControllerProps={{
                name: 'maintenancePartName',
                control,
              }}
              label={ctIntl.formatMessage({ id: 'maintenance.global.partName' })}
              fullWidth
              variant="outlined"
            />

            <Controller
              control={control}
              name="reason"
              render={({ field, fieldState }) => (
                <Autocomplete
                  size="small"
                  disabled={isEditOnGoingMaintenance}
                  {...getAutocompleteVirtualizedProps({
                    options: maintenanceReasonOptions.array,
                  })}
                  onChange={(_, newValue) => {
                    setFormValue(field.name, newValue ? newValue.id : null, {
                      shouldValidate: true,
                    })
                  }}
                  value={
                    field.value
                      ? (maintenanceReasonOptions.byId.get(field.value) ?? null)
                      : null
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={ctIntl.formatMessage({ id: 'Reason' })}
                      helperText={ctIntl.formatMessage({
                        id: fieldState.error?.message ?? '',
                      })}
                      error={!!fieldState.error}
                    />
                  )}
                />
              )}
            />

            <TextFieldControlled
              ControllerProps={{
                name: 'maintenanceNotes',
                control,
              }}
              label={ctIntl.formatMessage({
                id: 'Description',
              })}
              fullWidth
              variant="standard"
            />
            <Controller
              control={control}
              name="operationId"
              render={({ field, fieldState }) => (
                <Autocomplete
                  size="small"
                  disabled={isEditOnGoingMaintenance}
                  {...getAutocompleteVirtualizedProps({
                    options: maintenanceOperationStatesOptions.array,
                  })}
                  onChange={(_, newValue) => {
                    setFormValue(field.name, newValue ? newValue.id : null, {
                      shouldValidate: true,
                    })
                  }}
                  value={
                    field.value
                      ? (maintenanceOperationStatesOptions.byId.get(field.value) ??
                        null)
                      : null
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={ctIntl.formatMessage({
                        id: 'maintenance.global.securityStatus',
                      })}
                      helperText={ctIntl.formatMessage({
                        id: fieldState.error?.message ?? '',
                      })}
                      error={!!fieldState.error}
                    />
                  )}
                />
              )}
            />
          </Stack>

          <Divider sx={{ mb: 3 }} />

          <Stack
            gap={2}
            mb={5}
          >
            <Typography>
              {ctIntl.formatMessage({
                id: 'Start',
              })}
            </Typography>
            <Controller
              control={control}
              name="hasStartImmediately"
              render={({ field }) => (
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={field.value}
                        onChange={(event) => {
                          setFormValue(field.name, event.target.checked, {
                            shouldValidate: false,
                          })
                          if (event.target.checked) {
                            setFormValue('startTime', null, { shouldValidate: false })
                          }
                        }}
                        disabled={isEditOnGoingMaintenance}
                      />
                    }
                    label={ctIntl.formatMessage({
                      id: 'list.maintenance.scheduleMaintenance.addImmediately',
                    })}
                  />
                </FormGroup>
              )}
            />

            {!hasStartImmediately && (
              <Controller
                name="startTime"
                control={control}
                render={({ field, fieldState }) => (
                  <DateTimePicker
                    {...field}
                    disabled={isEditOnGoingMaintenance}
                    slotProps={{
                      textField: {
                        error: !!fieldState.error,
                        helperText: ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        }),
                      },
                    }}
                    value={field.value ? DateTime.fromJSDate(field.value) : null}
                    label={ctIntl.formatMessage({ id: 'Date / Time' })}
                    onChange={(value) => {
                      setFormValue('startTime', value?.toJSDate() ?? null, {
                        shouldValidate: true,
                      })
                    }}
                    minDateTime={DateTime.now()}
                  />
                )}
              />
            )}
          </Stack>

          <Divider sx={{ mb: 3 }} />

          <Stack
            gap={2}
            mb={5}
          >
            <Typography>
              {ctIntl.formatMessage({
                id: 'End',
              })}
            </Typography>
            <Controller
              control={control}
              name="isEndTimeUnknown"
              render={({ field }) => (
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={field.value}
                        onChange={(event) => {
                          setFormValue(field.name, event.target.checked, {
                            shouldValidate: false,
                          })
                          if (event.target.checked) {
                            setFormValue('endTime', null, { shouldValidate: false })
                          }
                        }}
                      />
                    }
                    label={ctIntl.formatMessage({
                      id: 'list.maintenance.scheduleMaintenance.unknown',
                    })}
                  />
                </FormGroup>
              )}
            />

            {!isEndTimeUnknown && (
              <Controller
                name="endTime"
                control={control}
                render={({ field, fieldState }) => (
                  <DateTimePicker
                    {...field}
                    disabled={isEndTimeUnknown}
                    slotProps={{
                      textField: {
                        error: !!fieldState.error,
                        helperText: ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        }),
                      },
                    }}
                    value={field.value ? DateTime.fromJSDate(field.value) : null}
                    label={ctIntl.formatMessage({ id: 'Date / Time' })}
                    onChange={(value) => {
                      setFormValue('endTime', value?.toJSDate() ?? null, {
                        shouldValidate: true,
                      })
                    }}
                    minDateTime={
                      maintenanceStartTime
                        ? DateTime.fromJSDate(maintenanceStartTime)
                        : DateTime.now()
                    }
                  />
                )}
              />
            )}
          </Stack>

          {/* Footer */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              onClick={() => onClose()}
              variant="outlined"
              color="secondary"
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>
            <Button
              color="primary"
              variant="contained"
              type="submit"
              loading={isLoadingWhenSubmit}
            >
              {ctIntl.formatMessage({ id: 'Save' })}
            </Button>
          </Box>
        </form>
      </Box>
    </DrawerBase>
  )
}

export default ScheduleMaintenanceDrawer
