import { useEffect, useMemo, useRef, useState } from 'react'
import {
  Chip,
  CircularProgress,
  DataGrid,
  getGridSingleSelectOperators,
  GridActionsCellItem,
  GridLogicOperator,
  LinearProgress,
  Tooltip,
  useDataGridColumnHelper,
  useGridApiRef,
  type DateRange,
  type GridColDef,
  type GridFilterModel,
  type GridPaginationMeta,
  type GridPaginationModel,
  type GridSortDirection,
  type GridSortModel,
  type PickersShortcutsItem,
} from '@karoo-ui/core'
import DownloadIcon from '@mui/icons-material/Download'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import type { DateTime } from 'luxon'
import { rgba } from 'polished'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import {
  DriverNameVisibilityStatus,
  type VehicleInspectionIdCustom,
  type VehicleInspectionIdStandard,
  type VehicleInspectionIdSubmit,
} from 'api/types'
import PageHeader from 'src/components/_containers/PageHeader'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import DriverWithChip from 'src/modules/app/components/driverChip/DriverWithChip'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useUserFormatLengthInKmOrMiles } from 'src/modules/components/connected/UserFormattedLengthInKmOrMiles'
import {
  mapColumnsToServerFilterableOrSortableColumns,
  mapFilterItemToServerFilter_Boolean,
  mapFilterItemToServerFilter_Date,
  mapFilterItemToServerFilter_Enum,
  mapFilterItemToServerFilter_String,
} from 'src/shared/data-grid/server-client/utils'
import { ctIntl } from 'src/util-components/ctIntl'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'

import {
  fetchMaintenanceVehicleInspectionDetails,
  useMaintenanceVehicleInspectionsQuery,
  type UseMaintenanceVehicleInspectionsQueryData,
} from './api/queries'
import {
  fetchMaintenanceVehicleInspectionsFilterModelSchema,
  VehicleInspectionApprovalStatus,
  type VehicleInspectionFormStatus,
  type VehicleInspectionUniqueRowId,
} from './api/types'
import createCustomInspectionPdf from './components/createCustomInspectionPdf'
import createStandardInspectionPdf from './components/createStandardInspectionPdf'
import {
  getInspectionFormStatusMsgId,
  statusSingleSelectColumns,
  VehicleInspectionDataGridCustomToolbar,
  type DataGridInnerFilterModel,
  type DataGridOuterFilterModel,
  type VehicleInspectionDataGridCustomToolbarProps,
} from './components/VehicleInspectionDataGridCustomToolbar'
import VehicleInspectionDetailsDrawer from './components/VehicleInspectionDetailsDrawer'

const sortableColumnIds = ['date'] as const
type SortableColumnId = (typeof sortableColumnIds)[number]

const filterModelSchema = fetchMaintenanceVehicleInspectionsFilterModelSchema

const filterableColumnIds = R.values(filterModelSchema.items)
  .filter(
    (item) =>
      item.shape.field.value !== 'required' && item.shape.field.value !== 'status',
  )
  .map((item) => item.shape.field.value)
type FilterableColumnId = (typeof filterableColumnIds)[number]

const columnsIds = {
  status: 'status',
  formName: 'formName',
  date: 'date',
  submittedBy: 'submittedBy',
  vehicleName: 'vehicleName',
  required: 'required',
  approvalStatusId: 'approvalStatusId',
  approvedBy: 'approvedBy',
  notes: 'notes',
} as const satisfies Record<
  FilterableColumnId | SortableColumnId,
  FilterableColumnId | SortableColumnId
> &
  Record<string, unknown>

type DataGridSortModel = [
  {
    field: SortableColumnId
    sort: 'asc' | 'desc'
  },
]

type StatusColumnValue = VehicleInspectionFormStatus | 'unknown' | null

type DataGridRow = UseMaintenanceVehicleInspectionsQueryData['rows'][number]

const PAGE_SIZE = 25

export default function VehicleInspections() {
  const gridApiRef = useGridApiRef()
  const shortcuts = useDateRangeShortcutItems()

  const { formatLengthInKmOrMiles } = useUserFormatLengthInKmOrMiles()

  const [currentDrawer, setCurrentDrawer] = useState<{
    type: 'vehicle_inspection_details'
    data: {
      inspection:
        | { id: VehicleInspectionIdCustom; type: 'custom' }
        | { id: VehicleInspectionIdStandard; type: 'standard' }
        | { id: VehicleInspectionIdSubmit; type: 'submit' }
    }
  } | null>(null)

  const [filterModel, setFilterModel] = useState<DataGridInnerFilterModel>(() => ({
    items: [],
    logicOperator: GridLogicOperator.And,
    quickFilterValues: [],
  }))
  const [downloadStatusObj, setDownloadStatusObj] = useState<
    Record<VehicleInspectionUniqueRowId, 'fetching' | 'idle'>
  >({})
  const [outerFilterModel, setOuterFilterModel] = useState(
    (): DataGridOuterFilterModel => ({
      required: {
        field: 'required',
        operator: 'is',
        value: 'any',
      },
      date: {
        field: 'date',
        operator: 'range',
        value: shortcuts.today.getValue(),
      },
      formName: {
        field: 'formName',
        operator: 'contains',
        value: null,
      },
      status: {
        field: 'status',
        operator: 'is',
        value: null,
      },
    }),
  )

  const mapPageToNextCursor = useRef<{
    [page: number]: Record<string, unknown> | undefined
  }>({})

  const [rowCount, setRowCount] = useState<number | undefined>(undefined)

  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: PAGE_SIZE,
  })

  const [sortModel, setSortModel] = useState<DataGridSortModel>([
    { field: columnsIds.date, sort: 'asc' },
  ])

  const approvalStatusSingleSelectColumns = useMemo(
    () =>
      [
        {
          label: ctIntl.formatMessage({
            id: 'maintenance.vehicleInspection.approvalStatus.Approved',
          }),
          value: VehicleInspectionApprovalStatus.APPROVED,
        },
        {
          label: ctIntl.formatMessage({ id: 'reusable.Pending approval' }),
          value: VehicleInspectionApprovalStatus.NOT_APPROVED,
        },
      ] as const satisfies ReadonlyArray<{
        label: string
        value: VehicleInspectionApprovalStatus
      }>,
    [],
  )

  const finalFilterModelItems = useMemo(
    () => [...filterModel.items, ...R.values(outerFilterModel)],
    [filterModel.items, outerFilterModel],
  )

  const queryParams = useMemo(
    () =>
      ({
        serverModel: {
          sort: [
            {
              field: 'row_num',
              sort: sortModel[0].sort,
            },
          ],
          pagination: {
            cursor: mapPageToNextCursor.current[paginationModel.page - 1] ?? 'start',
            pageSize: paginationModel.pageSize,
          },
          filter: {
            logicOperator: filterModel.logicOperator,
            items: Array_filterMap(finalFilterModelItems, (item, { RemoveSymbol }) => {
              switch (item.field) {
                case 'date': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_Date({
                    field: 'created_ts',
                    value,
                    operator,
                    precision: 'day',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'formName': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_String({
                    field: 'form_name',
                    value,
                    operator,
                    case: 'insensitive',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'submittedBy': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_String({
                    field: 'submitted_by',
                    value,
                    operator,
                    case: 'insensitive',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'status': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_Enum({
                    field: 'status',
                    value,
                    operator,
                    getValidSingleValueOrUndefined: (singleValue) => {
                      const result = statusSingleSelectColumns.find(
                        (opt) => opt.value === singleValue,
                      )
                      return result?.value ?? undefined
                    },
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'vehicleName': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_String({
                    field: 'vehicle_name',
                    value,
                    operator,
                    case: 'insensitive',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'required': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_Boolean({
                    field: 'is_required',
                    value,
                    operator,
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'approvalStatusId': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_Enum({
                    field: 'approval_status_id',
                    value,
                    operator,
                    getValidSingleValueOrUndefined: (singleValue) =>
                      approvalStatusSingleSelectColumns.find(
                        (opt) => opt.value === singleValue,
                      )?.value ?? undefined,
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                default: {
                  return RemoveSymbol
                }
              }
            }),
            searchTextFilterValues: filterModel.quickFilterValues,
          },
        },
      }) satisfies Parameters<
        typeof useMaintenanceVehicleInspectionsQuery
      >[0]['params'],
    [
      approvalStatusSingleSelectColumns,
      filterModel.logicOperator,
      filterModel.quickFilterValues,
      finalFilterModelItems,
      paginationModel.page,
      paginationModel.pageSize,
      sortModel,
    ],
  )

  const vehicleInspectionsQuery = useMaintenanceVehicleInspectionsQuery({
    params: queryParams,
  })

  const paginationMeta = useMemo((): GridPaginationMeta => {
    if (!vehicleInspectionsQuery.data) {
      /* The hasNextPage must not be set to false until there are actually no records left to fetch, because when hasNextPage becomes false,
         the Grid considers this as the last page and tries to set the rowCount value to a finite value.
      */
      return { hasNextPage: undefined }
    }

    return {
      hasNextPage: !!vehicleInspectionsQuery.data.serverModelPageInfo.nextCursorRow,
    }
  }, [vehicleInspectionsQuery.data])

  const shortcutsItems: Array<PickersShortcutsItem<DateRange<DateTime>>> = useMemo(
    () => [
      shortcuts.today,
      shortcuts.last7Days,
      shortcuts.thisWeek,
      shortcuts.thisMonth,
    ],
    [shortcuts.last7Days, shortcuts.thisMonth, shortcuts.thisWeek, shortcuts.today],
  )

  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'server' })

  const onPaginationModelChange = (newPaginationModel: GridPaginationModel) => {
    // We have the cursor, we can allow the page transition.
    if (
      newPaginationModel.page === 0 ||
      mapPageToNextCursor.current[newPaginationModel.page - 1]
    ) {
      setPaginationModel(newPaginationModel)
    }
  }

  useEffect(() => {
    if (
      queryParams.serverModel.pagination.cursor === 'start' &&
      vehicleInspectionsQuery.data
    ) {
      // The BE has a limitation where it can't return the total row count correctly when the cursor is anything other than 'start'.
      // Instead, it keeps returning a wrong total amount. So we only set the row count when asking for the first page.
      setRowCount(vehicleInspectionsQuery.data.serverModelPageInfo.totalRowCount)
    }
  }, [queryParams.serverModel.pagination.cursor, vehicleInspectionsQuery.data])

  useEffect(() => {
    if (!vehicleInspectionsQuery.data) {
      return
    }

    // We add nextCursor when available
    mapPageToNextCursor.current[paginationModel.page] =
      vehicleInspectionsQuery.data.serverModelPageInfo.nextCursorRow ?? undefined
  }, [paginationModel.page, vehicleInspectionsQuery.data])

  const resetTablePagination = () => {
    setPaginationModel({ ...paginationModel, page: 0 })
  }

  const onSortModelChange = (_newSortModel: GridSortModel) => {
    type TypeSortItem = {
      field: SortableColumnId
      sort: GridSortDirection
    }
    const newSortModel = _newSortModel as Array<TypeSortItem>

    const isStartTimeField = (item: TypeSortItem) => item.field === columnsIds.date

    resetTablePagination()
    setSortModel(() => [
      {
        field: columnsIds.date,
        sort: newSortModel.find((item) => isStartTimeField(item))?.sort ?? 'asc',
      },
    ])
  }

  const onFilterModelChange = async (newFilterModel: GridFilterModel) => {
    setFilterModel(newFilterModel as DataGridInnerFilterModel)
    resetTablePagination()

    // We set the filter model anyways but we try to throw an error while in dev for developers to fix it
    if (ENV.NODE_ENV === 'development') {
      // Might throw an error if the filter model is invalid (which is what we want)
      filterModelSchema.self.parse(newFilterModel)
      return
    }
    return
  }

  const columns = useMemo((): Array<GridColDef<DataGridRow>> => {
    const base: Array<GridColDef<DataGridRow>> = [
      columnHelper.string(
        (_, row) => (row.inspectionSource === 'standard' ? '-' : row.formTitle),
        {
          field: columnsIds.formName,
          headerName: ctIntl.formatMessage({
            id: 'maintenance.vehicleInspections.tableHeader.formName',
          }),
          flex: 1,
        },
      ),
      columnHelper.string((_, row) => row.vehicleName, {
        field: columnsIds.vehicleName,
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        flex: 1,
      }),
      columnHelper.string(
        (_, row) => {
          if (row.inspectionSource === 'standard' || row.required === 'unknown') {
            return ''
          }
          if (!row.required.value) {
            return ctIntl.formatMessage({ id: 'No' })
          }
          if (!row.required.formattedTime) {
            return ctIntl.formatMessage({ id: 'Yes' })
          }
          return ctIntl.formatMessage(
            { id: 'maintenance.vehicleInspections.required.yesWithTime' },
            {
              values: { timeVar: row.required.formattedTime },
            },
          )
        },
        {
          field: columnsIds.required,
          headerName: ctIntl.formatMessage({ id: 'Required' }),
          flex: 1,
        },
      ),
      columnHelper.singleSelect(
        (_, row) => (row.inspectionSource === 'standard' ? null : row.formStatus),
        {
          field: columnsIds.status,
          headerName: ctIntl.formatMessage({ id: 'Status' }),
          valueOptions: statusSingleSelectColumns,
          valueFormatter: (value) => {
            if (!value) return null
            if (value === 'unknown') {
              return 'unknown' as const
            }
            return ctIntl.formatMessage({
              id: getInspectionFormStatusMsgId(value),
            }) as StatusColumnValue
          },
          filterOperators: getGridSingleSelectOperators<
            DataGridRow,
            StatusColumnValue
          >().filter((operator) => operator.value === 'is'),
          renderCell: ({ row }) => {
            if (row.inspectionSource === 'standard' || row.formStatus === 'unknown') {
              return '-'
            }

            const color = match(row.formStatus)
              .returnType<'success' | 'warning' | 'error'>()
              .with('submitted', () => 'success')
              .with('pending', () => 'warning')
              .with('not_submitted', () => 'error')
              .exhaustive()
            return (
              <Chip
                size="small"
                variant="outlined"
                sx={({ palette }) => ({
                  background: rgba(palette[color].main, palette.action.selectedOpacity),
                  border: 0,
                  color: palette[color].main,
                })}
                label={ctIntl.formatMessage({
                  id: getInspectionFormStatusMsgId(row.formStatus),
                })}
              />
            )
          },
          flex: 1.5,
        },
      ),
      columnHelper.dateTime({
        field: columnsIds.date,
        headerName: ctIntl.formatMessage({ id: 'Date Time' }),
        valueGetter: (_, row) =>
          row.inspectionSource === 'custom_form_did_not_occur'
            ? null
            : row.submissionDate,
        filterOperators: columnHelper.utils
          .getGridDateColumnOperators({ showTime: false })
          .filter((operator) => operator.value === 'range'),
        flex: 1.3,
      }),
      columnHelper.string(
        (_, row) =>
          row.inspectionSource === 'custom_form_did_not_occur'
            ? null
            : (row.inspectionDriver?.name ?? null),
        {
          field: columnsIds.submittedBy,
          headerName: ctIntl.formatMessage({
            id: 'maintenance.vehicleInspection.submittedBy',
          }),
          flex: 1.5,
          renderCell: ({ row }) =>
            row.inspectionSource === 'custom_form_did_not_occur' ||
            !row.inspectionDriver ? null : (
              <DriverWithChip
                driverName={{
                  ...row.inspectionDriver,
                  status: DriverNameVisibilityStatus.PUBLIC,
                }}
                driverId={row.inspectionDriver.id}
                linkingMethod={{ type: row.inspectionDriver.linkageMethod }}
                picture={row.inspectionDriver.avatar ?? ''}
              />
            ),
        },
      ),
      columnHelper.singleSelect(
        (_, row) => ('approvalStatusId' in row ? row.approvalStatusId : null),
        {
          field: columnsIds.approvalStatusId,
          headerName: ctIntl.formatMessage({
            id: 'maintenance.vehicleInspections.tableHeader.approvalStatus',
          }),
          valueOptions: approvalStatusSingleSelectColumns,
          renderCell: ({ row }) => {
            if (!('approvalStatusId' in row)) {
              return '-'
            }
            if (row.approvalStatusId === VehicleInspectionApprovalStatus.APPROVED) {
              return ctIntl.formatMessage({ id: 'Yes' })
            }
            return ctIntl.formatMessage({ id: 'No' })
          },
          flex: 1,
        },
      ),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        align: 'center',
        getActions: ({ row }) => {
          if (row.inspectionSource === 'custom_form_did_not_occur') {
            return []
          }

          const id = row.id
          return [
            <Tooltip
              title={ctIntl.formatMessage({ id: 'Info' })}
              arrow
              key="info"
            >
              <GridActionsCellItem
                icon={<VisibilityOutlinedIcon />}
                label={ctIntl.formatMessage({ id: 'Info' })}
                onClick={() => {
                  match(row)
                    .with({ inspectionSource: 'standard' }, ({ inspectionId }) => {
                      setCurrentDrawer({
                        type: 'vehicle_inspection_details',
                        data: {
                          inspection: { id: inspectionId, type: 'standard' },
                        },
                      })
                    })
                    .with({ inspectionSource: 'submit' }, ({ inspectionId }) => {
                      setCurrentDrawer({
                        type: 'vehicle_inspection_details',
                        data: {
                          inspection: { id: inspectionId, type: 'submit' },
                        },
                      })
                    })
                    .with({ inspectionSource: 'custom' }, ({ inspectionId }) => {
                      setCurrentDrawer({
                        type: 'vehicle_inspection_details',
                        data: {
                          inspection: { id: inspectionId, type: 'custom' },
                        },
                      })
                    })
                    .exhaustive()
                }}
              />
            </Tooltip>,
            downloadStatusObj[id] === 'fetching' ? (
              <GridActionsCellItem
                key="loading"
                icon={<CircularProgress size={20} />}
                label={ctIntl.formatMessage({ id: 'Loading' })}
              />
            ) : (
              <Tooltip
                title={ctIntl.formatMessage({ id: 'Download' })}
                arrow
                key="download"
              >
                <GridActionsCellItem
                  icon={<DownloadIcon />}
                  label={ctIntl.formatMessage({ id: 'Download' })}
                  onClick={async () => {
                    setDownloadStatusObj((prev) => ({ ...prev, [id]: 'fetching' }))
                    const params = match(row)
                      .with({ inspectionSource: 'standard' }, ({ inspectionId }) => ({
                        id: inspectionId,
                        type: 'standard' as const,
                      }))
                      .with({ inspectionSource: 'submit' }, ({ inspectionId }) => ({
                        id: inspectionId,
                        type: 'submit' as const,
                      }))
                      .with({ inspectionSource: 'custom' }, ({ inspectionId }) => ({
                        id: inspectionId,
                        type: 'custom' as const,
                      }))
                      .exhaustive()

                    const result =
                      await fetchMaintenanceVehicleInspectionDetails(params)

                    if (result.type === 'custom') {
                      createCustomInspectionPdf(result, formatLengthInKmOrMiles)
                    } else {
                      const data = result
                      createStandardInspectionPdf({
                        ...data,
                        odometer: {
                          value: data.odometer,
                          formatLengthInKmOrMiles,
                        },
                      })
                    }

                    setDownloadStatusObj((prev) => ({ ...prev, [id]: 'idle' }))
                  }}
                />
              </Tooltip>
            ),
          ]
        },
        flex: 1,
      },
    ]

    return mapColumnsToServerFilterableOrSortableColumns(base, {
      filterableColumnIds,
      sortableColumnIds,
    })
  }, [
    approvalStatusSingleSelectColumns,
    columnHelper,
    downloadStatusObj,
    formatLengthInKmOrMiles,
  ])

  const rows = useMemo(
    (): ReadonlyArray<DataGridRow> => vehicleInspectionsQuery.data?.rows ?? [],
    [vehicleInspectionsQuery.data],
  )
  return (
    <PageWithMainTableContainer>
      <PageHeader>
        <PageHeader.Title>
          {ctIntl.formatMessage({ id: 'maintenance.vehicleInspections.title' })}
        </PageHeader.Title>
      </PageHeader>

      <UserDataGridWithSavedSettingsOnIDB<DataGridRow>
        apiRef={gridApiRef}
        Component={DataGrid}
        dataGridId="newMaintenanceVehicleInspections"
        sortingMode="server"
        filterMode="server"
        pagination
        pageSizeOptions={[PAGE_SIZE]}
        paginationMode="server"
        paginationModel={paginationModel}
        onPaginationModelChange={onPaginationModelChange}
        paginationMeta={paginationMeta}
        filterDebounceMs={500} // since were doing server side-filtering, we can not afford a lower debounce (to not doo too many requests)
        rowCount={rowCount ?? 0}
        rows={rows}
        loading={vehicleInspectionsQuery.isFetching}
        columns={columns}
        filterModel={filterModel}
        onFilterModelChange={onFilterModelChange}
        slots={{
          toolbar: VehicleInspectionDataGridCustomToolbar,
          loadingOverlay: LinearProgress,
          noRowsOverlay: () => <DataStatePlaceholder label="No data available" />,
        }}
        sortModel={sortModel}
        onSortModelChange={onSortModelChange}
        slotProps={{
          toolbar: {
            filterModels: {
              value: outerFilterModel,
              setter: setOuterFilterModel,
            },
            rowCount: rowCount ?? 0,
            dateRangePicker: { shortcuts: shortcutsItems },
            forms: vehicleInspectionsQuery.data?.serverModelPageInfo.forms ?? [],
            queryParams,
            gridApiRef,
          } satisfies VehicleInspectionDataGridCustomToolbarProps,
          pagination: {
            showFirstButton: true,
            showLastButton: false,
            slotProps: {
              actions: {
                previousButton: {
                  disabled: paginationModel.page === 0,
                },
                nextButton: {
                  disabled:
                    vehicleInspectionsQuery.isFetching || !paginationMeta.hasNextPage,
                },
              },
            },
          },
        }}
      />
      {currentDrawer?.type === 'vehicle_inspection_details' && (
        <VehicleInspectionDetailsDrawer
          onClose={() => setCurrentDrawer(null)}
          inspection={currentDrawer.data.inspection}
        />
      )}
    </PageWithMainTableContainer>
  )
}
