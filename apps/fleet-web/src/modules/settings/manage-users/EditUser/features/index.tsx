import { Fragment, useMemo, useState } from 'react'
import {
  CircularProgressDelayedAbsolute,
  FormControlLabel,
  FormGroup,
  Stack,
  Switch,
} from '@karoo-ui/core'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import {
  getAIChatSettingEnabled,
  getPermissionsAppState,
  getSettings_UNSAFE,
  getVisionSetting,
} from 'duxs/user'
import { doesCurrentUserHaveAccessFromSetting } from 'duxs/user-sensitive-selectors'
import { getVehicles } from 'duxs/vehicles'
import { useFleetUserOrRoleVehicles } from 'src/modules/admin/shared/data-access/vehicles/queries'
import { ALERT_CENTER } from 'src/modules/app/components/routes/alert-center'
import { ALERTS } from 'src/modules/app/components/routes/alerts'
import { MAP } from 'src/modules/app/components/routes/map'
import { TACHOGRAPH } from 'src/modules/app/components/routes/tachograph'
import type {
  PermissionsAppState,
  PermissionsSelector,
} from 'src/modules/app/components/routes/types'
import { VISION } from 'src/modules/app/components/routes/vision'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import { useUserEditingContext } from '../UserEditingContext'
import AlertFeatureSection from './AlertFeatureSection'
import FeatureSection from './FeatureSection'
import FeatureSectionAuth from './FeatureSectionAuth'
import FeatureSectionWithoutCategories, {
  FeatureSectionWithoutCategoriesUI,
} from './FeatureSectionWithoutCategories'
import ListFeatureSection from './ListFeatureSection'
import {
  getNormalizedSettingNameFromRawDbName,
  useFetchUserAclFeaturesQuery,
  useUpdateUserAclFeaturesMutation,
  type AclKey,
  type NormalizedSettingName,
  type RawDBSettingName,
} from './queries'
import {
  getAclSectionFromNormalizedSetting,
  getMaybeNormalizedSettingFromAclSection,
} from './utils'

const getFeatureCategoryId = (featureCat: AclKey) =>
  'features-' + featureCat.toLocaleLowerCase().replace(' ', '-')

const featuresSection = {
  map: {
    label: 'Map',
    categoryName: 'Map',
    id: getFeatureCategoryId('Map'),
    selector: MAP.tab.selector,
  },
  list: { label: 'List', id: getFeatureCategoryId('List'), categoryName: 'List' },
  reports: {
    label: 'Reports',
    id: getFeatureCategoryId('Reports'),
    categoryName: 'Reports',
  },
  alerts: {
    label: 'Alerts',
    categoryName: 'Alerts',
    id: getFeatureCategoryId('Alerts'),
    selector: ALERTS.tab.selector,
  },
  tachograph: {
    label: 'Tachograph',
    id: getFeatureCategoryId('Tachograph'),
    categoryName: 'Tachograph',
    selector: TACHOGRAPH.tab.selector,
  },
  user: { label: 'User', id: getFeatureCategoryId('User'), categoryName: 'User' },
  vision: {
    label: VISION.tab.text,
    categoryName: 'Vision',
    id: getFeatureCategoryId('Vision'),
    selector: getVisionSetting,
  },
  alertCenter: {
    label: ALERT_CENTER.tab.text,
    categoryName: 'Control Room',
    id: getFeatureCategoryId('Control Room'),
    selector: ALERT_CENTER.tab.selector,
  },
  carpool: {
    label: (state: PermissionsAppState) =>
      getSettings_UNSAFE(state).carpoolAppName as string,
    categoryName: 'Carpool',
    id: getFeatureCategoryId('Carpool'),
    selector: (state: PermissionsAppState) =>
      doesCurrentUserHaveAccessFromSetting(state, 'carpool'),
  },
  coaching: {
    label: 'Coaching',
    categoryName: 'Coaching',
    id: getFeatureCategoryId('Coaching'),
    selector: (state: PermissionsAppState) =>
      doesCurrentUserHaveAccessFromSetting(state, 'coaching'),
  },
  credits: {
    label: 'Credits',
    categoryName: 'Credits',
    id: getFeatureCategoryId('Credits'),
    selector: (state: PermissionsAppState) =>
      doesCurrentUserHaveAccessFromSetting(state, 'credits'),
  },
  delivery: {
    label: 'Delivery',
    categoryName: 'Delivery',
    id: getFeatureCategoryId('Delivery'),
    selector: (state: PermissionsAppState) =>
      doesCurrentUserHaveAccessFromSetting(state, 'delivery'),
  },
  fieldService: {
    label: 'industrySelectionModal.industry.fieldService',
    categoryName: 'Field Service',
    id: getFeatureCategoryId('Delivery'),
    selector: (state: PermissionsAppState) =>
      doesCurrentUserHaveAccessFromSetting(state, 'fieldService'),
  },
  maintenance: {
    label: 'Maintenance',
    categoryName: 'Maintenance',
    id: getFeatureCategoryId('Maintenance'),
    selector: (state: PermissionsAppState) =>
      doesCurrentUserHaveAccessFromSetting(state, 'listMaintenance'),
  },
} satisfies Record<
  string,
  {
    label: string | ((permissionsAppState: PermissionsAppState) => string)
    categoryName: AclKey
    id: string
    selector?: PermissionsSelector
  }
>

const llmChatFeatureSection = {
  label: 'aiChat.header.title',
  id: 'llm-chat',
  selector: getAIChatSettingEnabled,
  type: 'custom',
} as const

const fixedFeaturesSection = Object.values(featuresSection).map((c) => ({
  categoryName: c.categoryName,
  label: c.label,
  type: 'fixed-feature' as const,
}))

const MainMenuCategory = 'Main menu' satisfies AclKey

function Features() {
  const { userId } = useUserEditingContext()

  const fetchUserAclFeaturesQuery = useFetchUserAclFeaturesQuery({
    client_user_id: userId,
  })

  return match(fetchUserAclFeaturesQuery)
    .with({ status: 'error' }, () => null)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'success' }, ({ data }) => (
      <FeaturesContent userAclFeaturesData={data} />
    ))
    .exhaustive()
}

type FeaturesContentProps = {
  userAclFeaturesData: NonNullable<
    ReturnType<typeof useFetchUserAclFeaturesQuery>['data']
  >
}

const FeaturesContent = ({ userAclFeaturesData }: FeaturesContentProps) => {
  const { userId } = useUserEditingContext()
  const permissionsAppState = useTypedSelector(getPermissionsAppState)

  const updateUserAclFeaturesMutation = useUpdateUserAclFeaturesMutation()

  const assignedVehicles = useFleetUserOrRoleVehicles({ data: userId }).data
  const vehicleList = useTypedSelector(getVehicles)
  const userManageUsersMeta = userAclFeaturesData.acl.settingsMap.get('userManageUsers')
  const userManageRolesMeta = userAclFeaturesData.acl.settingsMap.get('userManageRoles')
  const llmChatMeta = userAclFeaturesData.acl.settingsMap.get('llmChat')

  const explicitFeatures = useMemo(
    () => [userManageUsersMeta, userManageRolesMeta, llmChatMeta],
    [userManageUsersMeta, userManageRolesMeta, llmChatMeta],
  )

  const shouldShowImmobiliseVehicleSetting = useMemo(() => {
    if (R.isNullish(assignedVehicles) || R.isEmpty(vehicleList)) {
      return false
    }

    return assignedVehicles.some(
      (vehicleId) => vehicleList.find((v) => v.id === vehicleId)?.startInhibitAllowed,
    )
  }, [assignedVehicles, vehicleList])

  const handleToggleAllFeatures = (value: boolean) => {
    updateUserAclFeaturesMutation.mutate({
      client_user_id: userId,
      features: [...userAclFeaturesData.acl.settingsMap.values()].map((feature) => ({
        name: feature.rawDbName,
        value: value ? ('true' as const) : ('false' as const),
      })),
    })
  }

  const categoriesName = useGetCategoriesNameWithPermission()

  const hasAllFeatures = useMemo(() => {
    if (explicitFeatures.some((feature) => !!feature && !feature.value)) {
      return false
    }

    // This variable contains the list of settings that are shown in UI
    // Initialize the settingsToCheck set with the listFeatures
    const settingsToCheck = new Set<NormalizedSettingName>(
      userAclFeaturesData.listFeatures.map((l) => l.flag),
    )

    // Function to add normalize settings to the check set
    const addSettingsToCheck = (settings: ReadonlyArray<RawDBSettingName>) => {
      for (const item of settings) {
        const normalizedSetting = getNormalizedSettingNameFromRawDbName(item)
        settingsToCheck.add(normalizedSetting)
      }
    }

    // Function to handle 'List' category settings, because 'List' category settings will have 2 level in the tree
    const processListCategorySettings = (
      featuresSettings: ReadonlyArray<RawDBSettingName>,
    ) => {
      for (const listItem of featuresSettings) {
        const listCategoryName = getNormalizedSettingNameFromRawDbName(listItem)

        // settingsToCheck was initialized with the list of features that the user has permission for,
        // so we need to check if the user has permission for each feature before adding the child permissions to the check set
        if (settingsToCheck.has(listCategoryName)) {
          const categoryNameFromNormalized =
            getAclSectionFromNormalizedSetting(listCategoryName)
          const sectionSettings = categoryNameFromNormalized
            ? (userAclFeaturesData.acl.categories.get(categoryNameFromNormalized) ?? [])
            : []
          for (const item of sectionSettings) {
            const settingName = getNormalizedSettingNameFromRawDbName(item)
            if (
              settingName !== 'canImmobiliseVehicles' ||
              shouldShowImmobiliseVehicleSetting
            ) {
              settingsToCheck.add(settingName)
            }
          }
        }
      }
    }

    // Add main menu settings to the check set
    const mainMenuSettings =
      userAclFeaturesData.acl.categories.get(MainMenuCategory) || []
    addSettingsToCheck(mainMenuSettings as ReadonlyArray<RawDBSettingName>)

    // Iterate over all category names and process their settings
    for (const categoryName of categoriesName) {
      const sectionSettings = userAclFeaturesData.acl.categories.get(categoryName) || []
      if (categoryName === 'List') {
        processListCategorySettings(sectionSettings)
      } else {
        addSettingsToCheck(sectionSettings)
      }
    }

    // Check that every setting in the user's ACL is either not shown in the UI or has a true value
    return [...userAclFeaturesData.acl.settingsMap.values()].every(
      (setting) => !settingsToCheck.has(setting.name) || setting.value,
    )
  }, [
    categoriesName,
    shouldShowImmobiliseVehicleSetting,
    userAclFeaturesData.acl.categories,
    userAclFeaturesData.acl.settingsMap,
    userAclFeaturesData.listFeatures,
    explicitFeatures,
  ])

  // The "Main Menu" section won't be shown anymore. All the permissions in the "Main Menu" will be a separate section.
  // Therefore, we need to check if these permissions are not shown yet. If not, we need to add them to the sections list.
  // We already filter with the current user's permission in the query, so no need to check permission here.
  const excludedFeaturesSection = useMemo(() => {
    const existingCategoriesSettingNames = new Set<NormalizedSettingName>()
    for (const { categoryName } of Object.values(featuresSection)) {
      const maybeNormalizedSetting =
        getMaybeNormalizedSettingFromAclSection(categoryName)
      if (maybeNormalizedSetting) {
        existingCategoriesSettingNames.add(maybeNormalizedSetting)
      }
    }

    const result: Array<{
      categoryName: AclKey
      label: string
      type: 'excluded-feature'
    }> = []
    for (const rawSettingName of userAclFeaturesData.acl.categories.get(
      MainMenuCategory,
    ) ?? []) {
      const normalizedSettingName =
        getNormalizedSettingNameFromRawDbName(rawSettingName)

      const categoryNameFromNormalized =
        getAclSectionFromNormalizedSetting(normalizedSettingName)
      if (
        !existingCategoriesSettingNames.has(normalizedSettingName) &&
        categoryNameFromNormalized
      ) {
        result.push({
          categoryName: categoryNameFromNormalized,
          label:
            userAclFeaturesData.acl.settingsMap.get(normalizedSettingName)
              ?.description ?? categoryNameFromNormalized,
          type: 'excluded-feature',
        })
      }
    }
    return result
  }, [userAclFeaturesData.acl.categories, userAclFeaturesData.acl.settingsMap])

  type SortedFeaturesSection =
    | {
        categoryName: AclKey
        label: string
        type: 'fixed-feature'
      }
    | {
        categoryName: AclKey
        label: string
        type: 'excluded-feature'
      }
    | {
        label: string
        id: string
        type: 'custom'
      }

  // We must sort the sections alphabetically
  const sortedFeaturesSection = useMemo(
    (): Array<SortedFeaturesSection> =>
      [...fixedFeaturesSection, ...excludedFeaturesSection, llmChatFeatureSection]
        .map(
          ({ label, ...c }): SortedFeaturesSection => ({
            ...c,
            label: ctIntl.formatMessage({
              id: typeof label === 'function' ? label(permissionsAppState) : label,
            }),
          }),
        )
        .sort((a, b) => a.label.localeCompare(b.label)),
    [excludedFeaturesSection, permissionsAppState],
  )

  return (
    <Stack gap={1}>
      <FormGroup sx={{ display: 'flex', flexDirection: 'row', px: 1 }}>
        <FormControlLabel
          control={
            <Switch
              checked={hasAllFeatures}
              onChange={(_, checked) => handleToggleAllFeatures(checked)}
              size="small"
            />
          }
          label={ctIntl.formatMessage({ id: 'All Available Permissions' })}
        />

        {!!userManageUsersMeta && (
          <FormControlLabel
            control={
              <Switch
                checked={userManageUsersMeta.value}
                onChange={(_, checked) => {
                  updateUserAclFeaturesMutation.mutate({
                    client_user_id: userId,
                    features: [
                      {
                        name: userManageUsersMeta.rawDbName,
                        value: checked ? 'true' : 'false',
                      },
                    ],
                  })
                }}
                size="small"
              />
            }
            label={ctIntl.formatMessage({ id: 'Manage Users' })}
          />
        )}

        {!!userManageRolesMeta && (
          <FormControlLabel
            control={
              <Switch
                checked={userManageRolesMeta.value}
                onChange={(_, checked) => {
                  updateUserAclFeaturesMutation.mutate({
                    client_user_id: userId,
                    features: [
                      {
                        name: userManageRolesMeta.rawDbName,
                        value: checked ? 'true' : 'false',
                      },
                    ],
                  })
                }}
                size="small"
              />
            }
            label={ctIntl.formatMessage({ id: 'Manage Roles' })}
          />
        )}
      </FormGroup>
      {sortedFeaturesSection.map((sectionToMatch) =>
        match(sectionToMatch)
          .with({ type: 'fixed-feature' }, { type: 'excluded-feature' }, (section) => (
            <Fragment key={section.categoryName}>
              {section.type === 'excluded-feature' && (
                <FeatureSectionWithoutCategories
                  key={section.categoryName}
                  section={section.categoryName}
                  label={section.label}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}
              {section.categoryName === featuresSection.credits.categoryName && (
                <FeatureSectionAuth
                  section={featuresSection.credits}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}
              {section.categoryName === featuresSection.map.categoryName && (
                <FeatureSectionAuth
                  section={featuresSection.map}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}

              {section.categoryName === featuresSection.list.categoryName && (
                <ListFeatureSection
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                  listFeatures={userAclFeaturesData.listFeatures}
                />
              )}

              {section.categoryName === featuresSection.reports.categoryName && (
                <FeatureSection
                  section={featuresSection.reports.categoryName}
                  label={featuresSection.reports.label}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}

              {section.categoryName === featuresSection.alerts.categoryName && (
                <AlertFeatureSection
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                  alertFeatures={userAclFeaturesData.alertFeatures}
                  section={featuresSection.alerts}
                />
              )}

              {section.categoryName === featuresSection.tachograph.categoryName && (
                <FeatureSection
                  section={featuresSection.tachograph.categoryName}
                  label={featuresSection.tachograph.label}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                  infoTooltips={{
                    tachographWindowsAppFileSync: ctIntl.formatMessage({
                      id: 'tachograph.permission.fileSync.tooltip',
                    }),
                  }}
                />
              )}

              {section.categoryName === featuresSection.user.categoryName && (
                <FeatureSection
                  section={featuresSection.user.categoryName}
                  label={featuresSection.user.label}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}

              {section.categoryName === featuresSection.vision.categoryName && (
                <FeatureSectionAuth
                  section={featuresSection.vision}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}

              {section.categoryName === featuresSection.alertCenter.categoryName && (
                <FeatureSectionAuth
                  section={featuresSection.alertCenter}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}

              {section.categoryName === featuresSection.carpool.categoryName && (
                <FeatureSectionAuth
                  section={{
                    ...featuresSection.carpool,
                    label: featuresSection.carpool.label(permissionsAppState),
                  }}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}

              {section.categoryName === featuresSection.coaching.categoryName && (
                <FeatureSectionAuth
                  section={featuresSection.coaching}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}

              {section.categoryName === featuresSection.delivery.categoryName && (
                <FeatureSectionAuth
                  section={featuresSection.delivery}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                  infoTooltips={{
                    deliveryAllowAdminManagementAccess: ctIntl.formatMessage(
                      {
                        id: 'This grants this user the ability to manage all jobs, including those assigned to other sub-users, in addition to accessing all {name} features and settings.',
                      },
                      {
                        values: {
                          name: ctIntl.formatMessage({ id: 'Delivery' }).toLowerCase(),
                        },
                      },
                    ),
                  }}
                />
              )}

              {section.categoryName === featuresSection.fieldService.categoryName && (
                <FeatureSectionAuth
                  section={featuresSection.fieldService}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                  infoTooltips={{
                    deliveryAllowAdminManagementAccess: ctIntl.formatMessage(
                      {
                        id: 'This grants this user the ability to manage all jobs, including those assigned to other sub-users, in addition to accessing all {name} features and settings.',
                      },
                      {
                        values: {
                          name: ctIntl
                            .formatMessage({
                              id: 'industrySelectionModal.industry.fieldService',
                            })
                            .toLowerCase(),
                        },
                      },
                    ),
                  }}
                />
              )}
              {section.categoryName === featuresSection.maintenance.categoryName && (
                <FeatureSectionAuth
                  section={featuresSection.maintenance}
                  categories={userAclFeaturesData.acl.categories}
                  settings={userAclFeaturesData.acl.settingsMap}
                />
              )}
            </Fragment>
          ))
          .with({ type: 'custom' }, (section) => (
            <Fragment key={section.id}>
              {(() => {
                if (section.id === llmChatFeatureSection.id) {
                  return (
                    !!llmChatMeta && (
                      <FeatureSectionWithoutCategoriesUI
                        value={llmChatMeta.value}
                        label={llmChatFeatureSection.label}
                        handleCheckboxChange={(checked) => {
                          updateUserAclFeaturesMutation.mutate({
                            client_user_id: userId,
                            features: [
                              {
                                name: llmChatMeta.rawDbName,
                                value: checked ? 'true' : 'false',
                              },
                            ],
                          })
                        }}
                      />
                    )
                  )
                }

                return null
              })()}
            </Fragment>
          ))
          .exhaustive(),
      )}
    </Stack>
  )
}

export default Features

const useGetCategoriesNameWithPermission = () => {
  const permissionsAppState = useTypedSelector(getPermissionsAppState)

  const [categoryNames] = useState((): Set<AclKey> => {
    const names = new Set<AclKey>()

    for (const featureSection of Object.values(featuresSection)) {
      if ('selector' in featureSection) {
        if (featureSection.selector(permissionsAppState)) {
          names.add(featureSection.categoryName)
        }
      } else {
        names.add(featureSection.categoryName)
      }
    }

    return names
  })

  return categoryNames
}
