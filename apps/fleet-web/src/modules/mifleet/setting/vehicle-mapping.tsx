import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  DataGrid,
  GridActionsCellItem,
  LinearProgress,
  TextField,
  useCallbackBranded,
  useSearchTextField,
  type GridColDef,
  type GridRowHeightParams,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined'
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import ModeEditOutlineOutlinedIcon from '@mui/icons-material/ModeEditOutlineOutlined'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { connect } from 'react-redux'
import { withRouter, type RouteComponentProps } from 'react-router'
import { z } from 'zod/v4'

import type { VehicleId } from 'api/types'
import { addToast } from 'duxs/toast'
import { actions as reducerActions } from 'duxs/vehicle-mapping'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import {
  fetchDocumentArrayTypes,
  getArrayTypes,
} from 'src/modules/mifleet/DocumentsEdit/slice'
import type { AppState } from 'src/root-reducer'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { messages } from 'src/shared/forms/messages'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from 'src/util-functions/search-utils'
import { isTrue } from 'src/util-functions/validation'

import { ctToast } from 'cartrack-ui-kit'
import DeleteSettingsDialog from '../shared/DeleteSettingsDialog'
import { CustomPagination } from '../shared/footer-dataGrid'
import MifleetContainer from '../shared/mifleetContainer'
import type { VehicleMappingType } from './shared/type'

type vehicleTypes = {
  driver_id: number | null
  is_deleted: boolean | string
  name: string
  plate: string
  value: string
  vehicle_id: number
}

type props = RouteComponentProps &
  ReturnType<typeof mapStateToProps> &
  typeof actionCreators

const initialFormValues: FixMeAny = {
  vehicle_id: '',
  mapped_registration_1: '',
  mapped_registration_2: '',
  mapped_registration_3: '',
  flag: false,
}
const vehicleMappingSchema = z.object({
  vehicle_id: z.string().min(1, { message: messages.required }),
  mapped_registration_1: z.string().min(1, { message: messages.required }),
  mapped_registration_2: z.string().nullable(),
  mapped_registration_3: z.string().nullable(),
  flag: z.boolean(),
})

const VehicleMapping = ({
  list,
  fetchVehicleMapping,
  deletevehicleMapping,
  arrayTypes,
  fetchArrayTypes,
  updateVehicle,
  createVehicle,
  doAddToast,
}: props) => {
  const { vehicles } = arrayTypes
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
  const [vehicleList, setVehicleList] = useState<Array<VehicleMappingType>>([])
  const [selectedVehicle, setSelectedVehicle] = useState<VehicleMappingType | null>(
    null,
  )
  const searchProps = useSearchTextField('')

  const vehicleListById = useMemo(() => {
    const vehiclesStore = []
    for (const row of list) {
      const v = []
      if (row.mapped_registration_1) {
        v.push(row.mapped_registration_1)
      }
      if (row.mapped_registration_2) {
        v.push(row.mapped_registration_2)
      }
      if (row.mapped_registration_3) {
        v.push(row.mapped_registration_3)
      }
      vehiclesStore.push([row.vehicle_id as VehicleId, v] satisfies [
        VehicleId,
        Array<string>,
      ])
    }
    return vehiclesStore
  }, [list])

  useEffect(() => {
    if (list) {
      setVehicleList(list)
    }
  }, [list])

  useEffect(() => {
    fetchVehicleMapping()
  }, [fetchVehicleMapping])

  useEffect(() => {
    fetchArrayTypes()
  }, [fetchArrayTypes])

  const {
    control,
    formState: { errors, isValid },
    reset,
    setValue: setFormValue,
    getValues,
  } = useForm<any>({
    resolver: zodResolverV4(vehicleMappingSchema),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: initialFormValues,
  })

  const watchVehiceId = useWatch({ name: 'vehicle_id', control })
  const watchRegistration1 = useWatch({ name: 'mapped_registration_1', control })
  const watchRegistration2 = useWatch({ name: 'mapped_registration_2', control })
  const watchRegistration3 = useWatch({ name: 'mapped_registration_3', control })
  const watchIsCreate = useWatch({ name: 'isCreate', control })
  const watchFlag = useWatch({ name: 'flag', control })
  const watchId = useWatch({ name: 'id', control })
  const errorsLength = Object.keys(errors).length

  const showAllVehicles = useCallback(
    (vehicles: Array<any>) => {
      const selectedVehicleIds = new Set<string>()
      for (const v of vehicleList) {
        if (v.vehicle_id !== watchVehiceId) {
          selectedVehicleIds.add(v.vehicle_id)
        }
      }

      const activeItems = vehicles.filter(
        (item) =>
          !isTrue(item.is_deleted) &&
          !selectedVehicleIds.has(item.vehicle_id.toString()),
      )
      return activeItems.map((i) => ({
        value: i.value,
        label: i.plate,
      }))
    },
    [vehicleList, watchVehiceId],
  )

  const handleToggleModal = (e: FixMeAny, row: VehicleMappingType) => {
    e.preventDefault()
    e.stopPropagation()
    setIsModalOpen(!isModalOpen)
    setSelectedVehicle(row)
  }
  const handleDeleteRow = () => {
    if (selectedVehicle != null) {
      deletevehicleMapping(selectedVehicle.vehicle_id)
    }
  }

  const handleModalResult = (val: boolean) => {
    if (val) {
      handleDeleteRow()
    }
    setIsModalOpen(!isModalOpen)
    setSelectedVehicle(null)
  }

  const renderInputCell = (
    row: VehicleMappingType,
    key: keyof VehicleMappingType,
    required?: boolean,
  ) => {
    const editing = !watchFlag ? false : row.vehicle_id === watchId
    if (!editing) {
      return row[key]
    }

    return (
      <TextFieldControlled
        ControllerProps={{
          name: key,
          control,
        }}
        sx={{
          input: {
            backgroundColor: '#fff',
          },
        }}
        onKeyDown={(event) => {
          // https://stackoverflow.com/questions/71055614/why-is-the-space-key-being-filtered-out-by-muis-text-field-component
          event.stopPropagation()
        }}
        required={required || false}
      />
    )
  }
  const currentVehicle = (selectedItem: string) => {
    if (selectedItem) {
      const vehicle = vehicles.find((v: vehicleTypes) => v.value === selectedItem)
      return {
        label: vehicle?.plate,
        value: vehicle?.value,
      }
    }
    return null
  }

  const renderDropdownCell = (
    row: VehicleMappingType,
    key: 'vehicle_id',
    required?: boolean,
  ) => {
    const editing = !watchFlag ? false : row.vehicle_id === watchId

    if (!editing) {
      return row.plate
    }
    return (
      <Controller
        control={control}
        name={key}
        render={({ field, fieldState }) => (
          <Autocomplete
            fullWidth
            {...getAutocompleteVirtualizedProps({
              options: showAllVehicles(vehicles),
            })}
            onChange={(_, newValue: any) => {
              const value = newValue ? newValue.value : ''
              setFormValue(field.name, value, {
                shouldValidate: true,
              })
            }}
            value={currentVehicle(field.value)}
            disabled={!watchIsCreate}
            renderInput={(params) => (
              <TextField
                sx={{
                  '.MuiOutlinedInput-root': {
                    backgroundColor: '#fff',
                  },
                }}
                {...params}
                required={required}
                helperText={ctIntl.formatMessage({
                  id: fieldState.error?.message ?? '',
                })}
                error={!!fieldState.error}
              />
            )}
          />
        )}
      />
    )
  }
  const handleEditItem = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement, MouseEvent>,
    row: VehicleMappingType,
  ) => {
    e.preventDefault()
    e.stopPropagation()
    if (watchIsCreate) {
      ctToast.fire(
        'error',
        ctIntl.formatMessage({ id: 'mifleet.settings.save.form.first' }),
      )
      return
    }
    reset({ ...row, flag: true, id: row.vehicle_id })
  }
  const handleSaveItem = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement, MouseEvent>,
  ) => {
    e.preventDefault()
    e.stopPropagation()

    //check duplicate alias on selected vehicle
    const currentRowAlias = []
    currentRowAlias.push(
      watchRegistration1?.trim(),
      watchRegistration2?.trim(),
      watchRegistration3?.trim(),
    )
    const removeEmpty = currentRowAlias.filter(Boolean)

    //check duplicate alias on another vehicles
    const duplicatedAlias = vehicleListById.filter(
      (item) =>
        item[0] !== watchVehiceId &&
        item[1].some((r: string) => removeEmpty.includes(r.trim())),
    )

    if (
      duplicatedAlias.length > 0 ||
      removeEmpty.length !== new Set(removeEmpty).size
    ) {
      doAddToast('error', 'Duplicated vehicle', 3000)
      return
    }

    if (!watchIsCreate) {
      updateVehicle(getValues())
    } else {
      createVehicle(getValues())
      fetchVehicleMapping()
    }

    reset(initialFormValues)
  }
  const handleResetItem = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    row: VehicleMappingType,
  ) => {
    e.preventDefault()
    e.stopPropagation()
    const arrToMutate = [...vehicleList]

    if (row.isCreate) {
      arrToMutate.splice(0, 1)
      reset(initialFormValues)
      setVehicleList(arrToMutate)
      return
    }
    reset(initialFormValues)
  }

  const handleCreateItem = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement, MouseEvent>,
  ) => {
    e.preventDefault()
    e.stopPropagation()

    const arrToMutate = [...vehicleList]

    // Check if it is already creating one line
    const createChecker = arrToMutate.filter((o) => Boolean(o.isCreate))
    if (createChecker.length > 0) {
      ctToast.fire(
        'error',
        ctIntl.formatMessage({ id: 'mifleet.settings.save.form.first' }),
      )
      return
    }

    const defaultObj = {
      vehicle_id: '',
      mapped_registration_1: '',
      mapped_registration_2: '',
      mapped_registration_3: '',
      plate: '',
      isCreate: true,
      flag: true,
      id: '',
    }
    arrToMutate.unshift(defaultObj)
    reset(defaultObj)
    setVehicleList(arrToMutate)
  }

  const columns = useMemo(
    (): Array<GridColDef<VehicleMappingType & { flag?: boolean }>> => [
      {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        field: 'plate',
        flex: 1,
        minWidth: 200,
        valueGetter: (_, row) =>
          row.plate
            ? row.plate
            : (vehicles.find(
                (v: vehicleTypes) => v.vehicle_id.toString() === row.vehicle_id,
              )?.plate ?? ''),
        renderCell: ({ row }) => renderDropdownCell(row, 'vehicle_id', true),
      },
      {
        headerName: ctIntl.formatMessage({ id: 'First Alternate Registration' }),
        field: 'mapped_registration_1',
        flex: 1,
        minWidth: 200,
        valueGetter: (_, row) => row.mapped_registration_1,
        renderCell: ({ row }) => renderInputCell(row, 'mapped_registration_1', true),
      },
      {
        headerName: ctIntl.formatMessage({ id: 'Second Alternate Registration' }),
        field: 'mapped_registration_2',
        flex: 1,
        minWidth: 200,
        valueGetter: (_, row) => row.mapped_registration_2,
        renderCell: ({ row }) => renderInputCell(row, 'mapped_registration_2'),
      },
      {
        headerName: ctIntl.formatMessage({ id: 'Third Alternate Registration' }),
        field: 'mapped_registration_3',
        flex: 1,
        minWidth: 200,
        valueGetter: (_, row) => row.mapped_registration_3,
        renderCell: ({ row }) => renderInputCell(row, 'mapped_registration_3'),
      },
      {
        field: 'actions',
        type: 'actions',
        width: 100,
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => {
          const isInEditMode = watchFlag && watchId === row.vehicle_id
          return isInEditMode
            ? [
                <GridActionsCellItem
                  key="saveEdit"
                  icon={<CheckOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Update' })}
                  onClick={handleSaveItem}
                  disabled={!isValid}
                  color="success"
                />,
                <GridActionsCellItem
                  key="cancelEdit"
                  icon={<CloseOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Close' })}
                  onClick={(e) => handleResetItem(e, row)}
                  color="error"
                />,
              ]
            : [
                <GridActionsCellItem
                  key="edit"
                  icon={<ModeEditOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Edit' })}
                  onClick={(e) => handleEditItem(e, row)}
                />,
                <GridActionsCellItem
                  key="delete"
                  icon={<DeleteOutlineOutlinedIcon />}
                  label={ctIntl.formatMessage({ id: 'Delete' })}
                  onClick={(
                    e: React.MouseEvent<
                      HTMLButtonElement | HTMLAnchorElement,
                      MouseEvent
                    >,
                  ) => handleToggleModal(e, row)}
                />,
              ]
        },
      },
    ],
    // eslint-disable-next-line react-hooks/react-compiler, react-hooks/exhaustive-deps
    [
      handleEditItem,
      handleResetItem,
      handleSaveItem,
      handleToggleModal,
      isValid,
      renderDropdownCell,
      watchVehiceId,
    ],
  )

  const makeTableData = useMemo(() => {
    const items: Array<VehicleMappingType> = vehicleList

    const searchFilters: Filters<VehicleMappingType> = {
      search: [
        (u) => u.plate,
        (u) => u.mapped_registration_1?.toString(),
        (u) => u.mapped_registration_2?.toString(),
        (u) => u.mapped_registration_3?.toString(),
      ],
    }
    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )
    return items.filter((category) =>
      itemMatchesWithTextAndFilters(category, searchFilters),
    )
  }, [searchProps.value, vehicleList])

  return (
    <MifleetContainer
      title="Vehicle Mapping"
      isSubComponent={true}
    >
      <Box sx={{ height: '100%', pt: '12px' }}>
        {isModalOpen && (
          <DeleteSettingsDialog
            onClose={() => handleModalResult(false)}
            onDelete={() => handleModalResult(true)}
            labels={{
              titleLabel: 'Vehicle Mapping',
              specificName: selectedVehicle?.plate,
            }}
          />
        )}
        <UserDataGridWithSavedSettingsOnIDB
          sx={{
            '.MuiDataGrid-row.Mui-selected': {
              backgroundColor: '#f4773514',
              '.MuiDataGrid-cell': {
                alignItems: () =>
                  errorsLength > 0 ? 'flex-start !important' : 'inherit',
                paddingTop: '8px',
              },
            },
          }}
          getRowHeight={useCallbackBranded(
            ({ id }: GridRowHeightParams<VehicleMappingType>) => {
              if (errorsLength > 0 && id === watchId) {
                return 75
              }
              return null
            },
            [errorsLength, watchId],
          )}
          Component={DataGrid}
          dataGridId="fuelValidationSetting"
          loading={!makeTableData}
          pagination
          rows={makeTableData}
          getRowId={useCallbackBranded((row: VehicleMappingType) => row.vehicle_id, [])}
          rowSelectionModel={watchFlag ? watchId : []}
          columns={columns}
          slots={{
            toolbar: KarooToolbar,
            loadingOverlay: LinearProgress,
            pagination: CustomPagination,
          }}
          slotProps={{
            filterPanel: {
              sx: {
                '.MuiNativeSelect-select': {
                  paddingLeft: `${spacing[1]}`,
                },
              },
              columnsSort: 'asc',
            },
            toolbar: KarooToolbar.createProps({
              slots: {
                searchFilter: { show: true },
                settingsButton: { show: true },
                filterButton: { show: true },
              },
              extraContent: {
                right: (
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    size="small"
                    onClick={handleCreateItem}
                  >
                    {ctIntl.formatMessage({ id: 'Add Mapping' })}
                  </Button>
                ),
              },
            }),
          }}
        />
      </Box>
    </MifleetContainer>
  )
}
const mapStateToProps = (state: AppState) => ({
  list: state.vehicleMapping.list,
  arrayTypes: getArrayTypes(state),
})
const actionCreators = {
  fetchVehicleMapping: reducerActions.fetchVehicleMapping,
  deletevehicleMapping: reducerActions.deleteVehicleMapping,
  fetchArrayTypes: fetchDocumentArrayTypes,
  updateVehicle: reducerActions.updateVehicleMapping,
  createVehicle: reducerActions.createVehicleMapping,
  doAddToast: addToast,
}

export default withRouter(connect(mapStateToProps, actionCreators)(VehicleMapping))
