import { useEffect, useMemo, useState } from 'react'
import { isEmpty } from 'lodash'
import {
  Autocomplete,
  Button,
  DatePicker,
  DialogContent,
  DialogTitle,
  MenuItem,
  Stack,
  styled,
  TextField,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import CheckIcon from '@mui/icons-material/Check'
import ClearIcon from '@mui/icons-material/Clear'
import CreateIcon from '@mui/icons-material/Create'
import { DateTime } from 'luxon'
import { Controller, useForm, useWatch } from 'react-hook-form'
import type { Except } from 'type-fest'
import { z } from 'zod/v4'

import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { getArrayTypes } from 'src/modules/mifleet/DocumentsEdit/slice'
import { useTypedSelector } from 'src/redux-hooks'
import { messages } from 'src/shared/forms/messages'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'
import { isTrue } from 'src/util-functions/validation'
import { createZodObjPathGetter } from 'src/util-functions/zod-utils'

export const distanceInterval = [
  { name: 'Kilometers', value: 'km' },
  { name: 'Miles', value: 'mi' },
  { name: 'Hours', value: 'hrs' },
]

const zodSchema = z
  .object({
    vehicle_id: z.string().min(1, { message: messages.required }),
    vehicle_service_type_id: z.string().min(1, { message: messages.required }),
    expiration_date: z.string().nullable(),
    mileage_period: z.number({ error: messages.validNumber }).nullable(),
    service_date_interval: z.number({ error: messages.validNumber }).nullable(),
    timeInterval: z.string().nullable(),
    service_interval: z
      .string()
      .or(z.number({ error: messages.validNumber }))
      .nullable(),
    service_interval_unit: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    const { createPath } = createZodObjPathGetter(data)
    if (!data.mileage_period && !data.expiration_date) {
      ctx.addIssue({
        code: 'custom',
        path: createPath(['expiration_date']),
        message:
          'You need to provide at least one `expiration date` or `mileage period`',
      })
      ctx.addIssue({
        code: 'custom',
        path: createPath(['mileage_period']),
        message:
          'You need to provide at least one `expiration date` or `mileage period`',
      })
    } else {
      if (data.expiration_date && !data.service_date_interval) {
        ctx.addIssue({
          code: 'custom',
          path: createPath(['service_date_interval']),
          message: messages.required,
        })
      }
      if (data.expiration_date && !data.timeInterval) {
        ctx.addIssue({
          code: 'custom',
          path: createPath(['timeInterval']),
          message: messages.required,
        })
      }
      if (data.mileage_period && !data.service_interval)
        ctx.addIssue({
          code: 'custom',
          path: createPath(['service_interval']),
          message: messages.required,
        })
      if (data.mileage_period && !data.service_interval_unit)
        ctx.addIssue({
          code: 'custom',
          path: createPath(['service_interval_unit']),
          message: messages.required,
        })
    }
  })

type ReminderServiceForm = z.infer<typeof zodSchema>

const timeInterval = [
  { name: 'Months', value: 'months' },
  { name: 'Days', value: 'days' },
  { name: 'Weeks', value: 'weeks' },
]

type vehicleTypes = {
  driver_id: number | null
  is_deleted: boolean | string
  name: string
  plate: string
  value: string
  vehicle_id: number
}

const ReminderServiceModal = ({
  selectedService,
  onSubmit,
}: {
  selectedService:
    | (ServiceReminder & {
        dateIntervalFirst?: string | null
        dateIntervalSecond?: string
      })
    | null
  onSubmit: (props: {
    data: Except<ReminderServiceForm, 'service_date_interval'> & {
      service_date_interval: string | null
    }
    onSuccess?: () => void
  }) => void
}) => {
  const { vehicles } = useTypedSelector(getArrayTypes)
  const serviceTask = useTypedSelector((state) => state.overview.serviceTask)
  const isLoadingUpdates = useTypedSelector(
    (state) => state.overview.isReminderUpdatesLoading,
  )

  const [isEditing, setIsEditing] = useState(false)
  const [serviceTaskList, setServiceTaskList] = useState<Array<ServiceTask>>()
  const actionType = selectedService ? 'update' : 'create'

  const initialFormValues = selectedService
    ? {
        vehicle_id: selectedService.vehicle_id?.toString(),
        vehicle_service_type_id: selectedService.vehicle_service_type_id?.toString(),
        expiration_date: selectedService.expiration_date,
        service_date_interval: Number(selectedService.dateIntervalFirst),
        mileage_period: selectedService.mileage_period,
        service_interval: selectedService.service_interval,
        timeInterval: selectedService.dateIntervalSecond
          ? selectedService.dateIntervalSecond
          : '',
        service_interval_unit: selectedService.service_interval_unit,
      }
    : {
        vehicle_id: '',
        vehicle_service_type_id: '',
        expiration_date: null,
        mileage_period: null,
        service_date_interval: null,
        timeInterval: '',
        service_interval: null,
        service_interval_unit: '',
      }

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setValue: setFormValue,
  } = useForm<ReminderServiceForm>({
    resolver: zodResolverV4(zodSchema),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: initialFormValues,
  })

  const watched_mileage_period = useWatch({ name: 'mileage_period', control })
  const watched_expiration_date = useWatch({ name: 'expiration_date', control })

  useEffect(() => {
    if (!isEmpty(serviceTask)) {
      setServiceTaskList(
        serviceTask.map((p) => ({
          ...p,
          name: p.service_type,
          value: p.vehicle_service_type_id,
        })),
      )
    }
  }, [serviceTask])

  const submitForm = handleSubmit((values) => {
    const validValues = {
      ...values,
      expiration_date: values.expiration_date
        ? DateTime.fromSQL(values.expiration_date).toFormat('D')
        : null,
      service_date_interval: values.service_date_interval
        ? `${values.service_date_interval} ${values.timeInterval}`
        : null,
    }

    onSubmit({
      data: validValues,
      onSuccess: () => {
        // only reset the default values with updated value when updating successfully coz the modal is not closed
        if (selectedService) {
          setIsEditing(false)
          reset(values)
        }
      },
    })
  })

  const onChangeNumberInput = (
    _newValue: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    key: 'service_date_interval' | 'service_interval' | 'mileage_period',
  ) => {
    const newValue = _newValue.target.value
    setFormValue(key, Number(newValue) ? Number(newValue) : newValue || null, {
      shouldValidate: true,
    })
  }

  const memoVehiclesOptions = useMemo(() => {
    const array: Array<FixMeAny> = []
    const byId = new Map<string, FixMeAny>()

    if (vehicles && !isEmpty(vehicles)) {
      let localVehicles = []

      const currentDeletedItem = vehicles.find(
        (item: vehicleTypes) =>
          isTrue(item.is_deleted) && item.vehicle_id === selectedService?.vehicle_id,
      )

      const activeItems = vehicles.filter(
        (item: { is_deleted: string | boolean }) => !isTrue(item.is_deleted),
      )

      if (currentDeletedItem) {
        localVehicles = [...activeItems, currentDeletedItem]
      } else {
        localVehicles = activeItems
      }

      // eslint-disable-next-line
      // eslint-disable-next-line unicorn/no-array-for-each
      localVehicles.forEach((i: { value: string; name: string }) => {
        const option = { id: i.value, label: i.name }
        array.push(option)
        byId.set(option.id, option)
      })
    }

    return {
      array: array.sort((a, b) => a.label.localeCompare(b.label)),
      byId,
    }
  }, [selectedService?.vehicle_id, vehicles])

  const toggleEditing = () => {
    setIsEditing((v) => !v)
    if (isEditing) {
      reset()
    }
  }

  return (
    <>
      <DialogTitle>
        {actionType === 'create'
          ? ctIntl.formatMessage({ id: 'New Service Reminder' })
          : ''}
      </DialogTitle>
      <form onSubmit={submitForm}>
        <DialogContent>
          <Stack
            sx={{
              height: '100%',
              justifyContent: 'space-around',
            }}
            direction="row"
            spacing={2}
          >
            <Stack sx={{ gap: 3, width: '100%' }}>
              <Controller
                control={control}
                name="vehicle_id"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    size="small"
                    {...getAutocompleteVirtualizedProps({
                      options: memoVehiclesOptions.array,
                    })}
                    onChange={(_, newValue) =>
                      setFormValue(field.name, newValue ? newValue.id : null, {
                        shouldValidate: false,
                      })
                    }
                    value={
                      field.value
                        ? (memoVehiclesOptions.byId.get(field.value as string) ?? null)
                        : null
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        required
                        label={ctIntl.formatMessage({ id: 'Vehicle' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                      />
                    )}
                    disabled={!isEditing && actionType === 'update'}
                  />
                )}
              />
              <Controller
                control={control}
                name="expiration_date"
                render={({ field }) => (
                  <DatePicker
                    label={ctIntl.formatMessage({ id: 'Expiration Date' })}
                    value={field.value ? DateTime.fromSQL(field.value) : null}
                    onChange={(newValue) =>
                      setFormValue(
                        'expiration_date',
                        newValue && newValue.isValid ? newValue.toISODate() : null,
                        { shouldValidate: false },
                      )
                    }
                    disabled={!isEditing && actionType === 'update'}
                    slotProps={{
                      textField: {
                        required: Boolean(!watched_mileage_period),
                      },
                    }}
                  />
                )}
              />
              <TextFieldControlled
                ControllerProps={{ name: 'service_date_interval', control }}
                onChange={(_newValue) =>
                  onChangeNumberInput(_newValue, 'service_date_interval')
                }
                required={Boolean(watched_expiration_date)}
                label={ctIntl.formatMessage({ id: 'Service Date Interval' })}
                disabled={!isEditing && actionType === 'update'}
                helperText={
                  watched_expiration_date
                    ? ctIntl.formatMessage({
                        id: (errors.service_date_interval?.message as string) ?? '',
                      })
                    : ' '
                }
                error={errors.service_date_interval && Boolean(watched_expiration_date)}
              />
              <TextFieldControlled
                ControllerProps={{ name: 'service_interval', control }}
                onChange={(_newValue) =>
                  onChangeNumberInput(_newValue, 'service_interval')
                }
                label={ctIntl.formatMessage({ id: 'Service Interval' })}
                required={Boolean(watched_mileage_period)}
                disabled={!isEditing && actionType === 'update'}
                helperText={
                  watched_mileage_period
                    ? ctIntl.formatMessage({
                        id: (errors.service_interval?.message as string) ?? '',
                      })
                    : ' '
                }
                error={errors.service_interval && Boolean(watched_mileage_period)}
              />
            </Stack>

            <Stack sx={{ gap: 3, width: '100%' }}>
              {serviceTaskList && (
                <TextFieldControlled
                  select
                  required
                  ControllerProps={{ name: 'vehicle_service_type_id', control }}
                  label={ctIntl.formatMessage({ id: 'Service Task' })}
                  disabled={!isEditing && actionType === 'update'}
                >
                  {serviceTaskList.map((item) => (
                    <MenuItem
                      value={item.value}
                      key={item.value}
                    >
                      {item.name}
                    </MenuItem>
                  ))}
                </TextFieldControlled>
              )}
              <TextFieldControlled
                ControllerProps={{ name: 'mileage_period', control }}
                required={Boolean(!watched_expiration_date)}
                onChange={(_newValue) =>
                  onChangeNumberInput(_newValue, 'mileage_period')
                }
                label={ctIntl.formatMessage({ id: 'Mileage Period' })}
                disabled={!isEditing && actionType === 'update'}
              />
              <TextFieldControlled
                select
                ControllerProps={{ name: 'timeInterval', control }}
                required={Boolean(watched_expiration_date)}
                label={ctIntl.formatMessage({ id: 'Months' })}
                disabled={!isEditing && actionType === 'update'}
                helperText={
                  watched_expiration_date
                    ? ctIntl.formatMessage({
                        id: (errors.timeInterval?.message as string) ?? '',
                      })
                    : ' '
                }
                error={errors.timeInterval && Boolean(watched_expiration_date)}
              >
                {timeInterval.map((item) => (
                  <MenuItem
                    value={item.value}
                    key={item.value}
                  >
                    {ctIntl.formatMessage({ id: item.name })}
                  </MenuItem>
                ))}
              </TextFieldControlled>
              <TextFieldControlled
                select
                ControllerProps={{ name: 'service_interval_unit', control }}
                required={Boolean(watched_mileage_period)}
                label={ctIntl.formatMessage({ id: 'Kilometers' })}
                disabled={!isEditing && actionType === 'update'}
                helperText={
                  watched_mileage_period
                    ? ctIntl.formatMessage({
                        id: (errors.service_interval_unit?.message as string) ?? '',
                      })
                    : ' '
                }
                error={errors.service_interval_unit && Boolean(watched_mileage_period)}
              >
                {distanceInterval.map((item) => (
                  <MenuItem
                    value={item.value}
                    key={item.value}
                  >
                    {ctIntl.formatMessage({ id: item.name })}
                  </MenuItem>
                ))}
              </TextFieldControlled>

              {actionType === 'create' && (
                <Button
                  color="primary"
                  variant="contained"
                  type="submit"
                  disabled={!isValid}
                >
                  {ctIntl.formatMessage({ id: 'Save' })}
                </Button>
              )}
            </Stack>
            <Stack>
              {actionType === 'update' ? (
                <>
                  {isEditing ? (
                    <Stack
                      direction="column"
                      sx={{ gap: 2 }}
                    >
                      <StyleEditBtn
                        startIcon={<ClearIcon />}
                        variant="outlined"
                        onClick={toggleEditing}
                      />
                      <Button
                        loading={isLoadingUpdates}
                        type="submit"
                        variant="outlined"
                        disabled={!isValid}
                        sx={{ color: '#5cae60' }}
                      >
                        <CheckIcon />
                      </Button>
                    </Stack>
                  ) : (
                    <StyleEditBtn
                      startIcon={<CreateIcon />}
                      onClick={toggleEditing}
                      variant="outlined"
                    />
                  )}
                </>
              ) : (
                ''
              )}
            </Stack>
          </Stack>
        </DialogContent>
      </form>
    </>
  )
}

export default ReminderServiceModal

const StyleEditBtn = styled(Button)({
  '&:hover': {
    backgroundColor: 'transparent',
  },
  minWidth: '42px',
  padding: 'auto 0',
  color: '#666',
  '.MuiButton-startIcon': {
    margin: '0 !important',
  },
})
