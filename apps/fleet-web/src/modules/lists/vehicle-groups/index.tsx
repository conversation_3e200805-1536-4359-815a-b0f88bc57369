import { useEffect, useMemo, useState } from 'react'
import { isNil } from 'lodash'
import {
  Button,
  DataGrid,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  LinearProgress,
  OverflowTypography,
  Skeleton,
  Stack,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
  type GridColDef,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import { rgba } from 'polished'
import { useForm, useWatch } from 'react-hook-form'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'
import { z } from 'zod/v4'

import {
  getSettings_UNSAFE,
  getUserPositionAddressStateGetter,
  getVehicleDetailsShowTerminalSerial,
  getVehicleGroupLabel,
  getVehiclesImportVehiclesToGroup,
} from 'duxs/user-sensitive-selectors'
import {
  getVehicles,
  getVehiclesQueryIsFetching,
  type ReduxVehicleGroups,
  type ReduxVehicles,
} from 'duxs/vehicles'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { useModal } from 'src/hooks'
import { getVehicleDetailsModalMainPath } from 'src/modules/app/GlobalModals/VehicleDetails/utils'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { UserFormattedPositionAddress } from 'src/modules/components/connected/UserFormattedPositionAddress'
import {
  Actions,
  ActionsContainer,
} from 'src/modules/map-view/FleetMapView/shared/utils'
import { useTypedSelector } from 'src/redux-hooks'
import { variables } from 'src/shared/components/styled/global-styles'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { messages } from 'src/shared/forms/messages'
import { ctIntl } from 'src/util-components/ctIntl'
import Lookup from 'src/util-components/lookup'
import { toMutable } from 'src/util-functions/functional-utils'

import { StarRating, Stats } from 'cartrack-ui-kit'
import { DialogWrap } from '../Drivers/utils'
import { useVehiclesCurrentGeofencesQuery } from '../useVehiclesCurrentGeofences'
import { useVehicleGroupActions } from './useVehicleGroupActionsMutation'

type Props = {
  onClose: () => void
  editingGroup?: ReduxVehicleGroups[number]
}

const addOrEditGroupSchema = z.object({
  groupName: z
    .string()
    .min(1, messages.required)
    .max(250, messages.numberInputMaxLength),
  vehiclesIds: z.array(z.string()),
})

type AddOrEditGroupSchema = z.infer<typeof addOrEditGroupSchema>

const CreateEditVehicleGroupModal = ({ onClose, editingGroup }: Props) => {
  const columnHelper = useDataGridColumnHelper<ReduxVehicles[number]>({
    filterMode: 'client',
  })

  const [localEditing, setLocalEditing] = useState(false)
  const [isConfirmationOpen, confirmationModal] = useModal(false)

  const getUserPositionAddressState = useTypedSelector(
    getUserPositionAddressStateGetter,
  )
  const vehicles = useTypedSelector(getVehicles)
  const vehiclesQueryIsFetching = useTypedSelector(getVehiclesQueryIsFetching)

  const groupRemoteActions = useVehicleGroupActions()
  const { groupLabel } = useTypedSelector(getVehicleGroupLabel)
  const vehicleDetailsShowTerminalSerial = useTypedSelector(
    getVehicleDetailsShowTerminalSerial,
  )
  const vehiclesImportVehiclesToGroup = useTypedSelector(
    getVehiclesImportVehiclesToGroup,
  )
  const { isAdmin } = useTypedSelector(getSettings_UNSAFE)

  const history = useHistory()

  const editingVehiclesOfGroup = localEditing && vehiclesImportVehiclesToGroup

  const memoVehicles = useMemo(
    () =>
      editingVehiclesOfGroup
        ? vehicles
        : vehicles.filter((v: ReduxVehicles[number]) =>
            editingGroup?.itemIds.includes(v.id),
          ),
    [vehicles, editingGroup?.itemIds, editingVehiclesOfGroup],
  )

  const vehicleIds = useMemo(() => memoVehicles.map((v) => v.id), [memoVehicles])

  const vehiclesCurrentGeofences = useVehiclesCurrentGeofencesQuery({
    vehicleIds,
  })

  const permissions = useMemo(
    () =>
      editingGroup && !isAdmin
        ? editingGroup.permissions
        : {
            view: true,
            edit: true,
            remove: true,
          },
    [editingGroup, isAdmin],
  )

  const defaultFormValues = useMemo(() => {
    if (editingGroup) {
      return {
        groupName: editingGroup.name,
        vehiclesIds: editingGroup.itemIds,
      }
    }

    return {
      groupName: '',
      vehiclesIds: [],
    }
  }, [editingGroup])

  const {
    control,
    handleSubmit,
    formState: { isValid, isDirty },
    setValue,
  } = useForm<AddOrEditGroupSchema>({
    resolver: zodResolverV4(addOrEditGroupSchema),
    mode: 'all',
    defaultValues: toMutable(defaultFormValues),
  })

  const watchedVehiclesIds = useWatch({ name: 'vehiclesIds', control })

  useEffect(() => {
    if (editingGroup) {
      setLocalEditing(false)
    } else {
      setLocalEditing(true)
    }
  }, [editingGroup])

  const onLocalClose = () => {
    setLocalEditing(false)
    onClose()
  }

  const onSubmit = handleSubmit((_values) => {
    groupRemoteActions.mutate(
      {
        data: {
          name: _values.groupName,
          group_vehicle_id: editingGroup ? Number(editingGroup.id) : undefined,
          vehicle_ids: _values.vehiclesIds,
        },
        operation: editingGroup ? 'update' : 'create',
      },
      {
        onSuccess: () => {
          onLocalClose()
        },
      },
    )
  })

  const handleSelectionModelChange = (selectionModal: GridRowSelectionModel) => {
    setValue('vehiclesIds', selectionModal as Array<string>, {
      shouldValidate: true,
      shouldDirty: true,
    })
  }

  const vehicleColumns = useMemo(() => {
    const columns: Array<GridColDef<ReduxVehicles[number]>> = [
      { ...GRID_CHECKBOX_SELECTION_COL_DEF, hideable: false },
      columnHelper.string((_, row) => row.name, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Name' }),
        field: 'name',
        flex: 1,
        width: 300,
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={0.5}
          >
            <Typography>{row.name}</Typography>
            {!row.active && (
              <Typography
                sx={(theme) => ({
                  color: theme.palette.text.secondary,
                })}
              >
                {ctIntl.formatMessage({ id: '(Discommisioned)' })}
              </Typography>
            )}
          </Stack>
        ),
      }),
      columnHelper.string((_, row) => row.registration, {
        headerName: ctIntl.formatMessage({ id: 'Registration' }),
        field: 'registration',
        flex: 1,
        maxWidth: 300,
      }),
      columnHelper.valueGetter(
        (_, row) => ('driverName' in row ? row.driverName : undefined),
        {
          headerName: ctIntl.formatMessage({ id: 'Driver' }),
          field: 'driverName',
          renderCell: ({ row }) => {
            const driverName = row.driverName

            if (driverName.status === 'UNDISCLOSED') {
              return ctIntl.formatMessage({
                id: 'vehicle.driverName.undisclosed',
              })
            }

            const isInvalidDriverName = isNil(driverName.name) || !driverName.name

            return (
              <Typography
                sx={(theme) => ({
                  color: isInvalidDriverName
                    ? theme.palette.text.secondary
                    : theme.palette.text.primary,
                })}
              >
                {isInvalidDriverName
                  ? ctIntl.formatMessage({ id: 'Unassigned' })
                  : driverName.name}
              </Typography>
            )
          },
          flex: 1,
          maxWidth: 300,
          minWidth: 150,
        },
      ),
      columnHelper.string((_, row) => row.description, {
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        field: 'description',
        flex: 1,
      }),
      columnHelper.number((_, row) => row.rating, {
        headerName: ctIntl.formatMessage({ id: 'Score' }),
        field: 'rating',
        renderCell: ({ row }) => (
          <StarRating
            color={'#FFB400'}
            rating={row.rating}
          />
        ),
        flex: 1,
        maxWidth: 100,
      }),
      columnHelper.string((_, row) => row.homeGeofence, {
        headerName: ctIntl.formatMessage({ id: 'Home Geofence' }),
        field: 'homeGeofence',
        renderCell: ({ row }) => (
          <Lookup
            type="geofence"
            property="name"
            id={row.homeGeofence}
          />
        ),
        flex: 1,
        maxWidth: 200,
      }),
      columnHelper.valueGetter(
        (_, row) =>
          match(vehiclesCurrentGeofences)
            .with({ status: 'pending' }, () => null)
            .with({ status: 'error' }, () => null)
            .with({ status: 'success' }, ({ data }) => data[row.id])
            .exhaustive(),
        {
          headerName: ctIntl.formatMessage({ id: 'Current Geofence' }),
          field: 'currentGeofence',
          renderCell: ({ row }) =>
            match(vehiclesCurrentGeofences)
              .with({ status: 'pending' }, () => (
                <Skeleton
                  variant="text"
                  sx={{ width: '100%', fontSize: '1rem' }}
                />
              ))
              .with({ status: 'error' }, () => null)
              .with({ status: 'success' }, ({ data }) => data[row.id])
              .exhaustive(),
          flex: 1,
          minWidth: 200,
        },
      ),
      columnHelper.string((_, row) => row.VIN, {
        headerName: ctIntl.formatMessage({ id: 'VIN' }),
        field: 'VIN',
        flex: 1,
      }),
      columnHelper.string(
        (_, row) =>
          match(
            getUserPositionAddressState({
              address: row.positionDescription,
              gpsFixType: row.gpsFixType,
            }),
          )
            .with('EMPTY', () => '')
            .with({ visibility: 'PRIVATE' }, () =>
              ctIntl.formatMessage({ id: 'Privacy Enabled' }),
            )
            .with(
              { visibility: 'PUBLIC' },
              ({ processedDescriptionText }) => processedDescriptionText,
            )
            .exhaustive(),
        {
          headerName: ctIntl.formatMessage({ id: 'Location' }),
          field: 'positionDescription',
          renderCell: ({ row }) => (
            <UserFormattedPositionAddress
              address={row.positionDescription}
              gpsFixType={row.gpsFixType}
              statesRenderer={{
                publicAddress: ({ processedJSX, hasWarning }) => (
                  <OverflowTypography
                    typographyProps={{
                      variant: 'body2',
                      sx: hasWarning
                        ? {
                            '& .MuiTypography-root': {
                              color: variables.red, // for consistency with warning color in UserFormattedPositionAddress
                            },
                            color: variables.red,
                          }
                        : {},
                    }}
                  >
                    {processedJSX}
                  </OverflowTypography>
                ),
              }}
            />
          ),
          flex: 1,
          minWidth: 300,
        },
      ),
    ]

    if (vehicleDetailsShowTerminalSerial) {
      columns.push(
        columnHelper.string((_, row) => row.terminalSerial, {
          headerName: ctIntl.formatMessage({ id: 'Terminal Serial' }),
          field: 'terminalSerial',
          flex: 1,
        }),
      )
    }

    return columns
  }, [
    columnHelper,
    getUserPositionAddressState,
    vehicleDetailsShowTerminalSerial,
    vehiclesCurrentGeofences,
  ])

  const handleDeleteConfirm = () => {
    if (editingGroup) {
      groupRemoteActions.mutate({
        data: { group_ids: [Number(editingGroup.id)] },
        operation: 'delete',
      })
    }
    onLocalClose()
  }

  const isFormElementsVisible = (localEditing && editingGroup) || !editingGroup

  const getDialogTitle = () => {
    if (editingGroup) {
      return isFormElementsVisible
        ? ctIntl.formatMessage({ id: `Edit ${groupLabel}` })
        : editingGroup.name
    } else {
      return ctIntl.formatMessage({ id: `Add New ${groupLabel}` })
    }
  }

  return (
    <>
      {isConfirmationOpen && (
        <ConfirmationModal
          open
          title="settings.company.deleteUnitHeader"
          onClose={confirmationModal.close}
          onConfirm={handleDeleteConfirm}
        >
          {ctIntl.formatMessage({
            id: `Are you sure you want to delete this Vehicle ${groupLabel}?`,
          })}
        </ConfirmationModal>
      )}
      <DialogWrap
        title={getDialogTitle()}
        closeDialog={onLocalClose}
        {...(editingGroup && !localEditing
          ? {
              actionButtons: (
                <>
                  <Button
                    size="small"
                    startIcon={<DeleteOutlinedIcon />}
                    variant="outlined"
                    color="error"
                    onClick={() => confirmationModal.open()}
                    disabled={!permissions.remove}
                  >
                    {ctIntl.formatMessage({ id: `Delete ${groupLabel}` })}
                  </Button>
                  <Button
                    size="small"
                    startIcon={<EditOutlinedIcon />}
                    variant="outlined"
                    onClick={() => setLocalEditing(true)}
                    disabled={!permissions.edit}
                  >
                    {ctIntl.formatMessage({ id: `Edit ${groupLabel}` })}
                  </Button>
                </>
              ),
            }
          : {})}
      >
        {isFormElementsVisible && (
          <Stack
            gap={3}
            sx={{ px: 3 }}
          >
            <TextFieldControlled
              ControllerProps={{ control, name: 'groupName' }}
              sx={{
                width: '260px',
                '& .MuiFormHelperText-root.Mui-error': {
                  position: 'absolute',
                  top: '100%',
                },
              }}
              disabled={!localEditing}
              required
              label={ctIntl.formatMessage({ id: `Enter ${groupLabel} Name` })}
              data-testid="AddGroupModal-NameInput"
            />

            <Typography variant="body2">
              {ctIntl.formatMessage({
                id: editingGroup
                  ? 'Add or remove vehicles from the list below'
                  : `Select vehicles to add to ${groupLabel.toLowerCase()}`,
              })}
            </Typography>
          </Stack>
        )}
        <UserDataGridWithSavedSettingsOnIDB
          Component={DataGrid}
          dataGridId="sc-add-vehicles-groups"
          getRowId={useCallbackBranded((row: ReduxVehicles[number]) => row.id, [])}
          pagination
          pageSizeOptions={[25, 50, 100]}
          rows={memoVehicles}
          columns={vehicleColumns}
          loading={vehiclesQueryIsFetching}
          checkboxSelection={editingVehiclesOfGroup}
          disableRowSelectionOnClick={!editingVehiclesOfGroup}
          onRowSelectionModelChange={handleSelectionModelChange}
          // When searching for a non existing vehicle on the quick filter (aka - search input)
          // no rows are displayed and mui-x-data-grid deselects all the selected rows.
          // This prop keeps the selected rows when this happens
          keepNonExistentRowsSelected
          initialState={{
            pagination: {
              paginationModel: { pageSize: 25, page: 0 },
            },
          }}
          onRowClick={({ row }) => {
            if (!editingVehiclesOfGroup) {
              history.push(getVehicleDetailsModalMainPath(history.location, row.id))
            }
          }}
          rowSelectionModel={watchedVehiclesIds}
          RootPaperProps={{ elevation: 0 }}
          localeText={{
            footerRowSelected: (count) =>
              isFormElementsVisible && editingVehiclesOfGroup
                ? ctIntl.formatMessage(
                    { id: 'list.vehicles.groups.selection' },
                    { values: { count } },
                  )
                : '',
          }}
          slots={{
            toolbar: KarooToolbar,
            loadingOverlay: LinearProgress,
            noRowsOverlay: () => (
              <DataStatePlaceholder
                label={`No vehicles in this ${groupLabel.toLowerCase()}`}
              />
            ),
          }}
          slotProps={{
            toolbar: KarooToolbar.createProps({
              slots: {
                searchFilter: { show: true },
                settingsButton: { show: true },
              },
              extraContent: {
                middle: (
                  <Stats
                    reversed
                    data={[
                      {
                        key: `Vehicles in ${groupLabel.toLowerCase()}`,
                        value: watchedVehiclesIds.length,
                      },
                    ]}
                  />
                ),
              },
            }),
            pagination: { showFirstButton: true, showLastButton: true },
          }}
          sx={(theme) => ({
            px: 3,
            ...(editingVehiclesOfGroup
              ? {
                  '.MuiDataGrid-row.Mui-selected': {
                    backgroundColor: rgba(
                      theme.palette.primary.main,
                      theme.palette.action.selectedOpacity,
                    ),
                    '&:hover': {
                      backgroundColor: rgba(
                        theme.palette.primary.main,
                        theme.palette.action.hoverOpacity,
                      ),
                    },
                  },
                }
              : {
                  '& .MuiDataGrid-row, & .Mui-selected': {
                    backgroundColor: 'unset !important',
                    '&:hover': {
                      backgroundColor: `${theme.palette.action.hover} !important`,
                    },
                  },
                }),
            '& .MuiDataGrid-row': {
              cursor: 'pointer',
            },
            '.MuiCheckbox-root.Mui-checked': {
              color: theme.palette.primary.main,
            },
          })}
        />
        {(localEditing || isFormElementsVisible) && (
          <ActionsContainer
            sx={{
              justifyContent: editingGroup ? 'flex-end' : 'space-between',
            }}
          >
            <Actions
              gap={1}
              sx={{ justifyContent: 'flex-end' }}
            >
              {editingGroup && localEditing && (
                <Button
                  color={'secondary'}
                  variant="outlined"
                  onClick={() =>
                    editingGroup ? setLocalEditing(false) : onLocalClose()
                  }
                >
                  {ctIntl.formatMessage({ id: 'Cancel' })}
                </Button>
              )}
              {isFormElementsVisible && (
                <Button
                  data-testid="AddGroupModal-SaveButton"
                  variant="contained"
                  type="submit"
                  disabled={!isValid || !isDirty}
                  onClick={onSubmit}
                  loading={groupRemoteActions.status === 'pending'}
                >
                  {ctIntl.formatMessage({
                    id: editingGroup ? 'Save' : `Add New ${groupLabel}`,
                  })}
                </Button>
              )}
            </Actions>
          </ActionsContainer>
        )}
      </DialogWrap>
    </>
  )
}

export default CreateEditVehicleGroupModal
