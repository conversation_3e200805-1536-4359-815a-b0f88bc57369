import { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  CircularProgressDelayedAbsolute,
  Divider,
  Drawer,
  FormHelperText,
  ListItem,
  MenuItem,
  Stack,
  TextField,
  Typography,
  type AutocompleteChangeReason,
  type AutocompleteInputChangeReason,
  type DrawerProps,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import type { Coords } from 'google-map-react'
import {
  Controller,
  useForm,
  useWatch,
  type ControllerFieldState,
  type SetValueConfig,
} from 'react-hook-form'
import { useHistory } from 'react-router'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'
import { z } from 'zod/v4'

import {
  geofenceIdSchema,
  locationIdSchema,
  vehicleIdSchema,
  type GeofenceId,
  type LocationId,
  type VehicleId,
} from 'api/types'
import {
  getGoogleMapsLanguageCodeFromUserLocale,
  getMapZoomOptions,
  getSettings_UNSAFE,
} from 'duxs/user'
import {
  getFacilitiesTranslatorFn,
  getIsRequiredMapSelectionAtSiteLocationsPage,
  type UserAvailableMapApiProvider,
} from 'duxs/user-sensitive-selectors'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { MapApiProvider } from 'src/api/user/types'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import {
  getGoogleLatLngFromGeocode,
  getGoogleMapsCachedGeocodeFirstResult,
  useUserGoogleLazyPlacesAutocomplete,
} from 'src/hooks/useUserGooglePlacesAutocomplete'
import { useValidatedSearchParams } from 'src/hooks/useValidatedSearchParams'
import { useGeofencesQuery } from 'src/modules/api/useGeofencesQuery'
import {
  useVehiclesQuery,
  useVehiclesWithCarpoolMetaQuery,
} from 'src/modules/api/useVehiclesQuery'
import { LIST } from 'src/modules/app/components/routes/list'
import AssignVehicleComponent from 'src/modules/carpool/Settings/components/AssignVehicleComponent'
import { useLocalMapApiProviderWithMapTypeId } from 'src/modules/components/connected/useLocalMapApiProviderWithUserRestrictedSetter'
import { createMapZoomConfig } from 'src/modules/shared/map-utils'
import { useTypedSelector } from 'src/redux-hooks'
import { messages } from 'src/shared/forms/messages'
import { MapTypeId } from 'src/types/extended/google-maps'
import { ctIntl } from 'src/util-components/ctIntl'
import type { FacilityTranslationTermsBanned } from 'src/util-components/intlBannedTerms'

import {
  useAddLocationMutation,
  useLocationDetails,
  useLocationsQuery,
  useLocationTypes,
  useUpdateLocationMutation,
  type FacilityFormData,
  type FetchLocationTypesResolved,
} from '../api/queries'
import { FACILITIES_SCHEMAS } from '../api/types'
import { mapLocationTypeToTranslation } from '../utils'
import { GoogleMapsApiLoadChecker } from './GoogleMapsApiLoadChecker'
import MapArea from './MapArea'
import MapOpenLayersArea from './MapOpenLayersArea'

const MAX_LENGTH = 250
// Will show required message for all types of errors
const showRequiredMessageOnAllErrorTypes = () => ({ message: messages.required })

const createSchema = ({
  isRequiredMapSelection,
}: {
  isRequiredMapSelection: boolean
}) =>
  z.object({
    typeId: FACILITIES_SCHEMAS.locationTypeId.withParams({
      error: showRequiredMessageOnAllErrorTypes,
    }),
    name: z
      .string()
      .min(1, messages.required)
      .max(MAX_LENGTH, messages.numberInputMaxLength),
    addressCoords: isRequiredMapSelection
      ? z.object(
          { lat: z.number(), lng: z.number() },
          { error: showRequiredMessageOnAllErrorTypes },
        )
      : z
          .object(
            { lat: z.number(), lng: z.number() },
            { error: showRequiredMessageOnAllErrorTypes },
          )
          .nullable(),
    address: isRequiredMapSelection
      ? z.string().max(MAX_LENGTH, messages.numberInputMaxLength).nullable()
      : z
          .string()
          .min(1, messages.required)
          .max(MAX_LENGTH, messages.numberInputMaxLength),
    geofenceId: geofenceIdSchema.nullable(),
    vehicleIds: vehicleIdSchema.array().nullable(),
    description: z.string().max(MAX_LENGTH, messages.numberInputMaxLength).nullable(),
  })

type ValidSchema = z.infer<ReturnType<typeof createSchema>>

type InitialValues = (
  | ValidSchema
  | {
      typeId: null
      name: ''
      description: ''
      geofenceId: null
      addressCoords: null
      vehicleIds: null
    }
) & {
  address: string | null
}

type FormPossibleValues = {
  typeId: ValidSchema['typeId'] | InitialValues['typeId']
  name: ValidSchema['name']
  description: ValidSchema['description']
  geofenceId: ValidSchema['geofenceId']
  addressCoords: ValidSchema['addressCoords'] | InitialValues['addressCoords']
  address: ValidSchema['address'] | InitialValues['address']
  vehicleIds: ValidSchema['vehicleIds']
}

export const facilityDetailsSearchParamsSchema = z.discriminatedUnion('type', [
  z.object({ type: z.literal('add') }),
  z.object({ type: z.literal('edit'), id: locationIdSchema }),
])

export function FacilityDetails() {
  const history = useHistory()
  const addFacilityMutation = useAddLocationMutation()
  const validatedParams = useValidatedSearchParams(
    () => facilityDetailsSearchParamsSchema,
  )
  const facilityTypesQuery = useLocationTypes()
  const { currentMapProvider: mapApiProviderId } = useLocalMapApiProviderWithMapTypeId({
    initialMapTypeId: MapTypeId.ROADMAP,
  })
  const usingGoogleMap = mapApiProviderId === MapApiProvider.GOOGLE

  return (
    <GoogleMapsApiLoadChecker>
      {({ googleApiLoaded }) =>
        (!usingGoogleMap || googleApiLoaded) &&
        match(validatedParams)
          .with({ status: 'invalid' }, () => null)
          .with(
            { status: 'valid', data: { type: 'add' } },
            ({ data: { type } }) =>
              facilityTypesQuery.data && (
                <DetailsDrawer
                  onClose={() => history.push(LIST.subMenusRoutes.FACILITIES.path)}
                >
                  <Facility
                    mapApiProviderId={mapApiProviderId}
                    facilityTypes={facilityTypesQuery.data}
                    handleCancelClick={() =>
                      history.push(LIST.subMenusRoutes.FACILITIES.path)
                    }
                    formParams={{
                      type,
                      onSubmit: (formData) => {
                        addFacilityMutation.mutate(
                          { formData },
                          {
                            onSuccess: () => {
                              history.push(LIST.subMenusRoutes.FACILITIES.path)
                            },
                          },
                        )
                      },
                      isSubmittingForm: addFacilityMutation.isPending,
                      initialValues: {
                        typeId: null,
                        name: '',
                        addressCoords: null,
                        description: '',
                        geofenceId: null,
                        address: null,
                        vehicleIds: null,
                      },
                    }}
                  />
                </DetailsDrawer>
              ),
          )
          .with({ status: 'valid', data: { type: 'edit' } }, ({ data: { id } }) => (
            <Details
              mapApiProviderId={mapApiProviderId}
              id={id}
              facilityTypes={facilityTypesQuery.data}
            />
          ))
          .exhaustive()
      }
    </GoogleMapsApiLoadChecker>
  )
}

function Details({
  id,
  facilityTypes,
  mapApiProviderId,
}: {
  id: LocationId
  facilityTypes: FetchLocationTypesResolved | undefined
  mapApiProviderId: UserAvailableMapApiProvider
}) {
  const history = useHistory()
  const facilityDetailsQuery = useLocationDetails({ id })
  const updateLocationMutation = useUpdateLocationMutation()
  const { data: carpoolVehiclesData } = useVehiclesWithCarpoolMetaQuery()

  const initialVehicleIds = useMemo(() => {
    if (!carpoolVehiclesData) return null

    return Array.from(carpoolVehiclesData.locationAndVehicleJoinByVehicleId.entries())
      .filter(([_, facilityId]) => facilityId === id)
      .map(([vehicleId]) => vehicleId)
  }, [carpoolVehiclesData, id])

  return (
    <DetailsDrawer onClose={() => history.push(LIST.subMenusRoutes.FACILITIES.path)}>
      {match(facilityDetailsQuery)
        .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
        .with({ status: 'error' }, () => null)
        .with({ status: 'success' }, ({ data: locationDetails }) =>
          facilityTypes ? (
            <Facility
              mapApiProviderId={mapApiProviderId}
              facilityTypes={facilityTypes}
              handleCancelClick={() =>
                history.push(LIST.subMenusRoutes.FACILITIES.path)
              }
              formParams={{
                type: 'edit',
                onSubmit: (data) => {
                  updateLocationMutation.mutate(
                    { data, id },
                    {
                      onSuccess: () => {
                        history.push(LIST.subMenusRoutes.FACILITIES.path)
                      },
                    },
                  )
                },
                isSubmittingForm: updateLocationMutation.isPending,
                initialValues: {
                  addressCoords: locationDetails.coords,
                  description: locationDetails.description ?? '',
                  name: locationDetails.name,
                  typeId: locationDetails.typeId,
                  geofenceId: locationDetails.geofenceId,
                  address: locationDetails.address,
                  vehicleIds: initialVehicleIds,
                },
              }}
            />
          ) : null,
        )
        .exhaustive()}
    </DetailsDrawer>
  )
}

type GeofencesAutocompleteOption = { id: GeofenceId; label: string }
type PlacesAutocompleteOption = { id: string; label: string }

export function Facility({
  formParams: { initialValues, type, onSubmit, isSubmittingForm },
  facilityTypes,
  mapApiProviderId,
  customTranslateFacilitiesTermFunc,
  handleCancelClick,
}: {
  formParams: {
    type: 'edit' | 'add'
    onSubmit: (data: FacilityFormData) => void
    initialValues: InitialValues
    isSubmittingForm: boolean
  }
  facilityTypes: FetchLocationTypesResolved
  mapApiProviderId: UserAvailableMapApiProvider
  customTranslateFacilitiesTermFunc?: (term: FacilityTranslationTermsBanned) => string
  handleCancelClick: () => void
}) {
  const geofencesQuery = useGeofencesQuery()
  const facilitiesQuery = useLocationsQuery()
  const vehiclesQuery = useVehiclesQuery()

  const { defaultMapLat, defaultMapLon, defaultMapZoom } =
    useTypedSelector(getSettings_UNSAFE)
  const googleMapsLanguageCode = useTypedSelector(
    getGoogleMapsLanguageCodeFromUserLocale,
  )
  const isRequiredMapSelectionAtSiteLocationsPage = useTypedSelector(
    getIsRequiredMapSelectionAtSiteLocationsPage,
  )

  const { minZoom, maxZoom } = useTypedSelector(getMapZoomOptions)
  const { mapMaxZoom, decrementMapZoom, incrementMapZoom } = useMemo(
    () => createMapZoomConfig({ minZoom, maxZoom }),
    [minZoom, maxZoom],
  )

  const facilityTypesOptions = useMemo(
    () =>
      facilityTypes.array.map((type) => ({
        value: type.id,
        label: mapLocationTypeToTranslation(type.identifier),
      })),
    [facilityTypes.array],
  )

  const geofencesOptions = useMemo(() => {
    const array: Array<GeofencesAutocompleteOption> = []
    const byId = new Map<GeofenceId, GeofencesAutocompleteOption>()

    for (const { id, name } of geofencesQuery.data?.geofences ?? []) {
      const option = { id: id as GeofenceId, label: name }
      array.push(option)
      byId.set(option.id, option)
    }

    return { array, byId }
  }, [geofencesQuery.data?.geofences])

  const assignedVehicleWithLocationById = useMemo(() => {
    const map = new Map<VehicleId, { value: VehicleId; label: string; item: string }>()
    if (!facilitiesQuery.data || !vehiclesQuery.data) {
      return map
    }
    for (const [vehicleId, facilityId] of facilitiesQuery.data
      .locationAndVehicleJoinByVehicleId) {
      const vehicle = vehiclesQuery.data.vehicleById.get(vehicleId)
      const facility = facilitiesQuery.data.byId.get(facilityId)
      if (!vehicle || !facility) {
        continue
      }

      map.set(vehicleId, {
        value: vehicleId,
        label: vehicle.registration ?? '',
        item: facility.name,
      })
    }

    return map
  }, [facilitiesQuery.data, vehiclesQuery.data])

  const {
    ready,
    value: searchValue,
    suggestions,
    setValue: setSearchValue,
  } = useUserGoogleLazyPlacesAutocomplete({
    debounce: 500,
    defaultValue: initialValues.address ?? undefined,
  })

  const placesSearchOptions = useMemo(
    () =>
      suggestions.data.map(
        (prediction): PlacesAutocompleteOption => ({
          label: prediction.description,
          id: prediction.place_id,
        }),
      ),
    [suggestions.data],
  )

  const {
    control,
    handleSubmit,
    formState: { isValid: isFormValid },
    setValue: setFormValue,
  } = useForm<FormPossibleValues>({
    resolver: zodResolverV4(
      createSchema({
        isRequiredMapSelection: isRequiredMapSelectionAtSiteLocationsPage,
      }),
    ),
    mode: 'all',
    defaultValues: initialValues,
  })

  const isEditing = type === 'edit'

  const locationAddressCords = useWatch({ name: 'addressCoords', control })
  const geofenceId = useWatch({ name: 'geofenceId', control })

  const [mapOptions, setMapOptions] = useState<{
    center: Coords
    zoom: number
  }>({
    center: {
      lat: initialValues.addressCoords?.lat || defaultMapLat || 0,
      lng: initialValues.addressCoords?.lng || defaultMapLon || 0,
    },
    zoom: initialValues.addressCoords ? mapMaxZoom - 2 : defaultMapZoom || 1,
  })

  const onFormSubmit = handleSubmit((_values) => {
    const values = _values as ValidSchema

    onSubmit({
      name: values.name,
      address:
        mapApiProviderId === MapApiProvider.GOOGLE ? searchValue : values.address,
      description: values.description,
      geofenceId: values.geofenceId,
      coords: values.addressCoords,
      typeId: values.typeId,
      vehicleIds: values.vehicleIds,
    })
  })

  const setMapCenter = useCallback(
    ({ lat, lng, zoom }: { lat: number; lng: number; zoom?: number }) => {
      setMapOptions((current) => ({
        ...current,
        center: {
          lat,
          lng,
        },
        zoom: zoom ?? current.zoom,
      }))
    },
    [],
  )

  const displayingGeofences = useMemo(() => {
    const geofence = geofencesQuery.data?.geofences.find((g) => g.id === geofenceId)
    return geofence ? [geofence] : []
  }, [geofenceId, geofencesQuery.data?.geofences])

  const handleIncreaseMapZoom = () => {
    setMapOptions((current) => ({
      ...current,
      zoom: incrementMapZoom(current.zoom),
    }))
  }

  const handleDecreaseMapZoom = () => {
    setMapOptions((current) => ({
      ...current,
      zoom: decrementMapZoom(current.zoom),
    }))
  }

  const updateAddress = useCallback(
    async ({
      lat,
      lng,
      zoom,
      address,
    }: {
      lat: number
      lng: number
      address: 'inferFromCoordinates' | { value: string }
      zoom?: number
    }) => {
      setMapCenter({ lat, lng, zoom })

      setFormValue('addressCoords', { lat, lng }, { shouldValidate: true })

      const getSearchValue = async () => {
        if (address === 'inferFromCoordinates') {
          const result = await getGoogleMapsCachedGeocodeFirstResult({
            location: { lat, lng },
            language: googleMapsLanguageCode,
          })
          return result.formatted_address
        } else {
          return address.value
        }
      }

      setSearchValue(await getSearchValue())
    },
    [googleMapsLanguageCode, setMapCenter, setSearchValue, setFormValue],
  )

  const handleUseMyLocation = ({ lat, lng }: { lat: number; lng: number }) => {
    updateAddress({
      lat,
      lng,
      zoom: mapMaxZoom,
      address: 'inferFromCoordinates',
    })
  }

  const handleUpdateLocation = (
    { lat, lng }: { lat: number; lng: number },
    options: SetValueConfig,
  ) => {
    setFormValue('addressCoords', { lat, lng }, options)
  }

  const handleAddressSelection = async ({ address }: { address: string }) => {
    const result = await getGoogleMapsCachedGeocodeFirstResult({
      address,
      language: googleMapsLanguageCode,
    })
    const { lat, lng } = getGoogleLatLngFromGeocode(result)
    updateAddress({
      lat,
      lng,
      zoom: mapMaxZoom,
      address: { value: address },
    })
  }

  const removeMarker = () => {
    setFormValue('addressCoords', null, { shouldValidate: true })
  }

  const { translateFacilitiesTerm: defaultTranslateFacilitiesTerm } = useTypedSelector(
    getFacilitiesTranslatorFn,
  )
  const translateFacilitiesTerm =
    customTranslateFacilitiesTermFunc ?? defaultTranslateFacilitiesTerm

  return (
    <>
      <Typography variant="h5">
        {isEditing
          ? translateFacilitiesTerm('facility.edit.title')
          : translateFacilitiesTerm('facilities.addFacility')}
      </Typography>
      <Stack sx={{ mt: 5 }}>
        <Typography>{translateFacilitiesTerm('facility.details')}</Typography>
        <form onSubmit={onFormSubmit}>
          <Stack sx={{ mt: 2, mb: 6, gap: 2 }}>
            <TextFieldControlled
              required
              ControllerProps={{
                name: 'name',
                control,
              }}
              label={ctIntl.formatMessage({ id: 'Name' })}
              inputProps={{
                maxLength: MAX_LENGTH,
              }}
            />
            <TextFieldControlled
              required
              select
              ControllerProps={{
                name: 'typeId',
                control,
              }}
              label={ctIntl.formatMessage({ id: 'location.type' })}
            >
              {facilityTypesOptions.map((type) => (
                <MenuItem
                  key={type.value}
                  value={type.value}
                >
                  {type.label}
                </MenuItem>
              ))}
            </TextFieldControlled>

            {mapApiProviderId === MapApiProvider.GOOGLE ? (
              <>
                <Controller
                  control={control}
                  name="addressCoords"
                  render={({ fieldState }) => (
                    <PlacesAutoCompleteSearch
                      inputValue={searchValue}
                      onInputChange={(_e, value, reason) => {
                        if (reason === 'clear' || !value) {
                          removeMarker()
                        }
                        setSearchValue(value)
                      }}
                      required={isRequiredMapSelectionAtSiteLocationsPage}
                      onChange={(_e, newValue, reason) => {
                        if (reason === 'clear') {
                          return removeMarker()
                        }
                        if (newValue) {
                          handleAddressSelection({
                            address:
                              typeof newValue === 'string' ? newValue : newValue.label,
                          })
                        }
                      }}
                      options={placesSearchOptions}
                      disabled={!ready}
                      error={fieldState.error}
                    />
                  )}
                />
                <MapArea
                  mapOptions={mapOptions}
                  setMapOptions={setMapOptions}
                  handleIncreaseMapZoom={handleIncreaseMapZoom}
                  handleDecreaseMapZoom={handleDecreaseMapZoom}
                  handleUseMyLocation={handleUseMyLocation}
                  displayingGeofences={displayingGeofences}
                  locationAddressCords={locationAddressCords}
                  handleUpdateLocation={handleUpdateLocation}
                  updateAddress={updateAddress}
                />
              </>
            ) : (
              <>
                {/* Client doesn't use internet so we don't use hereFindPlaces */}
                <TextFieldControlled
                  ControllerProps={{
                    name: 'address',
                    control,
                  }}
                  required={isRequiredMapSelectionAtSiteLocationsPage}
                  label={ctIntl.formatMessage({ id: 'location.address' })}
                  inputProps={{
                    maxLength: MAX_LENGTH,
                  }}
                />
                <MapOpenLayersArea
                  mapApiProvider={mapApiProviderId}
                  mapOptions={mapOptions}
                  setMapOptions={setMapOptions}
                  handleIncreaseMapZoom={handleIncreaseMapZoom}
                  handleDecreaseMapZoom={handleDecreaseMapZoom}
                  handleUseMyLocation={handleUseMyLocation}
                  displayingGeofences={displayingGeofences}
                  locationAddressCords={locationAddressCords}
                  handleUpdateLocation={handleUpdateLocation}
                />
              </>
            )}

            <Controller
              control={control}
              name="geofenceId"
              render={({ field, fieldState }) => (
                <Autocomplete
                  value={
                    field.value
                      ? (geofencesOptions.byId.get(field.value) ?? null)
                      : null
                  }
                  {...getAutocompleteVirtualizedProps({
                    options: geofencesOptions.array,
                  })}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={`${ctIntl.formatMessage({
                        id: 'Geofence',
                      })}${
                        ' (' +
                        ctIntl.formatMessage({
                          id: 'Optional',
                        }) +
                        ')'
                      }`}
                      helperText={ctIntl.formatMessage({
                        id: fieldState.error?.message ?? '',
                      })}
                      error={!!fieldState.error}
                    />
                  )}
                  onChange={(_e, newValue) => {
                    setFormValue('geofenceId', newValue ? newValue.id : null, {
                      shouldValidate: true,
                    })
                  }}
                />
              )}
            />
            <Controller
              control={control}
              name="vehicleIds"
              render={({ field, fieldState }) => (
                <Stack>
                  <AssignVehicleComponent
                    itemName="Location"
                    assignedVehicleWithAdditionalFieldsById={
                      assignedVehicleWithLocationById
                    }
                    selectedValues={new Set(field.value)}
                    onChange={(selectedSet: Set<VehicleId>) => {
                      field.onChange(Array.from(selectedSet))
                    }}
                  />
                  <Box sx={{ m: 1 }}>
                    {fieldState.error ? (
                      <Typography color="error">{fieldState.error.message}</Typography>
                    ) : (
                      <FormHelperText>
                        {ctIntl.formatMessage({
                          id: 'carpool.location.assignVehicle.text',
                        })}
                      </FormHelperText>
                    )}
                  </Box>
                </Stack>
              )}
            />

            <Divider />
            <TextFieldControlled
              ControllerProps={{
                name: 'description',
                control,
              }}
              label={`${ctIntl.formatMessage({
                id: 'Description',
              })} (${ctIntl.formatMessage({
                id: 'Optional',
              })})`}
              variant="standard"
              multiline
              inputProps={{ maxLength: MAX_LENGTH }}
            />
          </Stack>
          <Stack
            direction="row"
            justifyContent="space-between"
          >
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleCancelClick}
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>

            <Button
              variant="contained"
              type="submit"
              disabled={!isFormValid || isSubmittingForm}
              startIcon={
                isSubmittingForm ? (
                  <CircularProgress
                    color="inherit"
                    size={16}
                  />
                ) : null
              }
            >
              {ctIntl.formatMessage({ id: 'Save' })}
            </Button>
          </Stack>
        </form>
      </Stack>
    </>
  )
}

function PlacesAutoCompleteSearch({
  options,
  inputValue,
  onInputChange,
  required,
  disabled,
  error,
  onChange,
}: {
  options: Array<PlacesAutocompleteOption>
  inputValue: string
  disabled: boolean
  required: boolean
  error: ControllerFieldState['error']
  onInputChange: (
    event: React.SyntheticEvent<Element, Event>,
    value: string,
    reason: AutocompleteInputChangeReason,
  ) => void
  onChange: (
    event: React.SyntheticEvent<Element, Event>,
    value: string | PlacesAutocompleteOption | null,
    reason: AutocompleteChangeReason,
  ) => void
}) {
  return (
    <Autocomplete
      onChange={onChange}
      options={options}
      renderInput={(params) => (
        <TextField
          {...params}
          required={required}
          label={ctIntl.formatMessage({
            id: 'location.address',
          })}
          helperText={ctIntl.formatMessage({
            id: error?.message ?? '',
          })}
          error={!!error}
        />
      )}
      onInputChange={onInputChange}
      inputValue={inputValue}
      renderOption={(params, option) => (
        <ListItem
          divider
          {...params}
        >
          {option.label}
        </ListItem>
      )}
      disabled={disabled}
      freeSolo
      noOptionsText={<></>}
    />
  )
}

const DetailsDrawer = (
  props: Except<DrawerProps, 'PaperProps' | 'anchor' | 'open'>,
) => (
  <Drawer
    anchor="right"
    open
    PaperProps={{ sx: { width: 500, maxWidth: '90vw', px: 3, pt: 4, pb: 5 } }}
    {...props}
  />
)
