import { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { capitalize } from 'lodash'
import {
  Autocomplete,
  Button,
  Chip,
  DataGridAsTabItem,
  Divider,
  FormControl,
  GridActionsCellItem,
  GridPagination,
  GridRowModes,
  GridToolbarExport,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  Stack,
  styled,
  TextField,
  Tooltip,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type GridColDef,
  type GridRowHeightParams,
  type GridRowId,
  type GridRowModesModel,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import HighlightOffIcon from '@mui/icons-material/HighlightOff'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import ModeOutlinedIcon from '@mui/icons-material/ModeOutlined'
import PersonAddAlt1OutlinedIcon from '@mui/icons-material/PersonAddAlt1Outlined'
import { rgba } from 'polished'
import { Controller, useForm } from 'react-hook-form'
import { useHistory } from 'react-router-dom'
import * as R from 'remeda'
import { z } from 'zod/v4'

import type { DriverId } from 'api/types'
import { buildRouteQueryStringKeepingExistingSearchParams } from 'api/utils'
import { getDidTagFaceIdEnabled, getDrivers } from 'duxs/drivers'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { useValidatedSearchParams } from 'src/hooks/useValidatedSearchParams'
import { getDriverDetailsModalMainPath } from 'src/modules/app/GlobalModals/DriverDetails'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'

import { Stats } from 'cartrack-ui-kit'
import {
  useAssignDriverIdTags,
  useMarkDriverIdTags,
  useRemoveDriverIdTags,
  useUpdateAssignedDriverIdTags,
  type MVDriverIdTag,
} from './queries'
import {
  useAllDriversIdTagsQuery,
  type FetchDriverIdTagsQuery,
} from './useAllDriversIdTagsQuery'
import {
  DatePickerCellField,
  driverIDTagSchema,
  DriverIDTagsMenu,
  type DriverIdTag,
  type DriverIdTagPossibleSchema,
} from './utils'

const StatusFilterOptions = [
  { label: 'All tags', value: 'all' },
  { label: 'Available tags', value: 'available' },
  { label: 'Lost Tags', value: 'lost' },
] as const

type StatusFilterValue = (typeof StatusFilterOptions)[number]['value']

const statusFilterValues = [
  StatusFilterOptions[0].value,
  StatusFilterOptions[1].value,
  StatusFilterOptions[2].value,
] as const

export const driversIdTagsFilterParamsSchema = z.object({
  filter: z.enum(statusFilterValues),
})

type DataGridRow = FetchDriverIdTagsQuery.Return[number]

const formDataToDataGridRow = (
  data: DriverIdTagPossibleSchema,
  rowData: DataGridRow,
  drivers: ReturnType<typeof getDrivers>,
): DataGridRow => ({
  ...rowData,
  driverId: data.driverId ?? null,
  driverName: data.driverId
    ? (drivers.find((d) => d.id === data.driverId)?.name ?? '')
    : '',
  description: data.description ?? null,
  assignedTime: data.startDate ? data.startDate.toISOString() : null,
  unassignedTime: data.endDate ? data.endDate.toISOString() : null,
})

const dataGridToMVDriverIDTag = (tag: DataGridRow): MVDriverIdTag => ({
  assigned_ts: tag.assignedTime,
  unassigned_ts: tag.unassignedTime,
  client_driver_id: tag.driverId as string,
  client_driver_tag_description: tag.description,
  client_driver_tag_id: tag.id,
  identification_tag: tag.idTag,
})

const parseRowToFormData = (
  row: DataGridRow | null | undefined,
): DriverIdTagPossibleSchema => ({
  driverId: row?.driverId ?? null,
  description: row?.description ?? '',
  startDate: row?.assignedTime ? new Date(row.assignedTime) : new Date(),
  endDate: row?.unassignedTime ? new Date(row?.unassignedTime) : undefined,
})

const DriverTagsTable = () => {
  const history = useHistory()
  const didFaceIdEnabled = useTypedSelector(getDidTagFaceIdEnabled)
  const [rowModesModel, setRowModesModel] = useState<GridRowModesModel>({})
  const [selectedRowIds, setSelectedRowIds] = useState<Array<string>>([])
  const [menuState, setMenuState] = useState<{
    anchorEl: HTMLButtonElement
    id: GridRowId
    row: DataGridRow | null
  } | null>(null)
  const validatedParams = useValidatedSearchParams(
    () => driversIdTagsFilterParamsSchema,
  )
  const [editFormData, setEditFormData] = useState<DriverIdTagPossibleSchema | null>(
    null,
  )
  const [currentModal, setCurrentModal] = useState<
    | {
        type: 'markIdTagsAsLost'
        context: Array<MVDriverIdTag>
      }
    | { type: 'removeIdTags'; context: Array<MVDriverIdTag> }
  >()
  const columnHelper = useDataGridColumnHelper<DriverIdTag>({ filterMode: 'client' })

  const handleSelectionModelChange = (selectionModal: GridRowSelectionModel) =>
    setSelectedRowIds(selectionModal as Array<string>)

  const apiRef = useGridApiRef()

  const driverIdTagsQuery = useAllDriversIdTagsQuery()
  const markDriverIdTagsMutation = useMarkDriverIdTags({ isSingleDriverId: false })
  const removeDriverIdTagsMutation = useRemoveDriverIdTags({ isSingleDriverId: false })
  const assignDriverIdTagsMutation = useAssignDriverIdTags({ isSingleDriverId: false })
  const updateDriverIdTagsMutation = useUpdateAssignedDriverIdTags({
    isSingleDriverId: false,
  })

  const drivers = useTypedSelector(getDrivers)

  const formValues = useMemo(
    () =>
      editFormData
        ? editFormData
        : {
            driverId: null,
            description: '',
            startDate: new Date(),
            endDate: null,
          },
    [editFormData],
  )

  const status: StatusFilterValue =
    validatedParams.status === 'valid' ? validatedParams.data.filter : 'all'

  const setStatus = (value: StatusFilterValue) => {
    history.push(
      `${history.location.pathname}?${buildRouteQueryStringKeepingExistingSearchParams({
        location: history.location,
        schema: driversIdTagsFilterParamsSchema,
        searchParams: {
          filter: value,
        },
      })}`,
    )
  }

  const {
    handleSubmit,
    control,
    formState: { isValid, isDirty, errors },
    getValues,
    reset,
  } = useForm<DriverIdTagPossibleSchema>({
    resolver: zodResolverV4(driverIDTagSchema),
    values: formValues,
    mode: 'all',
  })

  const isMultipleIDtagsToMarkSelected = useMemo(
    () => currentModal?.type === 'markIdTagsAsLost' && currentModal.context.length > 1,
    [currentModal],
  )

  const parsedDrivers = useMemo(
    () =>
      drivers
        .filter((d) => d.active)
        .map((driver) => ({
          label: driver.name,
          value: driver.id,
        })),
    [drivers],
  )

  const driverIdTagsData = useMemo(() => {
    if (driverIdTagsQuery.data) {
      const allTagsCount = driverIdTagsQuery.data.length
      const availableTags = driverIdTagsQuery.data.filter(
        (d) => d.status === 'available',
      )
      const lostTags = driverIdTagsQuery.data.filter((d) => d.status === 'lost')
      const availableTagsCount = availableTags.length
      const lostTagsCount = lostTags.length
      const tags =
        // eslint-disable-next-line no-nested-ternary
        status === 'all'
          ? driverIdTagsQuery.data
          : status === 'available'
            ? availableTags
            : lostTags
      return {
        tags: tags,
        allTagsCount,
        availableTags: availableTagsCount,
        lostTags: lostTagsCount,
      }
    }
    return { tags: [], allTagsCount: 0, availableTags: 0, lostTags: 0 }
  }, [driverIdTagsQuery.data, status])

  const statusSingleSelectColumns = useMemo(
    () =>
      [
        {
          label: ctIntl.formatMessage({ id: 'lost' }),
          value: 'lost',
        },
        {
          label: ctIntl.formatMessage({ id: 'available' }),
          value: 'available',
        },
      ] as const satisfies ReadonlyArray<{
        label: string
        value: 'lost' | 'available'
      }>,
    [],
  )

  const selectedRows = useMemo(() => {
    if (selectedRowIds.length > 0) {
      return selectedRowIds.reduce<{
        availableTags: Array<DataGridRow>
        lostTags: Array<DataGridRow>
      }>(
        (acc, idTag) => {
          const rowData = apiRef.current?.getRow(idTag)
          if (rowData && rowData.status === 'available') {
            acc.availableTags.push(rowData)
          }
          if (rowData && rowData.status === 'lost') {
            acc.lostTags.push(rowData)
          }
          return acc
        },
        {
          availableTags: [],
          lostTags: [],
        },
      )
    }
    return { availableTags: [], lostTags: [] }
  }, [apiRef, selectedRowIds])

  const bulkParseTags = (tags: Array<DataGridRow>) =>
    tags.map((tag) => dataGridToMVDriverIDTag(tag))

  const driverIdTagsColumns = useMemo(() => {
    const preColumns = [
      columnHelper.string((_, row) => row.idTag, {
        field: 'id-tags',
        headerName: ctIntl.formatMessage({ id: 'ID Tags' }),
        flex: 1,
      }),
      columnHelper.singleSelect((_, row) => row.status, {
        field: 'status',
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        valueOptions: statusSingleSelectColumns,
        flex: 1,
        renderCell: ({ row }) => (
          <Chip
            size="small"
            color={row.status === 'available' ? 'info' : 'error'}
            label={ctIntl.formatMessage({ id: capitalize(row.status) })}
            variant="outlined"
          />
        ),
      }),
      columnHelper.string((_, row) => row.driverName, {
        field: 'driverName',
        headerName: ctIntl.formatMessage({ id: 'Driver' }),
        flex: 1,
        editable: true,
        renderCell: ({ row }) =>
          row.driverName ? (
            row.driverName
          ) : (
            <Typography
              variant="body2"
              sx={{ color: 'rgba(0, 0, 0, 0.38)' }}
            >
              {ctIntl.formatMessage({ id: 'Unassigned' })}
            </Typography>
          ),
        renderEditCell: ({ row }) => (
          <Controller
            control={control}
            name="driverId"
            render={({ field }) => (
              <Autocomplete
                {...getAutocompleteVirtualizedProps({ options: parsedDrivers })}
                sx={{ width: '100%' }}
                disabled={row.status === 'lost'}
                size="small"
                value={parsedDrivers.find((driver) => driver.value === field.value)}
                onChange={(_, newValue) =>
                  field.onChange(newValue?.value ? newValue.value : null)
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    required
                    label={ctIntl.formatMessage({ id: 'Select Drivers' })}
                    sx={{ backgroundColor: 'white' }}
                  />
                )}
              />
            )}
          />
        ),
      }),
      columnHelper.string((_, row) => row.description, {
        field: 'description',
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        flex: 1,
        editable: true,
        renderEditCell: () => (
          <TextFieldControlled
            fullWidth
            required
            ControllerProps={{ control, name: 'description' }}
            label={ctIntl.formatMessage({ id: 'Description' })}
            sx={{
              input: {
                backgroundColor: 'primary.contrastText',
              },
            }}
          />
        ),
      }),
    ] satisfies Array<GridColDef<DriverIdTag>>

    const postColumns = [
      columnHelper.date({
        field: 'start-date',
        headerName: ctIntl.formatMessage({ id: 'Start Date' }),
        flex: 1,
        valueGetter: (_, row) => (row.assignedTime ? new Date(row.assignedTime) : null),
        editable: true,
        renderEditCell: () => (
          <Controller
            control={control}
            name="startDate"
            render={({ field }) => (
              <DatePickerCellField
                field={field}
                fieldToCheck="endDate"
                control={control}
              />
            )}
          />
        ),
      }),
      columnHelper.date({
        field: 'end-date',
        headerName: ctIntl.formatMessage({ id: 'End Date' }),
        flex: 1,
        valueGetter: (_, row) =>
          row.unassignedTime ? new Date(row.unassignedTime) : null,
        editable: true,
        renderEditCell: () => (
          <Controller
            control={control}
            name="endDate"
            render={({ field }) => (
              <DatePickerCellField
                field={field}
                fieldToCheck="startDate"
                control={control}
              />
            )}
          />
        ),
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        align: 'right',
        getActions: ({ row, id }) => {
          const isInEditMode = rowModesModel[id]?.mode === GridRowModes.Edit

          if (isInEditMode) {
            return [
              <Tooltip
                key="save"
                title={ctIntl.formatMessage({ id: 'Save' })}
              >
                <span>
                  <GridActionsCellItem
                    disabled={!isValid || !isDirty}
                    icon={<CheckIcon />}
                    label={ctIntl.formatMessage({ id: 'Save' })}
                    onClick={(e) =>
                      handleSubmit(() =>
                        setRowModesModel((prev) => ({
                          ...prev,
                          [id]: { mode: GridRowModes.View },
                        })),
                      )(e)
                    }
                    sx={{ color: 'success.main' }}
                  />
                </span>
              </Tooltip>,
              <Tooltip
                key="cancel"
                title={ctIntl.formatMessage({ id: 'Cancel' })}
              >
                <span>
                  <GridActionsCellItem
                    icon={<CloseIcon />}
                    label={ctIntl.formatMessage({ id: 'Cancel' })}
                    onClick={() => {
                      reset()
                      setRowModesModel((prev) => ({
                        ...prev,
                        [id]: { mode: GridRowModes.View, ignoreModifications: true },
                      }))
                    }}
                    sx={{ color: 'error.main' }}
                  />
                </span>
              </Tooltip>,
            ]
          }

          return row.assign === 'assign'
            ? [
                <Tooltip
                  key="edit"
                  title={ctIntl.formatMessage({
                    id: 'list.drivers.driverIDTags.edit',
                  })}
                >
                  <span>
                    <GridActionsCellItem
                      icon={<ModeOutlinedIcon />}
                      label={ctIntl.formatMessage({ id: 'Edit' })}
                      onClick={(e) =>
                        setMenuState({
                          anchorEl: e.currentTarget,
                          id,
                          row: apiRef.current?.getRow(id),
                        })
                      }
                    />
                  </span>
                </Tooltip>,
              ]
            : [
                <Tooltip
                  key="assign"
                  title={ctIntl.formatMessage({
                    id: 'list.drivers.driverIDTags.assign',
                  })}
                >
                  <Button
                    variant="text"
                    startIcon={<PersonAddAlt1OutlinedIcon />}
                    onClick={() => {
                      setEditFormData(parseRowToFormData(null))
                      setRowModesModel((prev) => ({
                        ...R.mapValues(prev, () => ({
                          mode: GridRowModes.View,
                          ignoreModifications: true,
                        })),
                        [id]: { mode: GridRowModes.Edit },
                      }))
                    }}
                  >
                    {ctIntl.formatMessage({ id: 'Assign' })}
                  </Button>
                </Tooltip>,
              ]
        },
      },
    ] satisfies Array<GridColDef<DriverIdTag>>
    return didFaceIdEnabled
      ? [
          ...preColumns,
          columnHelper.valueGetter((_, row) => row.driverImage, {
            field: 'image',
            type: 'custom',
            headerName: ctIntl.formatMessage({ id: 'Image' }),
            renderCell: ({ value }) =>
              value && (
                <img
                  src={value}
                  style={{ paddingBottom: '10px', paddingTop: '10px' }}
                />
              ),
            flex: 1,
          }) satisfies GridColDef<DriverIdTag>,
          ...postColumns,
        ]
      : [...preColumns, ...postColumns]
  }, [
    columnHelper,
    statusSingleSelectColumns,
    didFaceIdEnabled,
    control,
    parsedDrivers,
    rowModesModel,
    isValid,
    isDirty,
    handleSubmit,
    reset,
    apiRef,
  ])

  const rowUpdate = useCallback(
    (newRow: DataGridRow, oldRow: DataGridRow): Promise<DataGridRow> | DataGridRow =>
      new Promise((resolve, _reject) => {
        const rollbackChanges = () => resolve(oldRow)
        // assign driver
        if (!oldRow.driverId) {
          const editedRow = formDataToDataGridRow(getValues(), newRow, drivers)
          assignDriverIdTagsMutation.mutate(
            {
              driverIdTagsToAssign: [dataGridToMVDriverIDTag(editedRow)],
            },
            {
              onSuccess: () => resolve(editedRow),
              onError: () => rollbackChanges(),
            },
          )
        } else {
          // update driver
          const editedRow = formDataToDataGridRow(getValues(), newRow, drivers)
          updateDriverIdTagsMutation.mutate(
            {
              driverIdTagsToUpdate: dataGridToMVDriverIDTag(editedRow),
            },
            {
              onSuccess: () => resolve(editedRow),
              onError: () => rollbackChanges(),
            },
          )
        }
      }),
    [assignDriverIdTagsMutation, drivers, updateDriverIdTagsMutation, getValues],
  )

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB<DataGridRow>
        Component={DataGridAsTabItem}
        dataGridId="DriverIDTagsTable"
        data-testid="DriverIDTagsTable"
        pagination
        pageSizeOptions={[25, 50, 100]}
        initialState={{
          pagination: {
            paginationModel: { pageSize: 25, page: 0 },
          },
        }}
        editMode="row"
        getRowHeight={useCallbackBranded(
          ({ id }: GridRowHeightParams<DataGridRow>) => {
            if (didFaceIdEnabled) {
              return 'auto'
            }

            const isInEditMode = rowModesModel[id]?.mode === GridRowModes.Edit
            if (Object.keys(errors).length > 0 && isInEditMode) {
              return 80
            }
            return null
          },
          [didFaceIdEnabled, errors, rowModesModel],
        )}
        apiRef={apiRef}
        processRowUpdate={rowUpdate}
        rowModesModel={rowModesModel}
        columns={driverIdTagsColumns}
        rows={driverIdTagsData.tags}
        checkboxSelection={true} // TODO check if there is a permission for this
        sx={({ palette }) => ({
          '& .MuiDataGrid-row--editing': {
            boxShadow: 'none',
          },
          '& .MuiDataGrid-row--editing .MuiDataGrid-cell': {
            backgroundColor: rgba(palette.primary.main, palette.action.selectedOpacity),
            alignItems: () =>
              Object.keys(errors).length > 0 ? 'flex-start !important' : 'inherit',
            pt: '8px',
            pb: '8px',
          },
        })}
        loading={
          driverIdTagsQuery.status === 'pending' ||
          markDriverIdTagsMutation.status === 'pending' ||
          assignDriverIdTagsMutation.status === 'pending' ||
          updateDriverIdTagsMutation.status === 'pending'
        }
        isRowSelectable={({ row }) => row.assign === 'assign'}
        onRowSelectionModelChange={handleSelectionModelChange}
        rowSelectionModel={selectedRowIds}
        disableRowSelectionOnClick
        onRowClick={({ row }: { row: DataGridRow }) => {
          if (row.driverId !== null) {
            history.push(
              getDriverDetailsModalMainPath(
                history.location,
                row.driverId as DriverId,
                'ID_TAGS',
              ),
            )
          }
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          noRowsOverlay: () => <DataStatePlaceholder label="No data available" />,
          footer: () => (
            <Stack
              gap={1}
              direction="row"
              justifyContent="space-between"
              alignItems="center"
            >
              <Stack
                p={1}
                gap={1}
                direction="row"
                alignItems="center"
              >
                <InfoOutlinedIcon color="info" />

                <Typography
                  variant="body2"
                  color="text.secondary"
                >
                  {ctIntl.formatMessage({
                    id: 'list.drivers.idTags.table.footer.hint',
                  })}
                </Typography>
              </Stack>

              <GridPagination
                showFirstButton
                showLastButton
              />
            </Stack>
          ),
          pagination: GridPagination,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              settingsButton: { show: true },
              filterButton: { show: true },
            },
            extraContent: {
              left: (
                <FormControl>
                  <InputLabel>
                    {ctIntl.formatMessage({ id: 'Status Filter' })}
                  </InputLabel>
                  <Select
                    size="small"
                    value={status}
                    label={ctIntl.formatMessage({
                      id: ctIntl.formatMessage({ id: 'Status Filter' }),
                    })}
                    onChange={(e) => setStatus(e.target.value as StatusFilterValue)}
                  >
                    {StatusFilterOptions.map((option) => (
                      <MenuItem
                        key={option.value}
                        value={option.value}
                      >
                        {ctIntl.formatMessage({ id: option.label })}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              ),
              middle: (
                <Stack
                  direction="row"
                  gap={2}
                >
                  <Stats
                    reversed
                    data={[
                      {
                        key: `Total ID Tags`,
                        value: driverIdTagsData.allTagsCount,
                      },
                    ]}
                  />
                  <Divider
                    orientation="vertical"
                    variant="middle"
                    flexItem
                  />
                  <Stats
                    reversed
                    data={[
                      {
                        key: `Available ID Tags`,
                        value: driverIdTagsData.availableTags,
                      },
                    ]}
                  />
                  <Divider
                    orientation="vertical"
                    variant="middle"
                    flexItem
                  />
                  <Stats
                    reversed
                    data={[
                      {
                        key: `Lost ID Tags`,
                        value: driverIdTagsData.lostTags,
                      },
                    ]}
                  />
                </Stack>
              ),
              right: (
                <>
                  <GridToolbarExport
                    csvOptions={{
                      fileName: ctIntl.formatMessage({ id: 'Driver ID Tags' }),
                    }}
                    excelOptions={{
                      fileName: ctIntl.formatMessage({ id: 'Driver ID Tags' }),
                    }}
                    // TODO: need to enable it when print export stable
                    printOptions={{ disableToolbarButton: true }}
                  />
                  {status !== 'lost' && (
                    <Button
                      variant="outlined"
                      color="error"
                      size="small"
                      disabled={selectedRows.availableTags.length === 0}
                      startIcon={<HighlightOffIcon />}
                      onClick={() => {
                        setCurrentModal({
                          type: 'markIdTagsAsLost',
                          context: bulkParseTags(selectedRows.availableTags),
                        })
                      }}
                    >
                      <Stack
                        direction="row"
                        gap={0.5}
                      >
                        {ctIntl.formatMessage({ id: 'Mark as lost' })}
                        {selectedRows.availableTags.length > 0 && (
                          <Counter>{selectedRows.availableTags.length}</Counter>
                        )}
                      </Stack>
                    </Button>
                  )}
                  {status !== 'available' && (
                    <Button
                      variant="outlined"
                      color="success"
                      size="small"
                      disabled={selectedRows.lostTags.length === 0}
                      startIcon={<HighlightOffIcon />}
                      onClick={() => {
                        markDriverIdTagsMutation.mutate(
                          {
                            driverIdTagsToMark: bulkParseTags(selectedRows.lostTags),
                            deactivate: false,
                          },
                          {
                            onSuccess: () => {
                              setCurrentModal(undefined)
                              setSelectedRowIds([])
                            },
                          },
                        )
                      }}
                    >
                      <Stack
                        direction="row"
                        gap={0.5}
                      >
                        {ctIntl.formatMessage({ id: 'Recover ID Tags' })}
                        {selectedRows.lostTags.length > 0 && (
                          <Counter>{selectedRows.lostTags.length}</Counter>
                        )}
                      </Stack>
                    </Button>
                  )}
                </>
              ),
            },
          }),
        }}
      />
      <DriverIDTagsMenu
        isOpen={Boolean(menuState)}
        anchorEl={menuState?.anchorEl}
        closeMenu={() => setMenuState(null)}
        editDetails={() => {
          setEditFormData(parseRowToFormData(menuState?.row))
          setMenuState(null)
          setRowModesModel((prev) => ({
            ...R.mapValues(prev, () => ({
              mode: GridRowModes.View,
              ignoreModifications: true,
            })),
            [menuState?.id as GridRowId]: { mode: GridRowModes.Edit },
          }))
        }}
        showUnassign={true}
        unassignTag={() => {
          setMenuState(null)
          if (menuState?.row) {
            setCurrentModal({
              type: 'removeIdTags',
              context: [dataGridToMVDriverIDTag(menuState.row)],
            })
          }
        }}
        showMarkAsLost={menuState?.row?.status === 'available'}
        markAsLost={() => {
          if (menuState?.row) {
            setMenuState(null)
            setCurrentModal({
              type: 'markIdTagsAsLost',
              context: [dataGridToMVDriverIDTag(menuState.row)],
            })
          }
        }}
        viewAllLostTags={() => {
          setStatus('lost')
          setMenuState(null)
        }}
        showMarkAsAvailable={menuState?.row?.status === 'lost'}
        markAsAvailable={() => {
          if (menuState?.row) {
            setMenuState(null)
            markDriverIdTagsMutation.mutate(
              {
                driverIdTagsToMark: [dataGridToMVDriverIDTag(menuState.row)],
                deactivate: false,
              },
              {
                onSuccess: () => {
                  setCurrentModal(undefined)
                  setSelectedRowIds([])
                },
              },
            )
          }
        }}
        loadingUnassign={removeDriverIdTagsMutation.status === 'pending'}
        loadingMarkAsLost={markDriverIdTagsMutation.status === 'pending'}
        loadingMarkAsAvailable={markDriverIdTagsMutation.status === 'pending'}
      />

      {currentModal?.type === 'markIdTagsAsLost' && (
        <ConfirmationModal
          open
          title={ctIntl.formatMessage(
            {
              id: `list.driverTags.${
                isMultipleIDtagsToMarkSelected ? 'multiMark' : 'singleMark'
              }.modalTitle`,
            },
            {
              values: {
                tagType: ctIntl.formatMessage({
                  id: 'lost',
                }),
                tagCount: selectedRows.availableTags.length,
              },
            },
          )}
          onClose={() => setCurrentModal(undefined)}
          onConfirm={() => {
            markDriverIdTagsMutation.mutate(
              {
                driverIdTagsToMark: currentModal.context,
                deactivate: true,
              },
              {
                onSuccess: () => {
                  setCurrentModal(undefined)
                  setSelectedRowIds([])
                },
              },
            )
          }}
          isLoading={markDriverIdTagsMutation.status === 'pending'}
        >
          {ctIntl.formatMessage(
            {
              id: `list.driverTags.${
                isMultipleIDtagsToMarkSelected ? 'multiMark' : 'singleMark'
              }.modalMessage`,
            },
            {
              values: {
                tagType: ctIntl.formatMessage({
                  id: 'lost',
                }),
                tagCount: selectedRows.availableTags.length,
              },
            },
          )}
        </ConfirmationModal>
      )}

      {currentModal?.type === 'removeIdTags' && (
        <ConfirmationModal
          title="list.driverTags.removeTags.modalTitle"
          open
          onClose={() => setCurrentModal(undefined)}
          onConfirm={() => {
            removeDriverIdTagsMutation.mutate(
              {
                driverIdTagsToRemove: currentModal.context,
              },
              {
                onSuccess: () => {
                  setCurrentModal(undefined)
                },
              },
            )
          }}
          isLoading={removeDriverIdTagsMutation.status === 'pending'}
        >
          {ctIntl.formatMessage({ id: 'list.driverTags.removeTags.modalMessage' })}
        </ConfirmationModal>
      )}
    </>
  )
}

export default DriverTagsTable

const Counter = styled(Typography)(({ theme }) =>
  theme.unstable_sx({
    borderRadius: '50%',
    borderWidth: '1px',
    borderStyle: 'solid',
    marginLeft: 1,
    px: 0.8,
  }),
)

Counter.defaultProps = {
  variant: 'body2',
}
