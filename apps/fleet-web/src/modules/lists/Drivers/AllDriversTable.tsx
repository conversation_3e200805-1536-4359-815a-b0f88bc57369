import { useMemo, useState } from 'react'
import {
  Chip,
  DataGrid,
  Divider,
  FormControl,
  gridExpandedRowCountSelector,
  GridToolbarExport,
  InputLabel,
  MenuItem,
  OverflowTypography,
  Select,
  Skeleton,
  Stack,
  Tooltip,
  Typography,
  useDataGridColumnHelper,
  useGridApiRef,
  useGridSelector,
  type GridApi,
  type GridColDef,
} from '@karoo-ui/core'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

import {
  getDefaultDriversTableColumns,
  getListDriverPassport,
} from 'duxs/user-sensitive-selectors'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { useDriversQuery } from 'src/modules/api/useDriversQuery'
import { getDriverDetailsModalMainPath } from 'src/modules/app/GlobalModals/DriverDetails'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useDepartmentsOptionsQuery } from 'src/modules/settings/company/api/useDepartmentsQuery'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'
import FormattedPhone from 'src/util-components/formatted-phone'

import { StarRating, Stats } from 'cartrack-ui-kit'
import { useAllDriversIdTagsQuery } from './useAllDriversIdTagsQuery'
import { renderDriverNameCell, type Driver } from './utils'

const StatusFilterOptions = [
  { label: 'Active Drivers', value: 'active' },
  { label: 'All Drivers', value: 'all' },
  { label: 'Inactive Drivers', value: 'inactive' },
] as const

const IdPassportField = 'id_passport_number'

type StatusFilterValue = (typeof StatusFilterOptions)[number]['value']

const AllDriversTable = () => {
  const history = useHistory()
  const apiRef = useGridApiRef()
  const listDriverPassport = useTypedSelector(getListDriverPassport)
  const defaultDriversTableColumns = useTypedSelector(getDefaultDriversTableColumns)
  const [status, setStatus] = useState<StatusFilterValue>(StatusFilterOptions[0].value)
  const columnHelper = useDataGridColumnHelper<Driver>({ filterMode: 'client' })
  const departmentsOptionsQuery = useDepartmentsOptionsQuery()
  const driverIdTagsQuery = useAllDriversIdTagsQuery()

  const driversQuery = useDriversQuery()

  const totalActiveGroups = useMemo(() => {
    if (driversQuery.data) {
      return driversQuery.data.driverGroupsMapById.size
    }
    return 0
  }, [driversQuery.data])

  const filteredDrivers = useMemo(() => {
    if (driversQuery.data) {
      if (status === 'active') {
        return driversQuery.data.activeDrivers
      }

      if (status === 'inactive') {
        return driversQuery.data.activeAndInactiveDrivers.filter((d) => !d.active)
      }

      return driversQuery.data.activeAndInactiveDrivers
    }
    return []
  }, [driversQuery.data, status])

  const driverColumns = useMemo(() => {
    const columns = [
      columnHelper.string((_, row) => row.name, {
        field: 'driverName',
        headerName: ctIntl.formatMessage({ id: 'Driver Name' }),
        flex: 1,
        renderCell: ({ row }) => renderDriverNameCell({ driver: row }),
      }),
      columnHelper.string((_, row) => row.ownerUsername, {
        field: 'owner',
        headerName: ctIntl.formatMessage({ id: 'Owner' }),
        flex: 1,
      }),
      columnHelper.string(
        (_, row) =>
          row.departmentId
            ? (departmentsOptionsQuery.data?.allById.get(row.departmentId)?.label ??
              null)
            : null,
        {
          field: 'department',
          headerName: ctIntl.formatMessage({ id: 'Department' }),
          flex: 1,
          renderCell: ({ row }) =>
            match(departmentsOptionsQuery)
              .with({ status: 'pending' }, () => (
                <Skeleton
                  variant="text"
                  sx={{ width: '100%', fontSize: '1rem' }}
                />
              ))
              .with({ status: 'error' }, () => null)
              .with({ status: 'success' }, ({ data: { allById: byId } }) =>
                row.departmentId ? (byId.get(row.departmentId)?.label ?? null) : null,
              )
              .exhaustive(),
        },
      ),
      columnHelper.number((_, row) => row.rating, {
        headerName: ctIntl.formatMessage({ id: 'Score' }),
        field: 'rating',
        renderCell: ({ row }) => (
          <StarRating
            color="#FFB400"
            rating={row.rating}
          />
        ),
        width: 80,
        headerAlign: 'left',
        align: 'left',
      }),
      columnHelper.string((_, row) => row.cellPhone, {
        field: 'phone',
        headerName: ctIntl.formatMessage({ id: 'Phone' }),
        flex: 1,
        renderCell: ({ row }) => (
          <OverflowTypography typographyProps={{ variant: 'inherit' }}>
            <FormattedPhone value={row.cellPhone} />
          </OverflowTypography>
        ),
      }),
      columnHelper.valueGetter(
        (_, row) =>
          driverIdTagsQuery.data
            ?.filter((p) => p.driverId === row.id && !p.lost)
            .map((p) => p.idTag) ?? [],
        {
          field: 'id-tags',
          headerName: ctIntl.formatMessage({ id: 'ID Tags' }),
          flex: 1,
          minWidth: 200,
          valueFormatter: (value) => value.join(', '),
          sortComparator: (d1, d2) => d1.join(', ').localeCompare(d2.join(', ')),
          renderCell: ({ value }) => {
            if (value === undefined) {
              return null
            }
            return match(driverIdTagsQuery)
              .with({ status: 'pending' }, () => (
                <Skeleton
                  variant="text"
                  sx={{ width: '100%', fontSize: '1rem' }}
                />
              ))
              .with({ status: 'error' }, () => null)
              .with({ status: 'success' }, () => {
                const idTags = value

                if (idTags.length > 0) {
                  const hasMore = idTags.length > 1
                  return (
                    <Tooltip
                      disableHoverListener={!hasMore}
                      title={hasMore && idTags.join(', ')}
                    >
                      <Stack
                        direction="row"
                        gap={0.5}
                        alignItems="center"
                      >
                        <Chip
                          sx={{ height: '24px' }}
                          label={idTags[0]}
                        />
                        {hasMore && <Typography>{`+${idTags.length - 1}`}</Typography>}
                      </Stack>
                    </Tooltip>
                  )
                }
                return ''
              })
              .exhaustive()
          },
        },
      ),
      columnHelper.string((_, row) => row.licenseNumber, {
        field: 'licenseNumber',
        headerName: ctIntl.formatMessage({ id: 'License' }),
        flex: 1,
      }),
      columnHelper.string((_, row) => row.employeeNumber?.toString(), {
        field: 'employee_number',
        headerName: ctIntl.formatMessage({ id: 'Employee Number' }),
        flex: 1,
      }),
      columnHelper.string((_, row) => row.idPassportNumber, {
        field: IdPassportField,
        headerName: ctIntl.formatMessage({ id: 'Passport' }),
        flex: 1,
      }),
      columnHelper.date({
        field: 'expiration',
        headerName: ctIntl.formatMessage({ id: 'Expiration' }),
        flex: 1,
        valueGetter: (_, row) =>
          row.licenseExpirationTimeUnixMs
            ? new Date(row.licenseExpirationTimeUnixMs)
            : null,
      }),
      columnHelper.string((_, row) => row.licenseCode, {
        field: 'class',
        headerName: ctIntl.formatMessage({ id: 'Class' }),
        flex: 1,
      }),
      columnHelper.string((_, row) => row.gender, {
        field: 'gender',
        headerName: ctIntl.formatMessage({ id: 'Gender' }),
        flex: 1,
      }),
    ] satisfies Array<GridColDef<Driver>>

    if (listDriverPassport === false) {
      return columns.filter((c) => c.field !== IdPassportField)
    }
    return columns
  }, [columnHelper, driverIdTagsQuery, listDriverPassport, departmentsOptionsQuery])

  const columnVisibilityModel:
    | Record<GridColDef<typeof filteredDrivers>['field'], boolean>
    | undefined = useMemo(() => {
    if (defaultDriversTableColumns) {
      return driverColumns.reduce(
        (acc, c) => {
          if (!defaultDriversTableColumns.includes(c.field)) {
            acc[c.field] = false
          }
          return acc
        },
        {} as Record<GridColDef<typeof filteredDrivers>['field'], boolean>,
      )
    } else {
      return undefined
    }
  }, [defaultDriversTableColumns, driverColumns])

  return (
    <UserDataGridWithSavedSettingsOnIDB
      Component={DataGrid}
      dataGridId="sc-drivers-list"
      data-testid="sc-drivers-list"
      apiRef={apiRef}
      columns={driverColumns}
      rows={filteredDrivers}
      pagination
      pageSizeOptions={[25, 50, 100]}
      disableRowSelectionOnClick
      sx={{
        '& .MuiDataGrid-row': { cursor: 'pointer' },
      }}
      initialState={{
        columns: { columnVisibilityModel },
        pagination: {
          paginationModel: { pageSize: 25, page: 0 },
        },
      }}
      loading={driversQuery.status === 'pending'}
      onRowClick={({ row }) => {
        history.push(getDriverDetailsModalMainPath(history.location, row.id))
      }}
      slots={{
        toolbar: KarooToolbar,
        noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
      }}
      slotProps={{
        toolbar: KarooToolbar.createProps({
          slots: {
            searchFilter: { show: true },
            settingsButton: { show: true },
            filterButton: { show: true },
          },
          extraContent: {
            left: (
              <FormControl>
                <InputLabel>{ctIntl.formatMessage({ id: 'Status Filter' })}</InputLabel>
                <Select
                  size="small"
                  value={status}
                  label={ctIntl.formatMessage({
                    id: ctIntl.formatMessage({ id: 'Status Filter' }),
                  })}
                  onChange={(e) => setStatus(e.target.value as StatusFilterValue)}
                >
                  {StatusFilterOptions.map((option) => (
                    <MenuItem
                      key={option.value}
                      value={option.value}
                    >
                      {ctIntl.formatMessage({ id: option.label })}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ),
            middle: (
              <Stack
                direction="row"
                gap={2}
              >
                <TotalDriversToolbar
                  apiRef={apiRef}
                  statusFilter={status}
                />
                <Divider
                  orientation="vertical"
                  variant="middle"
                  flexItem
                />
                <Stats
                  reversed
                  data={[
                    {
                      key: `Total groups`,
                      value: totalActiveGroups,
                    },
                  ]}
                />
              </Stack>
            ),
            right: (
              <GridToolbarExport
                csvOptions={{
                  fileName: ctIntl.formatMessage({ id: 'All Drivers' }),
                }}
                excelOptions={{
                  fileName: ctIntl.formatMessage({ id: 'All Drivers' }),
                }}
                // TODO: need to enable it when print export stable
                printOptions={{ disableToolbarButton: true }}
              />
            ),
          },
        }),
        pagination: { showFirstButton: true, showLastButton: true },
      }}
    />
  )
}

export default AllDriversTable

type TotalDriversToolbarProps = {
  apiRef: React.MutableRefObject<GridApi>
  statusFilter: StatusFilterValue
}

function TotalDriversToolbar({ apiRef, statusFilter }: TotalDriversToolbarProps) {
  const rowCount = useGridSelector(apiRef, gridExpandedRowCountSelector)
  return (
    <Stats
      reversed
      data={[
        {
          key: match(statusFilter)
            .with('all', () => 'Total drivers')
            .with('active', () => 'Total active drivers')
            .with('inactive', () => 'Total inactive drivers')
            .exhaustive(),
          value: rowCount,
        },
      ]}
    />
  )
}
