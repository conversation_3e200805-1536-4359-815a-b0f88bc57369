import {
  Box,
  Button,
  Stack,
  Typography,
  type KarooUiInternalTheme,
  type SxProps,
  type SystemStyleObject,
} from '@karoo-ui/core'
import DownloadingIcon from '@mui/icons-material/Downloading'
import VideoCameraBackOutlinedIcon from '@mui/icons-material/VideoCameraBackOutlined'
import VideocamOffOutlinedIcon from '@mui/icons-material/VideocamOffOutlined'
import { match } from 'ts-pattern'

import type { EventHandlerBranded } from 'src/hooks/useEventHandler'
import type { VehicleVideoSourceId } from 'src/modules/vision/api/types'
import { ctIntl } from 'src/util-components/ctIntl'

import { useCameraFootageModalMetaData } from '../../CameraFootageModalContext'

export type CameraStatus =
  | 'video_available'
  | 'video_not_available'
  | 'requested_pending'
  | 'requested_retrieving'
  | 'not_requested'
  | 'request_failed_timeout'
  | 'request_failed_no_footage'

type Props = {
  children: React.ReactNode
  sourceId: VehicleVideoSourceId
  isCompact?: boolean
  cameraStatus: CameraStatus
  isFocusedCameraInSidebar?: boolean
  isStaticRatio?: boolean
  onRequestFootageButtonClick: EventHandlerBranded<[camera: VehicleVideoSourceId]>
  onRetryButtonClick: EventHandlerBranded<[camera: VehicleVideoSourceId]>
}

// TODO: This component will also handle other statuses,
// e.g., showing a box when the user doesn't have data to start the livestream, etc.
const CameraStreamPlaceholder = ({
  children,
  sourceId,
  isCompact = false,
  cameraStatus,
  isFocusedCameraInSidebar,
  onRequestFootageButtonClick,
  onRetryButtonClick,
  isStaticRatio = false,
}: Props) => {
  const { activeSourcesIds } = useCameraFootageModalMetaData()
  if (isFocusedCameraInSidebar) {
    return (
      <PlaceholderContainer
        sourceId={sourceId}
        isCompact={true}
        isStaticRatio={isStaticRatio}
        sx={({ palette }) => ({ border: `2px solid ${palette.primary.main}` })}
        isFocused
      />
    )
  }

  if (cameraStatus === 'video_available') {
    return isStaticRatio ? (
      <Box sx={{ aspectRatio: '16 / 9', height: 'auto' }}>{children}</Box>
    ) : (
      children
    )
  }

  return (
    <PlaceholderContainer
      sourceId={sourceId}
      isCompact={isCompact}
      isStaticRatio={isStaticRatio}
    >
      {match(cameraStatus)
        .with('video_not_available', () => (
          <>
            <VideocamOffOutlinedIcon
              sx={(theme) => ({
                fontSize: '38px',
                color: theme.palette.grey[600],
              })}
            />
            <PlaceholderMessage
              message={ctIntl.formatMessage({
                id: 'cameraFootageModal.placeholder.message.noFootage',
              })}
            />
          </>
        ))
        .with('requested_pending', () => (
          <>
            <WhiteBackdrop />
            <DownloadingIcon
              sx={(theme) => ({
                fontSize: '38px',
                color: theme.palette.primary.light,
                marginBottom: 0.5,
                zIndex: 1,
              })}
            />
            <Typography
              variant="body2"
              sx={{ color: 'secondary.main', zIndex: 1 }}
            >
              {`${ctIntl.formatMessage({
                id: 'cameraFootageModal.placeholder.message.requested',
              })}...`}
            </Typography>
            {!activeSourcesIds.has(sourceId) && (
              <Typography
                variant="caption"
                sx={{ color: 'secondary.main', zIndex: 1 }}
              >
                {`(${ctIntl.formatMessage({
                  id: 'cameraFootageModal.placeholder.message.waitingForOnlineCameras',
                })})`}
              </Typography>
            )}
          </>
        ))
        .with('requested_retrieving', () => (
          <>
            <WhiteBackdrop />
            <DownloadingIcon
              sx={(theme) => ({
                fontSize: '38px',
                color: theme.palette.primary.main,
                marginBottom: 0.5,
                zIndex: 1,
              })}
            />
            <PlaceholderMessage
              sx={{
                color: 'secondary.main',
                zIndex: 1,
              }}
              message={`${ctIntl.formatMessage({
                id: 'cameraFootageModal.placeholder.message.retrieving',
              })}...`}
            />
          </>
        ))
        .with('not_requested', () => (
          <Stack
            direction="column"
            spacing={2}
            alignItems="center"
            justifyContent="center"
            sx={{ width: '100%' }}
          >
            <PlaceholderMessage
              message={ctIntl.formatMessage({
                id: 'cameraFootageModal.placeholder.message.not_requested',
              })}
            />
            <Button
              variant="outlined"
              color="primary"
              startIcon={<VideoCameraBackOutlinedIcon />}
              sx={{
                color: 'common.white',
                borderColor: 'common.white',
              }}
              onClick={() => onRequestFootageButtonClick(sourceId)}
            >
              {ctIntl.formatMessage({ id: 'Request' })}
            </Button>
          </Stack>
        ))
        .with('request_failed_timeout', () => (
          <Stack
            direction="column"
            spacing={2}
            alignItems="center"
            justifyContent="center"
            sx={{ width: '100%' }}
          >
            <PlaceholderMessage message={ctIntl.formatMessage({ id: 'Error' })} />
            <Button
              variant="outlined"
              color="primary"
              startIcon={<DownloadingIcon />}
              sx={{
                color: 'common.white',
                borderColor: 'common.white',
              }}
              onClick={() => onRetryButtonClick(sourceId)}
            >
              {ctIntl.formatMessage({
                id: 'Retry',
              })}
            </Button>
          </Stack>
        ))
        .with('request_failed_no_footage', () => (
          <Stack
            direction="column"
            spacing={2}
            alignItems="center"
            justifyContent="center"
            sx={{ width: '100%' }}
          >
            <PlaceholderMessage message={ctIntl.formatMessage({ id: 'Error' })} />
            <Typography color="error">
              {ctIntl.formatMessage({ id: 'No footage' })}
            </Typography>
          </Stack>
        ))
        .exhaustive()}
    </PlaceholderContainer>
  )
}

export default CameraStreamPlaceholder

export const PlaceholderContainer = ({
  children,
  sourceId,
  isCompact = false,
  isFocused = false,
  isStaticRatio = false,
  sx,
}: Pick<Props, 'sourceId' | 'isCompact' | 'isStaticRatio'> & {
  children?: React.ReactNode
  sx?: SxProps<KarooUiInternalTheme>
  isFocused?: boolean
}) => (
  <Box
    sx={(theme) => {
      const defaultStyles: SystemStyleObject<KarooUiInternalTheme> = {
        backgroundColor: 'black',
        borderRadius: 2,
        px: 2,
        py: 2,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        color: 'white',
        position: 'relative',
        width: '100%',
        minHeight: isCompact ? '90px' : 'auto',
        ...(isStaticRatio
          ? { aspectRatio: '16/9', height: 'auto' }
          : { height: '100%' }),
        ...(isCompact && {
          boxSizing: 'border-box',
          border: '2px solid transparent',
          '&:hover': {
            border: `2px solid ${theme.palette.primary.light}`,
          },
        }),
      }

      const additionalStyles = typeof sx === 'function' ? sx(theme) : (sx ?? {})

      return {
        ...defaultStyles,
        ...additionalStyles,
      }
    }}
  >
    <Typography
      sx={{
        position: 'absolute',
        top: '8px',
        left: '18px',
        mb: 2,
      }}
    >
      {`${ctIntl.formatMessage({ id: 'Camera' })} ${sourceId}${
        isFocused ? ` - ${ctIntl.formatMessage({ id: 'Selected' })}` : ''
      }`}
    </Typography>
    {!isCompact && (
      <Stack sx={{ alignItems: 'center', justifyContent: 'center', height: '100%' }}>
        {children}
      </Stack>
    )}
  </Box>
)

export const PlaceholderMessage = ({
  message,
  sx,
}: {
  message: string
  sx?: SxProps<KarooUiInternalTheme>
}) => (
  <Typography
    variant="caption"
    sx={(theme) => {
      const defaultStyles: SystemStyleObject<KarooUiInternalTheme> = {
        marginTop: 2,
        color: theme.palette.grey[300],
        textAlign: 'center',
        mb: 2,
      }

      const additionalStyles = typeof sx === 'function' ? sx(theme) : (sx ?? {})

      return {
        ...defaultStyles,
        ...additionalStyles,
      }
    }}
  >
    {message}
  </Typography>
)

const WhiteBackdrop = () => (
  <Box
    sx={{
      position: 'absolute',
      top: '40px',
      width: '100%',
      height: 'calc(100% - 80px)',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
    }}
  />
)
