import { useMemo } from 'react'
import {
  Autocomplete,
  Box,
  DatePicker,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import type { DateTime } from 'luxon'

import type { VehicleId } from 'api/types'
import { ctIntl } from 'src/util-components/ctIntl'

type VehicleOption = {
  value: VehicleId
  label: string
}

type DialogHeaderProps = {
  onCloseCameraFootageModal: () => void
  selectedVehicleId: VehicleId | null
  vehicleOptions: Array<VehicleOption> | 'LOADING'
  onVehicleChange: (vehicleId: VehicleId) => void
  selectedDate: DateTime
  onDateChange: (date: Date) => void
}

const DialogHeader = ({
  onCloseCameraFootageModal,
  selectedVehicleId,
  vehicleOptions,
  onVehicleChange,
  selectedDate,
  onDateChange,
}: DialogHeaderProps) => {
  const currentVehicle = useMemo(
    () =>
      vehicleOptions === 'LOADING'
        ? null
        : (vehicleOptions.find((v) => v.value === selectedVehicleId) ?? null),
    [vehicleOptions, selectedVehicleId],
  )

  const options = useMemo(
    () => (vehicleOptions === 'LOADING' ? [] : vehicleOptions),
    [vehicleOptions],
  )

  return (
    <Box
      display="flex"
      justifyContent="space-between"
      alignItems="center"
      sx={{ mx: -2, px: 2, pb: 2, borderBottom: 1, borderColor: 'divider' }}
    >
      <Stack
        flexDirection="row"
        gap={1}
      >
        <Typography
          variant="h6"
          fontWeight={500}
          sx={{ whiteSpace: 'nowrap' }}
        >
          {ctIntl.formatMessage({ id: 'Camera footage history for' })}
        </Typography>
        <Autocomplete
          data-testid="CameraFootageModalDialog-vehicle-select"
          size="small"
          sx={{ width: '220px', minWidth: 'min-content' }}
          value={currentVehicle}
          loading={vehicleOptions === 'LOADING'}
          disableClearable={vehicleOptions !== 'LOADING'}
          onChange={(_, selectedOption) => {
            if (selectedOption) {
              onVehicleChange(selectedOption.value)
            }
          }}
          options={options}
          renderInput={(params) => (
            <TextField
              {...params}
              data-testid="CameraFootageModalDialog-vehicle-select-input"
              label={ctIntl.formatMessage({ id: 'Vehicle' })}
              required
            />
          )}
        />
        <DatePicker
          label={ctIntl.formatMessage({ id: 'Date' })}
          disableFuture
          value={selectedDate}
          onChange={(newValue) => newValue && onDateChange(newValue.toJSDate())}
          slotProps={{
            textField: {
              ['data-testid' as any]: 'CameraFootageModalDialog-date-picker-input',
            },
          }}
        />
      </Stack>
      <IconButton onClick={onCloseCameraFootageModal}>
        <CloseIcon />
      </IconButton>
    </Box>
  )
}

export default DialogHeader
