import { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import {
  Box,
  Checkbox,
  CircularProgressDelayedAbsolute,
  Dialog,
  FormControlLabel,
  Stack,
  Typography,
  type KarooUiInternalTheme,
  type SxProps,
} from '@karoo-ui/core'
import GridViewOutlinedIcon from '@mui/icons-material/GridViewOutlined'
import ViewSidebarOutlinedIcon from '@mui/icons-material/ViewSidebarOutlined'
import useResizeObserver from '@react-hook/resize-observer'
import { DateTime } from 'luxon'
import { useHistory } from 'react-router'
import { match } from 'ts-pattern'

import useTimelineEventsRawQuery, {
  type FetchTimelineEventsRawQuery,
} from 'api/timeline/useTimelineEventsRawQuery'
import useTimelineEventsUIQuery from 'api/timeline/useTimelineEventsUIQuery'
import type { TerminalEventTypeCode, VehicleId } from 'api/types'
import type { getVehicles } from 'duxs/vehicles'
import { useDeepCompareMemo } from 'src/hooks'
import { useEffectEvent, useEventHandler } from 'src/hooks/useEventHandler'
import { useLatestExceptDuringRender } from 'src/hooks/useLatestExceptDuringRender'
import { useStructuralSharedUrlValue } from 'src/hooks/useStructuralSharedUrlValue'
import {
  useAlertsLogQuery,
  type FetchAlertsLog,
} from 'src/modules/alerts/api/useAlertsLogQuery'
import {
  useVehiclesGroupedEventTypesQuery,
  type UseVehiclesGroupedEventTypesQueryData,
} from 'src/modules/api/useEventTypes'
import { useVehiclesQuery } from 'src/modules/api/useVehiclesQuery'
import DriverWithChip from 'src/modules/app/components/driverChip/DriverWithChip'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'

import type { VehicleVideoSourceId } from '../api/types'
import { useFetchCameraDetailQuery } from '../VisionEvents/api/vehiclesQueries'
import { useVisionFootageBlocksPollingQuery } from './api/useVisionFootageBlocksQuery'
import { CameraFootageModalMetaDataProvider } from './CameraFootageModalContext'
import RecordedFootageViewer from './components/CamerasContainer'
import {
  findFootagesWithTimestamp,
  findTriggeredGenericSensorAlertEventsWithTimestamp,
  type SimplifiedGenericSensorAlertEvent,
} from './components/CamerasContainer/utils'
import CamerasVideoControl from './components/CamerasVideoControl'
import { getPeriodsWithoutFootage } from './components/CamerasVideoControl/timeUtils'
import DialogHeader from './components/DialogHeader'
import { cameraFootageModalWithVehicleAndDataEventHandler } from './components/event-handler'
import type {
  CameraFootageModalWithVehicleEvent,
  CameraFootageModalWithVehicleState,
} from './components/event-handler/types'
import { getCurrentTimelineViewMillisMeta } from './components/event-handler/utils'
import MapArea from './components/MapArea'
import type { CameraFootageModalSearchParams, SendToCoachingParams } from './schema'
import type {
  FetchVehicleCamerasDetailSuccessQuery,
  FetchVisionFootageBlocksSuccessQueryWithData,
  TimelineEventsUISuccessQueryWithTrips,
  UnavailableFootageTimeRanges,
  VideoAsset,
  VideoElementsRefsMap,
  ViewingVideoType,
} from './types'
import { getCameraFootageModalMainPath, getMergedVideoPeriods } from './utils'

type Props = {
  onClose: () => void
  params: CameraFootageModalSearchParams
}

const getRawParamsWithDefaults = (rawParams: CameraFootageModalSearchParams) => {
  const selectedVehicleId = rawParams.selectedVehicleId
  const sendToCoachingParams = rawParams.sendToCoachingParams
  const selectedDate = rawParams.selectedDate ?? new Date().toISOString()
  const initialSelectedFootage = rawParams.initialSelectedFootage ?? null

  return {
    selectedVehicleId,
    selectedDate,
    sendToCoachingParams,
    initialSelectedFootage,
  } satisfies CameraFootageModalSearchParams
}

const CameraFootageModal = ({ onClose, params: rawParams }: Props) => {
  const history = useHistory()
  const rawStableReferenceParams = useStructuralSharedUrlValue({
    globalUniqueId: 'requestFootageParams',
    value: rawParams,
  })
  const rawParamsWithDefaults = useMemo(
    () => getRawParamsWithDefaults(rawStableReferenceParams),
    [rawStableReferenceParams],
  )

  const selectedVehicleId = rawParamsWithDefaults.selectedVehicleId ?? null
  const sendToCoachingParams = rawParamsWithDefaults.sendToCoachingParams
  const selectedDate = useMemo(
    () => DateTime.fromJSDate(new Date(rawParamsWithDefaults.selectedDate)),
    [rawParamsWithDefaults.selectedDate],
  )
  const initialSelectedFootage = useMemo(
    () =>
      rawParamsWithDefaults.initialSelectedFootage
        ? {
            camera: rawParamsWithDefaults.initialSelectedFootage
              .camera as VehicleVideoSourceId,
            startTime: new Date(rawParamsWithDefaults.initialSelectedFootage.startTime),
          }
        : null,
    [rawParamsWithDefaults.initialSelectedFootage],
  )

  const private_setMapSearchParams = useEventHandler(
    (newSearchParams: Partial<CameraFootageModalSearchParams>) => {
      history.push(
        getCameraFootageModalMainPath(history.location, {
          ...rawParamsWithDefaults,
          ...newSearchParams,
        }),
      )
    },
  )

  const setSelectedDate = useEventHandler((newDate: Date) => {
    private_setMapSearchParams({ selectedDate: newDate.toISOString() })
  })

  const setSelectedVehicleId = useEventHandler((newVehicleId: VehicleId) => {
    private_setMapSearchParams({ selectedVehicleId: newVehicleId })
  })

  // Prefetch vehicles query
  // TODO: refactor sub components to use the data returned from this hook
  const vehiclesQuery = useVehiclesQuery()

  const vehicles = useMemo(
    () => vehiclesQuery.data?.vehicles ?? [],
    [vehiclesQuery.data],
  )

  const vehicleOptions = useMemo(
    () =>
      vehiclesQuery.status === 'pending'
        ? ('LOADING' as const)
        : Array_filterMap(vehiclesQuery.data.vehicles, (vehicle, { RemoveSymbol }) => {
            if (!vehicle.isCamera) {
              return RemoveSymbol
            }
            return {
              value: vehicle.id,
              label: vehicle.registration,
            }
          }),
    [vehiclesQuery],
  )

  return (
    <Dialog
      open
      onClose={onClose}
      data-testid="CameraFootageModalDialog"
      sx={({ palette: { grey } }) => ({
        '& .MuiDialog-paper': {
          maxWidth: '1900px',
          width: '94%',
          maxHeight: 'unset',
          height: '94%',
          px: 2,
          py: 3,
          display: 'grid',
          gridTemplateRows: 'min-content auto',
          overflowY: 'auto',
          background: grey['100'],
        },
      })}
    >
      <DialogHeader
        onCloseCameraFootageModal={onClose}
        selectedVehicleId={selectedVehicleId}
        vehicleOptions={vehicleOptions}
        onVehicleChange={setSelectedVehicleId}
        selectedDate={selectedDate}
        onDateChange={setSelectedDate}
      />
      {selectedVehicleId ? (
        <Content
          selectedVehicleId={selectedVehicleId}
          selectedDate={selectedDate}
          initialSelectedFootage={initialSelectedFootage}
          sendToCoachingParams={sendToCoachingParams}
          vehicles={vehicles}
        />
      ) : (
        <Stack
          justifyContent="center"
          alignItems="center"
          gap={2}
        >
          <IntlTypography msgProps={{ id: 'vision.footage.modal.nodata' }} />
          <img src="https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/vision-camera-footage-modal-no-data.svg" />
        </Stack>
      )}
    </Dialog>
  )
}

function Content({
  selectedVehicleId,
  selectedDate,
  vehicles,
  ...rest
}: {
  selectedVehicleId: VehicleId
  selectedDate: DateTime
  vehicles: ReturnType<typeof getVehicles>
  initialSelectedFootage: { camera: VehicleVideoSourceId; startTime: Date } | null
  sendToCoachingParams: SendToCoachingParams
}) {
  const [viewingVideoType, setViewingVideoType] = useState<ViewingVideoType>('side_bar')
  const [isMapCheckboxChecked, setIsMapCheckboxChecked] = useState(false)
  const [isSensorsCheckboxChecked, setIsSensorsCheckboxChecked] = useState(false)

  const selectedDateTsRange = useMemo(
    () => ({
      startTs: selectedDate.startOf('day').toJSDate(),
      endTs: selectedDate.endOf('day').toJSDate(),
    }),
    [selectedDate],
  )

  const timelineEventsUIQuery = useTimelineEventsUIQuery({
    vehicleId: selectedVehicleId,
    ...selectedDateTsRange,
  })

  // poll the latest footage blocks
  const visionFootageBlocksQuery = useVisionFootageBlocksPollingQuery({
    vehicleId: selectedVehicleId,
    ...selectedDateTsRange,
  })

  const timelineEventsRawQuery = useTimelineEventsRawQuery({
    vehicleId: selectedVehicleId,
    ...selectedDateTsRange,
  })

  const vehicleCameraDetailQuery = useFetchCameraDetailQuery({
    vehicleId: selectedVehicleId,
  })

  const vehicleDetails = useMemo(
    () => vehicles.find((v) => v.id === selectedVehicleId),
    [vehicles, selectedVehicleId],
  )

  const vehiclesGroupedEventTypesQuery = useVehiclesGroupedEventTypesQuery()

  const alertsQuery = useAlertsLogQuery({
    startTs: selectedDate.startOf('day').toJSDate(),
    endTs: selectedDate.endOf('day').toJSDate(),
    vehicleId: selectedVehicleId as `${number}`,
  })

  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateRows: 'min-content auto',
        overflowY: 'auto',
      }}
    >
      <Stack
        mb={0.5}
        direction="row"
        alignItems="center"
      >
        <Box sx={{ flex: 6 }}>
          {!!vehicleDetails && (
            <DriverWithChip
              driverName={{
                name: vehicleDetails.driverName.name,
                status: vehicleDetails.driverName.status,
              }}
              linkingMethod={{
                type: vehicleDetails.driverName.linkage_type_enum,
              }}
              picture={vehicleDetails.driverName.logo_image_base64}
              driverId={vehicleDetails.driverName.client_driver_id}
            />
          )}
        </Box>
        <Stack
          sx={{ flex: 4, /* Minimum space between two groups */ gap: 1 }}
          direction="row"
          justifyContent="space-between"
        >
          <Stack
            direction="row"
            gap={1}
          >
            <FormControlLabel
              control={
                <Checkbox
                  data-testid="CameraFootageModalDialog-map-checkbox"
                  size="small"
                  value={isMapCheckboxChecked}
                  onChange={(e) => setIsMapCheckboxChecked(e.target.checked)}
                />
              }
              label={ctIntl.formatMessage({ id: 'Map' })}
            />
            <FormControlLabel
              control={
                <Checkbox
                  data-testid="CameraFootageModalDialog-sensor-checkbox"
                  size="small"
                  value={isSensorsCheckboxChecked}
                  onChange={(e) => setIsSensorsCheckboxChecked(e.target.checked)}
                />
              }
              label={ctIntl.formatMessage({ id: 'Sensors' })}
            />
          </Stack>
          <Stack
            direction="row"
            gap={1}
            alignItems="center"
          >
            <Typography>{ctIntl.formatMessage({ id: 'Camera view' })}</Typography>
            <ViewSidebarOutlinedIcon
              sx={sharedIconStyles({ isSelected: viewingVideoType === 'side_bar' })}
              onClick={() => setViewingVideoType('side_bar')}
            />
            <GridViewOutlinedIcon
              sx={sharedIconStyles({ isSelected: viewingVideoType === 'grid' })}
              onClick={() => setViewingVideoType('grid')}
            />
          </Stack>
        </Stack>
      </Stack>

      {(() => {
        if (
          timelineEventsUIQuery.status === 'error' ||
          visionFootageBlocksQuery.status === 'error' ||
          timelineEventsRawQuery.status === 'error' ||
          vehicleCameraDetailQuery.status === 'error' ||
          alertsQuery.status === 'error'
        ) {
          return null
        }

        if (
          timelineEventsUIQuery.status === 'pending' ||
          visionFootageBlocksQuery.status === 'pending' ||
          timelineEventsRawQuery.status === 'pending' ||
          vehicleCameraDetailQuery.status === 'pending' ||
          alertsQuery.status === 'pending' ||
          vehiclesGroupedEventTypesQuery.status === 'pending'
        ) {
          return <CircularProgressDelayedAbsolute />
        }

        if (
          !timelineEventsUIQuery.data.trips ||
          timelineEventsUIQuery.data.trips.length === 0 ||
          !visionFootageBlocksQuery.data ||
          !timelineEventsRawQuery.data ||
          !vehicleCameraDetailQuery.data ||
          !vehiclesGroupedEventTypesQuery.data ||
          alertsQuery.data.length === 0
        ) {
          // FIXME: better message and intl
          return 'There are no trips for selected day'
        }

        return (
          <FootageContentWithData
            // VERY IMPORTANT to reset state and some other state whenever vehicleId or selectedDate changes
            // If we didn't, we could have stuff like the timeline time period not being reset when you change date and other things
            key={`${selectedVehicleId}-${selectedDate.toISOTime()}`}
            timelineEventsUISuccessQuery={{
              ...timelineEventsUIQuery,
              data: timelineEventsUIQuery.data,
            }}
            visionFootageBlocksSuccessQuery={{
              ...visionFootageBlocksQuery,
              data: visionFootageBlocksQuery.data,
            }}
            timelineEventsRawData={timelineEventsRawQuery.data}
            vehicleId={selectedVehicleId}
            vehicleCameraDetailSuccessQuery={{
              ...vehicleCameraDetailQuery,
              data: vehicleCameraDetailQuery.data,
            }}
            viewingVideoType={viewingVideoType}
            isMapCheckboxChecked={isMapCheckboxChecked}
            isSensorsCheckboxChecked={isSensorsCheckboxChecked}
            vehiclesGroupedEventTypesQueryData={vehiclesGroupedEventTypesQuery.data}
            alertsQueryData={alertsQuery.data}
            selectedDate={selectedDate}
            {...rest}
          />
        )
      })()}
    </Box>
  )
}

function FootageContentWithData({
  visionFootageBlocksSuccessQuery,
  timelineEventsUISuccessQuery,
  vehicleCameraDetailSuccessQuery,
  timelineEventsRawData,
  vehicleId,
  selectedDate,
  viewingVideoType,
  isMapCheckboxChecked,
  isSensorsCheckboxChecked,
  initialSelectedFootage,
  sendToCoachingParams,
  vehiclesGroupedEventTypesQueryData,
  alertsQueryData,
}: {
  visionFootageBlocksSuccessQuery: FetchVisionFootageBlocksSuccessQueryWithData
  timelineEventsUISuccessQuery: TimelineEventsUISuccessQueryWithTrips
  vehicleCameraDetailSuccessQuery: FetchVehicleCamerasDetailSuccessQuery
  timelineEventsRawData: NonNullable<FetchTimelineEventsRawQuery.Return>
  vehicleId: VehicleId
  selectedDate: DateTime
  sendToCoachingParams: SendToCoachingParams
  viewingVideoType: ViewingVideoType
  isMapCheckboxChecked: boolean
  isSensorsCheckboxChecked: boolean
  initialSelectedFootage: { camera: VehicleVideoSourceId; startTime: Date } | null
  vehiclesGroupedEventTypesQueryData: UseVehiclesGroupedEventTypesQueryData
  alertsQueryData: FetchAlertsLog.AllAlertsReturn
}) {
  const isProcessingZoomScrollRef = useRef(false)
  const timelineDraggingRef = useRef<{
    startClickClientX: number
    currentDraggingClientX: number
  } | null>(null)
  const bottomScrollBarDraggingRef = useRef<{
    currentDraggingClientX: number
  } | null>(null)
  const documentMouseMetaRef = useRef<{
    isMouseDown: boolean
  }>({ isMouseDown: false })
  const timelineAutoPlayIntervalIdRef = useRef<number | null>(null)

  // Calculate the combined time range and offsets
  const { combinedTimeRange, timelineOffsets } = useMemo(() => {
    const startDateTimeMillis = selectedDate.startOf('day').toMillis()
    const endDateTimeMillis = selectedDate.endOf('day').toMillis()
    const timelineStartMillis = DateTime.fromJSDate(
      timelineEventsUISuccessQuery.data.dayStart,
    ).toMillis()
    const timelineEndMillis = DateTime.fromJSDate(
      timelineEventsUISuccessQuery.data.dayEnd,
    ).toMillis()

    // we do not compare the milliseconds, only hour, min and sec
    // if the end timeline datetime is 23:59:59, we would regard it as the same as the end datetime
    const isTimelineEndSameAsEndDateTime =
      timelineEndMillis === endDateTimeMillis ||
      (timelineEndMillis < endDateTimeMillis &&
        DateTime.fromMillis(timelineEndMillis).toFormat('tt') ===
          DateTime.fromMillis(endDateTimeMillis).toFormat('tt'))

    const realEndDateTimeMillis = isTimelineEndSameAsEndDateTime
      ? timelineEndMillis
      : endDateTimeMillis

    const combinedDuration = realEndDateTimeMillis - startDateTimeMillis

    // Calculate offsets as percentages of the combined timeline
    const timelineStartOffset =
      timelineStartMillis > startDateTimeMillis
        ? ((timelineStartMillis - startDateTimeMillis) / combinedDuration) * 100
        : 0

    const timelineEndOffset = isTimelineEndSameAsEndDateTime
      ? 0
      : ((endDateTimeMillis - timelineEndMillis) / combinedDuration) * 100

    return {
      combinedTimeRange: {
        startMillis: startDateTimeMillis,
        endMillis: realEndDateTimeMillis,
      },
      timelineOffsets: {
        startOffset: timelineStartOffset,
        endOffset: timelineEndOffset,
      },
    }
  }, [
    selectedDate,
    timelineEventsUISuccessQuery.data.dayStart,
    timelineEventsUISuccessQuery.data.dayEnd,
  ])

  const videoAvailabilityPeriods = useMemo(
    () => getMergedVideoPeriods(visionFootageBlocksSuccessQuery.data.footage.array),
    [visionFootageBlocksSuccessQuery.data.footage.array],
  )

  const unavailableFootageTimeRanges: UnavailableFootageTimeRanges = useMemo(
    () =>
      getPeriodsWithoutFootage({
        footageData: visionFootageBlocksSuccessQuery.data.footage.array,
        overallStartUnixMs: combinedTimeRange.startMillis,
        overallEndUnixMs: combinedTimeRange.endMillis,
      }),
    [
      combinedTimeRange.endMillis,
      combinedTimeRange.startMillis,
      visionFootageBlocksSuccessQuery.data.footage.array,
    ],
  )

  // eslint-disable-next-line sonarjs/hook-use-state
  const [state, setState_] = useState((): CameraFootageModalWithVehicleState => {
    const initialCurrentTimelineIndicatorUnixMillis = combinedTimeRange.startMillis

    return {
      // In case we have a footage block right at the start of the day,
      // we want to focus on the first camera immediately
      focusedCameraSource: {
        id: initialSelectedFootage
          ? initialSelectedFootage.camera
          : ('1' as VehicleVideoSourceId),
        changedBy: 'code_indirectly',
      },
      playbackStatus: {
        isLoading: false,
        playingMeta: { isPlaying: false, pauseCause: { type: 'no_cause' } },
      },
      isMuted: true,
      requestFootageSection: null,
      currentTimelineIndicatorUnixMillis: initialCurrentTimelineIndicatorUnixMillis,
      isSliderUpdatingIndicator: false,
      selectedFootageBlock: null,
      hoveredFootageBlockId: null,
      timelineZoomLevel: 1,
    }
  })

  const {
    requestFootageSection,
    currentTimelineIndicatorUnixMillis,
    isSliderUpdatingIndicator,
    selectedFootageBlock,
    hoveredFootageBlockId,
    timelineZoomLevel,
    playbackStatus,
    isMuted,
    focusedCameraSource,
  } = state

  const latestStateRef = useLatestExceptDuringRender(state)

  const selectedFootageBlockWithData = selectedFootageBlock
    ? (visionFootageBlocksSuccessQuery.data.footage.byId.get(selectedFootageBlock.id) ??
      null)
    : null

  const hoveredFootageBlock = hoveredFootageBlockId
    ? (visionFootageBlocksSuccessQuery.data.footage.byId.get(hoveredFootageBlockId) ??
      null)
    : null

  const timelineRef = useRef<HTMLDivElement | null>(null)

  const { activeSources, cameraChannelsSources } = vehicleCameraDetailSuccessQuery.data

  const activeSourcesIds = useMemo(
    () => new Set(activeSources.map(({ id }) => id)),
    [activeSources],
  )

  // check if the current time indicator is within the available footage
  const isCurrentTimeIndicatorWithinAvailableFootage = useMemo(() => {
    for (const period of videoAvailabilityPeriods) {
      if (
        period.start <= currentTimelineIndicatorUnixMillis &&
        period.end >= currentTimelineIndicatorUnixMillis
      ) {
        return true
      }
    }
    return false
  }, [videoAvailabilityPeriods, currentTimelineIndicatorUnixMillis])

  // filter out the generic sensor/alert events, it would be used to
  // 1. get the triggered  event around the current time
  // 2. display the events in the timeline slider
  // we only extract the fields we needed to save the memory, add it if needed
  const filteredGenericSensorAlertEvents = useMemo(() => {
    const result: Array<SimplifiedGenericSensorAlertEvent> = []

    const { vehicleVisionEventTypesIntlMetaByCode, getEventLabel } =
      vehiclesGroupedEventTypesQueryData

    // handle the generic sensor events
    for (const item of timelineEventsRawData.events) {
      const uniqueEventTypesToAdd: Map<
        TerminalEventTypeCode,
        SimplifiedGenericSensorAlertEvent
      > = new Map()

      for (const action of item.actions) {
        const eventIcon = action.icon
        const actionLabel = getEventLabel({
          eventType: eventIcon,
          eventTypeDescription: item.eventTypeDescription,
        })
        match(action)
          .with({ sourceType: 'sensor' }, () => {
            uniqueEventTypesToAdd.set(eventIcon, {
              id: item.id,
              timestampDate: item.timestampDate,
              eventLabel: actionLabel,
              type: 'sensor',
            })
          })
          .with({ sourceType: 'video' }, () => {
            uniqueEventTypesToAdd.set(eventIcon, {
              id: item.id,
              timestampDate: item.timestampDate,
              eventLabel: actionLabel,
              type: 'vision',
            })
          })
          .otherwise(() => {
            // We are not really 100% sure the sourceType is either 'video' or 'sensor'. Should not be but let's not crash
            // do nothing
          })
      }

      // In case vision does not show up on event actions, try to get it from the eventType
      const visionEventTranslationMeta = vehicleVisionEventTypesIntlMetaByCode.get(
        item.eventType,
      )
      if (visionEventTranslationMeta) {
        uniqueEventTypesToAdd.set(item.eventType, {
          id: item.id,
          timestampDate: item.timestampDate,
          eventLabel: getEventLabel(item),
          type: 'vision',
        })
      }

      const eventsObjects = Array.from(uniqueEventTypesToAdd.values())
      if (eventsObjects.length > 0) {
        result.push(...eventsObjects)
      }
    }

    for (const alert of alertsQueryData) {
      result.push({
        id: alert.id,
        type: 'alert',
        timestampDate: alert.eventDate.raw,
        eventLabel: alert.triggerDescription,
        eventName: alert.groupDescription,
      })
    }

    return result
  }, [
    alertsQueryData,
    timelineEventsRawData.events,
    vehiclesGroupedEventTypesQueryData,
  ])

  // Generate new generic sensor events around the current time when it's within available footage.
  // Changes often. Prefer using the deeply compared version instead.
  const triggeredGenericSensorEventsBasedOnCurrentTime_UNSTABLE = useMemo(() => {
    // since triggered events are only displayed in the main video
    // so we only do the calculation when the current time indicator is within the available footage
    if (isCurrentTimeIndicatorWithinAvailableFootage) {
      return findTriggeredGenericSensorAlertEventsWithTimestamp({
        genericSensorAlertEvents: filteredGenericSensorAlertEvents,
        timeInMillis: currentTimelineIndicatorUnixMillis,
      })
    }
    return null
  }, [
    currentTimelineIndicatorUnixMillis,
    filteredGenericSensorAlertEvents,
    isCurrentTimeIndicatorWithinAvailableFootage,
  ])

  // We use deep compare memo to really make sure triggeredGenericSensorEventsBasedOnCurrentTime __actually__ changes by deep comparison.
  // currentTimelineIndicatorUnixMillis can change a lot, which causes triggeredGenericSensorEventsBasedOnCurrentTime_UNSTABLE
  // to have a new reference very often. Now we can use this value more safely on useMemo and useEffect.
  const triggeredGenericSensorEventsBasedOnCurrentTime = useDeepCompareMemo(
    () => triggeredGenericSensorEventsBasedOnCurrentTime_UNSTABLE,
    [triggeredGenericSensorEventsBasedOnCurrentTime_UNSTABLE],
  )

  const triggeredGenericSensorEvents =
    (triggeredGenericSensorEventsBasedOnCurrentTime ?? []).length > 0
      ? triggeredGenericSensorEventsBasedOnCurrentTime
      : null

  // Generate new video assets based on current data. Changes often.
  // Prefer using the deeply compared version instead.
  const videoAssetsBasedOnCurrentTimelineIndicatorTimeMap_UNSTABLE = useMemo(
    () =>
      findFootagesWithTimestamp(
        visionFootageBlocksSuccessQuery.data.footage.array,
        currentTimelineIndicatorUnixMillis,
      ),
    [
      visionFootageBlocksSuccessQuery.data.footage.array,
      currentTimelineIndicatorUnixMillis,
    ],
  )
  // We use deep compare memo to really make sure videoAssetsBasedOnCurrentTimelineIndicatorTimeMap __actually__ changes by deep comparison.
  // currentTimelineIndicatorUnixMillis can change a lot, which causes videoAssetsBasedOnCurrentTimelineIndicatorTimeMap_UNSTABLE to have a new reference very often.
  // Now we can use this value more safely on useMemo and useEffect.
  const videoAssetsBasedOnCurrentTimelineIndicatorTimeMap = useDeepCompareMemo(
    () => videoAssetsBasedOnCurrentTimelineIndicatorTimeMap_UNSTABLE,
    [videoAssetsBasedOnCurrentTimelineIndicatorTimeMap_UNSTABLE],
  )

  const videoAssets = useMemo((): Array<VideoAsset> => {
    const newVideoAssets = cameraChannelsSources
      .map(({ id }) => {
        const camera = id
        const foundAsset = videoAssetsBasedOnCurrentTimelineIndicatorTimeMap.get(camera)

        return {
          camera,
          url: foundAsset?.url || null,
          startTimeUnixMs: foundAsset?.startTimeUnixMs || 0,
          endTimeUnixMs: foundAsset?.endTimeUnixMs || 0,
          differenceInMilliseconds: foundAsset?.differenceInMilliseconds || 0,
        }
      })
      .sort((a, b) => Number(Boolean(b.url)) - Number(Boolean(a.url)))

    return newVideoAssets
  }, [cameraChannelsSources, videoAssetsBasedOnCurrentTimelineIndicatorTimeMap])

  const videoElementsRefsMap: VideoElementsRefsMap = useRef(new Map())

  const layoutEffectCallbacksRef = useRef(new Set<() => void>())

  // To prevent impossible state updates, we derive the current timeline view millis from
  // the actual current timeline html element scroll position, scrollLeft, etc
  // which is more reliable than updating both on our own.
  // eslint-disable-next-line sonarjs/hook-use-state
  const [readonlyCurrentTimelineView, recalculateCurrentTimelineViewMillisMeta] =
    useState<{ startUnixMillis: number; endUnixMillis: number }>({
      startUnixMillis: combinedTimeRange.startMillis,
      endUnixMillis: combinedTimeRange.endMillis,
    })

  const dispatch = useEventHandler(
    (event: CameraFootageModalWithVehicleEvent): void => {
      cameraFootageModalWithVehicleAndDataEventHandler({
        event,
        getLatestState: () => latestStateRef.current,
        state,
        setState: setState_,
        combinedTimeRange,
        visionFootageBlocksData: visionFootageBlocksSuccessQuery.data,
        timelineRef,
        isProcessingZoomScrollRef,
        timelineDraggingRef,
        documentMouseMetaRef,
        timelineAutoPlayIntervalIdRef,
        vehicleId,
        videoAvailabilityPeriods,
        videoAssets,
        videoElementsRefsMap,
        readonlyCurrentTimelineView,
        recalculateCurrentTimelineViewMillisMeta,
        runLayoutEffectOnNextRender: (callback: () => void) => {
          layoutEffectCallbacksRef.current.add(callback)
        },
        unavailableFootageTimeRanges,
        bottomScrollBarDraggingRef,
      })
    },
  )

  useLayoutEffect(() => {
    for (const callback of layoutEffectCallbacksRef.current.values()) {
      callback()
      layoutEffectCallbacksRef.current.delete(callback)
    }
  })

  useEffect(() => {
    dispatch({
      type: 'on_videoAssetsBasedOnCurrentTimelineIndicatorTimeMap_deep_change',
      videoAssetsBasedOnCurrentTimelineIndicatorTimeMap,
    })
  }, [dispatch, videoAssetsBasedOnCurrentTimelineIndicatorTimeMap])

  const onMount = useEffectEvent(() => {
    dispatch({ type: 'on_mount', initialSelectedFootage })
  })

  useEffect(() => {
    onMount()
  }, [])

  const isResizeObserverRunningOnMountRef = useRef(true)
  useResizeObserver(timelineRef, () => {
    if (isResizeObserverRunningOnMountRef.current) {
      // We don't want to run this on mount, because it will trigger a scroll to the left
      // which can conflict with onMount event handler logic.
      // It's also simply not needed on mount.
      isResizeObserverRunningOnMountRef.current = false
      return
    }
    dispatch({ type: 'on_timeline_resize_observer_resize_except_on_mount' })
  })

  const onTimelineScroll = useEffectEvent(() => {
    if (!timelineRef.current) {
      return
    }
    recalculateCurrentTimelineViewMillisMeta(
      getCurrentTimelineViewMillisMeta({
        timelineElement: timelineRef.current,
        combinedTimeRange,
      }),
    )
  })

  useEffect(() => {
    const timelineEl = timelineRef.current
    timelineEl?.addEventListener('scroll', onTimelineScroll)
    return () => {
      timelineEl?.removeEventListener('scroll', onTimelineScroll)
    }
  }, [])

  const onDocumentMouseMove = useEffectEvent((mouseEvent: MouseEvent) => {
    dispatch({ type: 'on_document_mouse_move', mouseEvent })
  })
  const onDocumentMouseUp = useEffectEvent((mouseEvent: MouseEvent) => {
    dispatch({ type: 'on_document_mouse_up', mouseEvent })
  })
  const onDocumentMouseDown = useEffectEvent((mouseEvent: MouseEvent) => {
    dispatch({ type: 'on_document_mouse_down', mouseEvent })
  })
  useEffect(() => {
    document.addEventListener('mousemove', onDocumentMouseMove)
    document.addEventListener('mouseup', onDocumentMouseUp)
    document.addEventListener('mousedown', onDocumentMouseDown)
    return () => {
      document.removeEventListener('mousemove', onDocumentMouseMove)
      document.removeEventListener('mouseup', onDocumentMouseUp)
      document.removeEventListener('mousedown', onDocumentMouseDown)
    }
  }, [])

  // The reason we need to recalculate the current timeline view when zoom changes
  // is because the timeline element width changes, and with it the scrollLeft value
  // The other event listeners don't trigger in this case, we need to listen to zoom level changes manually
  const onTimelineZoomLevelChange = useEffectEvent(() => {
    if (!timelineRef.current) {
      return
    }
    recalculateCurrentTimelineViewMillisMeta(
      getCurrentTimelineViewMillisMeta({
        timelineElement: timelineRef.current,
        combinedTimeRange,
      }),
    )
  })
  useEffect(() => {
    onTimelineZoomLevelChange()
  }, [timelineZoomLevel])

  useEffect(
    () => () => {
      if (timelineAutoPlayIntervalIdRef.current) {
        window.clearInterval(timelineAutoPlayIntervalIdRef.current)
      }
    },
    [],
  )

  return (
    <Box
      sx={{
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'row',
        position: 'relative',
        gap: 1,
      }}
    >
      <CameraFootageModalMetaDataProvider
        value={{
          isMuted,
          vehicleId,
          timelineRef,
          combinedTimeRange,
          timelineOffsets,
          activeSourcesIds,
          dispatch,
          timelineZoomLevel,
        }}
      >
        <Box
          sx={{
            height: '100%',
            flex: '1 1 auto',
            overflow: 'hidden',
            display: 'grid',
            gridTemplateRows: 'auto min-content',
          }}
        >
          <RecordedFootageViewer
            // remounts the component for now if viewing type changed
            // to use different video references for each viewing type
            key={viewingVideoType}
            videoElementsRefsMap={videoElementsRefsMap}
            focusedCameraSourceId={focusedCameraSource?.id ?? null}
            videoAssets={videoAssets}
            playbackStatus={playbackStatus}
            viewingVideoType={viewingVideoType}
            isSliderUpdatingIndicator={isSliderUpdatingIndicator}
            footage={visionFootageBlocksSuccessQuery.data.footage}
            currentTimelineIndicatorUnixMillis={currentTimelineIndicatorUnixMillis}
            triggeredGenericSensorEvents={triggeredGenericSensorEvents}
          />
          <CamerasVideoControl
            isSensorsCheckboxChecked={isSensorsCheckboxChecked}
            timelineZoomLevel={timelineZoomLevel}
            requestFootageState={requestFootageSection}
            playbackStatus={playbackStatus}
            currentTimelineIndicatorUnixMillis={currentTimelineIndicatorUnixMillis}
            footage={visionFootageBlocksSuccessQuery.data.footage}
            selectedFootageBlock={selectedFootageBlockWithData}
            timelineViewStartUnixMillis={readonlyCurrentTimelineView.startUnixMillis}
            timelineViewEndUnixMillis={readonlyCurrentTimelineView.endUnixMillis}
            hoveredFootageBlock={hoveredFootageBlock}
            timelineEventsRawData={timelineEventsRawData}
            timelineEventsUIData={timelineEventsUISuccessQuery.data}
            sendToCoachingParams={sendToCoachingParams}
            unavailableFootageTimeRanges={unavailableFootageTimeRanges}
            filteredGenericSensorAlertEvents={filteredGenericSensorAlertEvents}
          />
        </Box>

        {isMapCheckboxChecked && (
          <Box
            data-testid="CameraFootageModalDialog-map"
            sx={{
              minWidth: 100,
              maxWidth: '32%',
              position: 'relative',
              flex: '0 0 32%',
            }}
          >
            <MapArea
              onEventMarkerClick={(eventTime: DateTime) =>
                dispatch({ type: 'on_map_event_marker_click', eventTime })
              }
              requestFootageSection={requestFootageSection}
              currentTimelineIndicatorUnixMillis={currentTimelineIndicatorUnixMillis}
              timelineEventsRawData={timelineEventsRawData}
              timelineEventsUIData={timelineEventsUISuccessQuery.data}
            />
          </Box>
        )}
      </CameraFootageModalMetaDataProvider>
    </Box>
  )
}

export default CameraFootageModal

const sharedIconStyles = ({
  isSelected,
}: {
  isSelected: boolean
}): SxProps<KarooUiInternalTheme> => ({
  color: isSelected ? 'primary.main' : 'grey.400',
  fontSize: '18px',
  cursor: 'pointer',
  '&:hover': { color: 'primary.main' },
})
