import { useMemo } from 'react'

import type { DriverGroupId, DriverId } from 'api/types'
import { getDrivers } from 'duxs/drivers'
import { useTypedSelector } from 'src/redux-hooks'

import { useCoachingDashboardFiltersQuery } from '../../CoachingDashboard/api/queries'

type DriverOpt = {
  label: string
  value: DriverId
}

export default function useDriverOptions() {
  const drivers = useTypedSelector(getDrivers)
  const coachingDashboardFiltersQuery = useCoachingDashboardFiltersQuery()

  const parsedDrivers = useMemo(() => {
    const driversByIdWithInfo = coachingDashboardFiltersQuery.data?.drivers.byId
    const leafsDriverGroupsById =
      coachingDashboardFiltersQuery.data?.driverGroups === 'NO_ACCESS'
        ? null
        : coachingDashboardFiltersQuery.data?.driverGroups.leafsById

    const driversMap = new Map<DriverId, DriverOpt>()
    const driversArray: Array<DriverOpt> = []
    for (const rawDriver of drivers) {
      const ascendentGroupIds = [
        ...(driversByIdWithInfo?.get(rawDriver.id)?.ascendentGroupIds ??
          new Set<DriverGroupId>()),
      ]

      const driverParentGroupId = ascendentGroupIds.find((groupId) =>
        leafsDriverGroupsById?.has(groupId),
      )

      const departmentLabel = driverParentGroupId
        ? leafsDriverGroupsById?.get(driverParentGroupId)?.label
        : null

      const driver = {
        value: rawDriver.id,
        label: `${rawDriver.name}${departmentLabel ? ` (${departmentLabel})` : ''}`,
      }

      driversMap.set(driver.value, driver)
      driversArray.push(driver)
    }

    return { byId: driversMap, arr: driversArray }
  }, [drivers, coachingDashboardFiltersQuery])

  return coachingDashboardFiltersQuery.status === 'pending'
    ? ('LOADING' as const)
    : parsedDrivers
}
