import { useMemo, useState } from 'react'
import { isNil } from 'lodash'
import {
  Autocomplete,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Stack,
  TextField,
  Typography,
} from '@karoo-ui/core'
import PersonIcon from '@mui/icons-material/Person'
import {
  Controller,
  useController,
  useWatch,
  type Control,
  type FieldErrors,
  type FormState,
} from 'react-hook-form'

import type { DriverGroupId, DriverId } from 'api/types'
import { getDrivers } from 'duxs/drivers'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { useModal } from 'src/hooks'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import { ctToast } from 'cartrack-ui-kit'
import { useCoachingDashboardFiltersQuery } from '../../CoachingDashboard/api/queries'
import { useAssignDriverToCoachEventMutation } from '../api/queries'
import type { CoachingEventId } from '../api/types'
import type { NewEventSchemaPossibleValues } from './schema'

type DriverOpt = {
  label: string
  value: DriverId
}

type AssignDriverToCoachParams = {
  eventIds: Array<CoachingEventId>
  driverId: DriverId
  driverName: string
}

const SelectDriver = ({
  control,
  errors,
  eventId,
  formState,
}: {
  control: Control<NewEventSchemaPossibleValues>
  errors: FieldErrors<NewEventSchemaPossibleValues>
  eventId: CoachingEventId
  formState: FormState<NewEventSchemaPossibleValues>
}) => {
  const [isSelectVisible, setIsSelectVisible] = useState(false)
  const [isModalOpen, { close, open, data }] = useModal<AssignDriverToCoachParams>()

  const drivers = useTypedSelector(getDrivers)
  const coachingDashboardFiltersQuery = useCoachingDashboardFiltersQuery()

  const parsedDrivers = useMemo(() => {
    const driversByIdWithInfo = coachingDashboardFiltersQuery.data?.drivers.byId
    const leafsDriverGroupsById =
      coachingDashboardFiltersQuery.data?.driverGroups === 'NO_ACCESS'
        ? null
        : coachingDashboardFiltersQuery.data?.driverGroups.leafsById

    const driversMap = new Map<DriverId, DriverOpt>()
    const driversArray: Array<DriverOpt> = []
    for (const rawDriver of drivers) {
      const ascendentGroupIds = [
        ...(driversByIdWithInfo?.get(rawDriver.id)?.ascendentGroupIds ??
          new Set<DriverGroupId>()),
      ]

      const driverParentGroupId = ascendentGroupIds.find((groupId) =>
        leafsDriverGroupsById?.has(groupId),
      )

      const departmentLabel = driverParentGroupId
        ? leafsDriverGroupsById?.get(driverParentGroupId)?.label
        : null

      const driver = {
        value: rawDriver.id,
        label: `${rawDriver.name}${departmentLabel ? ` (${departmentLabel})` : ''}`,
      }

      driversMap.set(driver.value, driver)
      driversArray.push(driver)
    }

    return { byId: driversMap, arr: driversArray }
  }, [drivers, coachingDashboardFiltersQuery])

  const { field: acceptedNoticeField } = useController({
    control,
    name: 'assignedDriverData.acceptedNotice',
  })

  const watched_assignedDriverData = useWatch({ name: 'assignedDriverData', control })

  return (
    <>
      <Stack
        direction="row"
        gap={1}
        alignItems={'flex-start'}
      >
        {isSelectVisible ? (
          <>
            <Controller
              control={control}
              name="assignedDriverData"
              render={({ field }) => (
                <Autocomplete
                  {...getAutocompleteVirtualizedProps({
                    options: parsedDrivers.arr,
                  })}
                  sx={{ width: '100%' }}
                  disableClearable={!isNil(field.value?.driverId)}
                  value={
                    field.value?.driverId
                      ? parsedDrivers.byId.get(field.value.driverId as DriverId)
                      : null
                  }
                  onChange={(_, value) => {
                    field.onChange({
                      driverId: value?.value ?? null,
                      acceptedNotice: field.value?.acceptedNotice ?? false,
                    })
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      error={
                        !!errors.assignedDriverData?.driverId ||
                        !errors.assignedDriverData?.acceptedNotice
                      }
                      helperText={
                        errors.assignedDriverData?.driverId?.message?.toString() ??
                        errors.assignedDriverData?.acceptedNotice?.message?.toString()
                      }
                      label={ctIntl.formatMessage({ id: 'Driver' })}
                      slotProps={{
                        input: {
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {coachingDashboardFiltersQuery.status === 'pending' ? (
                                <CircularProgress
                                  color="inherit"
                                  size={20}
                                />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        },
                      }}
                    />
                  )}
                />
              )}
            />
            <Button
              onClick={() =>
                watched_assignedDriverData?.driverId
                  ? open({
                      eventIds: [eventId],
                      driverId: watched_assignedDriverData.driverId as DriverId,
                      driverName:
                        parsedDrivers.byId.get(
                          watched_assignedDriverData.driverId as DriverId,
                        )?.label ?? '',
                    })
                  : null
              }
              variant="contained"
              disabled={!!formState.errors.assignedDriverData?.driverId}
            >
              {ctIntl.formatMessage({ id: 'Save' })}
            </Button>
          </>
        ) : (
          <Stack>
            <Button
              startIcon={<PersonIcon />}
              onClick={() => setIsSelectVisible((prevState) => !prevState)}
              variant="contained"
            >
              {ctIntl.formatMessage({ id: 'Assign Driver' })}
            </Button>
            {errors.assignedDriverData && (
              <Typography sx={{ color: '#F44336', fontSize: '0.75rem' }}>
                {errors.assignedDriverData?.driverId?.message?.toString() ?? ''}
              </Typography>
            )}
          </Stack>
        )}
      </Stack>
      {isModalOpen && (
        <AssignDriverDialog
          data={data}
          cancel={close}
          close={() => {
            close()
            acceptedNoticeField.onChange(true)
          }}
        />
      )}
    </>
  )
}

export default SelectDriver

const AssignDriverDialog = ({
  close,
  cancel,
  data,
}: {
  close: () => void
  cancel: () => void
  data: AssignDriverToCoachParams | undefined
}) => {
  const assignDriverToCoachEvent = useAssignDriverToCoachEventMutation()

  return (
    <Dialog
      open
      onClose={close}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">
        {ctIntl.formatMessage({ id: 'Warning' })}
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          {ctIntl.formatMessage({ id: 'coaching.assignDriverDialog.message' })}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          color="secondary"
          onClick={cancel}
        >
          {ctIntl.formatMessage({ id: 'Cancel' })}
        </Button>
        <Button
          loading={assignDriverToCoachEvent.isPending}
          onClick={() => {
            if (data) {
              assignDriverToCoachEvent.mutate(data, {
                onSuccess: () => {
                  ctToast.fire('success', 'coaching.events.assignDriverToCoachEvent')
                  close()
                },
              })
            }
          }}
        >
          {ctIntl.formatMessage({ id: 'Confirm' })}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
