import { Fragment, useCallback, useEffect, useMemo, useState } from 'react'
import { isNil } from 'lodash'
import {
  Autocomplete,
  Button,
  Checkbox,
  Chip,
  DataGridAsTabItem,
  getGridStringOperators,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridActionsCellItem,
  GridLogicOperator,
  LinearProgress,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
  useCallbackBranded,
  useDataGridDateColumns,
  useGridApiContext,
  useGridApiRef,
  type GridColDef,
  type GridEventListener,
  type GridFilterModel,
  type GridRowId,
  type GridRowSelectionModel,
  type GridSingleSelectColDef,
  type GridSortDirection,
  type GridSortModel,
} from '@karoo-ui/core'
import ArrowCircleRightIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import AssignmentIcon from '@mui/icons-material/Assignment'
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye'
import { useQueryClient } from '@tanstack/react-query'
import { DateTime } from 'luxon'
import { useDispatch } from 'react-redux'
import { useHistory } from 'react-router'
import * as R from 'remeda'
import { match, P } from 'ts-pattern'
import type { ReadonlyDeep } from 'type-fest'

import type {
  DriverId,
  GlobalClientUserId,
  GlobalMainUserId,
  TerminalEventTypeId,
  VehicleId,
} from 'api/types'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { useEffectEvent, useEventHandler } from 'src/hooks/useEventHandler'
import { useLatestExceptDuringRender } from 'src/hooks/useLatestExceptDuringRender'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useShowLoadNewEventsButtonMeta } from 'src/modules/vision/useShowLoadNewEventsButtonMeta'
import {
  mapFilterItemToServerFilter_Date,
  mapFilterItemToServerFilter_Enum,
  mapFilterItemToServerFilter_String,
  shouldShowLoadNewEventsButtonBasedOnMostRecentEvent,
} from 'src/shared/data-grid/server-client/utils'
import { ServerInfiniteLoadingPaginationContextProvider } from 'src/shared/data-grid/ServerInfiniteLoadingPaginationContextProvider'
import {
  createDataGridTextColumn,
  downloadBasicTableDataAsSheetFile,
} from 'src/shared/data-grid/utils'
import type { FixMeAny } from 'src/types'
import { CellTextWithMore } from 'src/util-components/CellTextWithMore'
import { ctIntl } from 'src/util-components/ctIntl'
import { ctToast } from 'src/util-components/ctToast'
import { exact } from 'src/util-functions/functional-utils'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'

import { getCoachingPriorityLevelDetails } from '../../utils'
import {
  fetchToCoachCoachingEvents,
  toCoachVehicleCoachingEventsQueryKey,
  useToCoachVehicleCoachingEventsQuery,
  useUpdateCoachingEventsMutation,
  type UseCoachingEventsTablesPreRequisitesReturnedData,
  type UseToCoachCoachingEventsReturnedData,
} from '../api/queries'
import {
  fetchToCoachVehicleCoachingEventsFilterModelSchema,
  type CoachingEventId,
  type CoachingPriorityLevelType,
  type FetchToCoachVehicleCoachingEvents,
  type FetchToCoachVehicleCoachingEventsFilterModelSchemaSelf,
} from '../api/types'
import useCoachOptions, { useCoachAllSubUsersOptions } from '../api/useCoachOptions'
import { getCoachingEventsDetailsModalMainPath } from '../CoachingEventsDetailsDrawer'
import { ColumnMenuWithCoachingDriverFilter } from '../components/ColumnMenuWithCoachingDriverFilter'
import {
  DataGridToolbarWithLoadNewEventsButtonAndExport,
  type DataGridToolbarWithLoadNewEventsButtonAndExportProps,
} from '../components/DataGridToolbarWithLoadNewEventsButtonAndExport'
import MarkAsCoachedModal from '../components/MarkAsCoachedModal'
import MarkForCoachingModal from '../components/MarkForCoachingModal'
import type { CoachingEventsDispatch } from './slice'
import {
  COACHING_COLUMNS_IDS,
  mapCoachingEventTypeFilterItemToServerFilter,
  normalizeEventTypeColumnValueOptions,
  useCoachingDriverMenuFilter,
} from './utils'

type SortableColumnId = (typeof sortableColumnIds)[keyof typeof sortableColumnIds]
type FilterableColumnId = (typeof filterableColumnIds)[keyof typeof filterableColumnIds]
type Option = {
  value: CoachingPriorityLevelType
  label: string
}

const columnsIds = {
  time: 'terminal_event_ts',
  driver: COACHING_COLUMNS_IDS.driver,
  departmentsNames: COACHING_COLUMNS_IDS.departmentsNames,
  priority: 'priority',
  vehicle: COACHING_COLUMNS_IDS.vehicle,
  eventType: COACHING_COLUMNS_IDS.eventType,
  numberId: COACHING_COLUMNS_IDS.numberId,
} as const

const sortableColumnIds = R.pick(columnsIds, ['time'])
const sortableColumnIdsArray = Object.values(sortableColumnIds) satisfies Array<
  ToCoachEventDataGridSortModel[number]['field']
>

const filterableColumnIds = R.pick(columnsIds, [
  'time',
  'driver',
  'departmentsNames',
  'vehicle',
  'priority',
  'eventType',
  'numberId',
])
const filterableColumnIdsArray = Object.values(filterableColumnIds) satisfies Array<
  ToCoachEventDataGridFilterModel['items'][number]['field']
>

type DataGridRow = UseToCoachCoachingEventsReturnedData['pages'][number]['rows'][number]

type DispatchedEvent =
  | {
      type: 'clicked_row_view_action_icon'
      rowId: CoachingEventId
    }
  | {
      type: 'clicked_load_more_button'
    }

type ToCoachEventDataGridSortModel =
  FetchToCoachVehicleCoachingEvents.ServerRequestModel['sort']

type ToCoachEventDataGridFilterModel =
  FetchToCoachVehicleCoachingEventsFilterModelSchemaSelf

type RowModel = FetchToCoachVehicleCoachingEvents.ParsedEvent

type AssignedCoachColumnValue = GlobalMainUserId | GlobalClientUserId | null
type PriorityColumnValue = CoachingPriorityLevelType | '_UNKNOWN_'

const POLLING_REFETCH_INTERVAL = 20_000

export function ToCoachEventsDataGrid({
  tableRequisites,
}: {
  tableRequisites: UseCoachingEventsTablesPreRequisitesReturnedData
}) {
  const reduxDispatch = useDispatch<CoachingEventsDispatch>()
  const queryClient = useQueryClient()
  const history = useHistory()

  const { coachingDriversFilter, columnMenuItemDriversFilterProps } =
    useCoachingDriverMenuFilter()

  const [sortModel, setSortModel] = useState<ToCoachEventDataGridSortModel>([
    { field: columnsIds.time, sort: 'asc' },
  ])
  const [filterModel, setFilterModel] = useState<ToCoachEventDataGridFilterModel>({
    items: [],
    logicOperator: GridLogicOperator.Or,
  })

  const currentTimeColumnSortOrder = useMemo(
    () => sortModel.find((field) => field.field === columnsIds.time)?.sort ?? 'asc',
    [sortModel],
  )
  const {
    showingLoadNewEventsButton,
    setShowingLoadNewEventsButton,
    callOnIntervalEventsDataChecks,
    mostRecentEventFetchedSoFarRef,
  } = useShowLoadNewEventsButtonMeta<ReadonlyDeep<RowModel>>({
    currentTimeColumnSortOrder: currentTimeColumnSortOrder,
  })

  const { createDateTimeColumn, getGridDateColumnOperators } = useDataGridDateColumns({
    filterMode: 'server',
  })

  const [exportQueryFetchStatus, setExportQueryFetchStatus] = useState<
    'fetching' | 'idle'
  >('idle')
  const gridApiRef = useGridApiRef()
  const [rowsIdSelected, setRowsIdSelected] = useState<Array<CoachingEventId>>([])
  const [currentModal, setCurrentModal] = useState<
    | {
        type: 'mark_for_coaching'
        context: {
          eventIds: Array<CoachingEventId>
          driverIds: Array<DriverId>
          vehicleIds: Array<VehicleId>
        }
      }
    | {
        type: 'mark_as_coached'
        context: {
          eventIds: Array<CoachingEventId>
        }
      }
    | null
  >(null)
  const updateCoachingEvents = useUpdateCoachingEventsMutation()

  const priorityColumnValuesOptionsMeta = useMemo(() => {
    const array: Array<Option> = []
    const byValue = new Map<CoachingPriorityLevelType, Option>()

    for (const code of tableRequisites.priorityCodes) {
      const option: Option = {
        label: getCoachingPriorityLevelDetails(code).labelMsgId,
        value: code,
      }
      array.push(option)
      byValue.set(code, option)
    }

    return { array, byValue }
  }, [tableRequisites.priorityCodes])

  const selectCoachAllSubUsersOptions = useCoachAllSubUsersOptions()

  const processRowUpdate = useCallback(
    (newRow: DataGridRow, oldRow: DataGridRow): Promise<DataGridRow> | DataGridRow =>
      new Promise((resolve, _reject) => {
        if (selectCoachAllSubUsersOptions === 'LOADING') {
          return
        }
        const rollbackChanges = () => resolve(oldRow)

        const selectedCoachOption = newRow.assignedCoachGlobalUserId
          ? selectCoachAllSubUsersOptions.byGlobalId.get(
              newRow.assignedCoachGlobalUserId,
            )
          : undefined

        if (!selectedCoachOption) {
          return
        }

        updateCoachingEvents.mutate(
          {
            eventIds: [newRow.id],
            priorityId: newRow.priority as CoachingPriorityLevelType,
            coachUser:
              selectedCoachOption.userType === 'admin'
                ? {
                    userId: selectedCoachOption.userId,
                    userType: 'admin',
                  }
                : {
                    userType: 'subUser',
                    subUserId: selectedCoachOption.subUserId,
                  },
            coachingNotes: newRow.coachingNotes,
            clientDriverId: null,
          },
          {
            onSuccess() {
              ctToast.fire('success', 'coaching.updateCoachingEventsSuccessMessage')
              resolve(newRow)
            },
            onError() {
              rollbackChanges()
            },
          },
        )
      }),
    [selectCoachAllSubUsersOptions, updateCoachingEvents],
  )

  const queryParams = useMemo(
    () =>
      ({
        serverModel: {
          sort: sortModel,
          pageSize: 100,
          filter: {
            logicOperator: filterModel.logicOperator,
            items: Array_filterMap(filterModel.items, (item, { RemoveSymbol }) => {
              switch (item.field) {
                case 'terminal_event_ts': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_Date({
                    field: 'terminal_event_ts',
                    value,
                    operator,
                    precision: 'minute',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'driver': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_String({
                    field: 'driver_name',
                    value,
                    operator,
                    case: 'insensitive',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'departmentsNames': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_String({
                    field: 'departments_names',
                    value,
                    operator,
                    case: 'insensitive',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'priority': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_Enum({
                    field: 'priority_id',
                    value,
                    operator,
                    getValidSingleValueOrUndefined: (singleValue) =>
                      priorityColumnValuesOptionsMeta.byValue.get(
                        singleValue as CoachingPriorityLevelType,
                      )?.value ?? undefined,
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'vehicle': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_String({
                    field: 'vehicle',
                    value,
                    operator,
                    case: 'insensitive',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'eventTypeId': {
                  const { value, operator } = item
                  const serverFilter = mapCoachingEventTypeFilterItemToServerFilter({
                    value,
                    operator,
                    duplicatedEventTypesByGeneratedLabel:
                      tableRequisites.duplicatedEventTypesByGeneratedLabel,
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
                case 'numberId': {
                  const { value, operator } = item
                  const serverFilter = mapFilterItemToServerFilter_String({
                    field: 'number_id',
                    value,
                    operator,
                    case: 'insensitive',
                  })

                  return serverFilter === 'TO_IGNORE' ? RemoveSymbol : serverFilter
                }
              }
            }),
          },
        },
        coachingDriversFilter,
      }) satisfies Parameters<typeof useToCoachVehicleCoachingEventsQuery>[0]['params'],
    [
      coachingDriversFilter,
      filterModel.items,
      filterModel.logicOperator,
      priorityColumnValuesOptionsMeta,
      sortModel,
      tableRequisites.duplicatedEventTypesByGeneratedLabel,
    ],
  )

  const toCoachCoachingEventsQuery = useToCoachVehicleCoachingEventsQuery({
    params: queryParams,
    refetchIntervalIfNeeded: POLLING_REFETCH_INTERVAL,
  })

  const toCoachCoachingEventsQueryLatestRef = useLatestExceptDuringRender(
    toCoachCoachingEventsQuery,
  )

  useEffect(() => {
    reduxDispatch({
      type: 'toCoachVehicleCoachingEventsQuery_params_changed_on_data_grid_component',
      params: queryParams,
    })
  }, [queryParams, reduxDispatch])

  useEffect(
    () => () => {
      reduxDispatch({ type: 'unmounted_toCoach_vehicle_coaching_events_data_grid' })
    },
    [reduxDispatch],
  )

  const onToCoachCoachingEventsQueryDataChange = useEffectEvent(
    (newQueryData: (typeof toCoachCoachingEventsQuery)['data']) => {
      if (currentTimeColumnSortOrder === 'desc') {
        const newMostRecentEvent = newQueryData?.pages.at(0)?.rows.at(0)
        if (!newMostRecentEvent) {
          return
        }

        mostRecentEventFetchedSoFarRef.current = (() => {
          const curr = mostRecentEventFetchedSoFarRef.current
          if (curr === null) {
            return newMostRecentEvent
          }

          return newMostRecentEvent.time.getTime() >= curr.time.getTime()
            ? newMostRecentEvent
            : curr
        })()
      }
    },
  )
  useEffect(() => {
    onToCoachCoachingEventsQueryDataChange(toCoachCoachingEventsQuery.data)
  }, [toCoachCoachingEventsQuery.data])

  const onIntervalCallback = useEffectEvent(async () => {
    if (
      toCoachCoachingEventsQuery.status === 'pending' &&
      toCoachCoachingEventsQuery.fetchStatus === 'fetching'
    ) {
      // useQuery has not yet fetched the data. Don't bother doing refetching logic
      return
    }

    const { shouldFetchToCheckForNewData } = callOnIntervalEventsDataChecks()
    if (shouldFetchToCheckForNewData) {
      try {
        const newEvents = await fetchToCoachCoachingEvents({
          serverModel: {
            sort: [{ field: columnsIds.time, sort: 'desc' }],
            pagination: { cursor: 'start', pageSize: 5 },
            // DO NOT include any filters to check for new events. The user wants to know if there are new events, period.
            filter: {
              items: [],
              logicOperator: GridLogicOperator.Or,
            },
          },
          // "Include" all drivers for same reason as above
          coachingDriversFilter: 'all_drivers',
        })
        const firstEventOnNewData = newEvents.rows.at(0)

        const newShowLoadNewEventsButton =
          shouldShowLoadNewEventsButtonBasedOnMostRecentEvent<CoachingEventId>({
            firstEventOnNewData,
            mostRecentEventFetchedSoFar: mostRecentEventFetchedSoFarRef.current,
            mainQueryLatestRef: toCoachCoachingEventsQueryLatestRef,
          })

        setShowingLoadNewEventsButton(newShowLoadNewEventsButton)
      } catch {
        // Do nothing
      }
    }
  })

  useEffect(() => {
    const id = window.setInterval(onIntervalCallback, POLLING_REFETCH_INTERVAL)

    return () => {
      window.clearInterval(id)
    }
  }, [])

  const availableActions = useMemo(
    (): Array<'view' | 'mark_as_coached'> => ['view', 'mark_as_coached'],
    [],
  )

  const numberOfActionsAvailable = availableActions.length

  const dispatch = useEventHandler((event: DispatchedEvent) => {
    const state = {}

    match([state, event])
      .with([P._, { type: 'clicked_row_view_action_icon' }], ([_, { rowId }]) => {
        history.push(
          getCoachingEventsDetailsModalMainPath(history.location, {
            eventId: rowId,
          }),
        )
      })
      .with([P._, { type: 'clicked_load_more_button' }], () => {
        setShowingLoadNewEventsButton(false)
        queryClient.resetQueries({
          queryKey: toCoachVehicleCoachingEventsQueryKey(undefined),
        })
      })
      .exhaustive()
  })

  const onDataGridRowsScrollEnd: GridEventListener<'rowsScrollEnd'> = () => {
    toCoachCoachingEventsQuery.fetchNextPage()
  }

  const onSortModelChange = async (newSortModel_: GridSortModel) => {
    type TypeSortItem = {
      field: SortableColumnId
      sort: GridSortDirection
    }
    const newSortModel = newSortModel_ as Array<TypeSortItem>

    const isStartTimeField = (item: TypeSortItem) => item.field === columnsIds.time

    // const newNormalizedModel = newSortModel.filter(
    //   (item) => !isStartTimeField(item),
    // ) as Array<FixMeAny>

    // Change sort model right away since resetQueries can take a while
    setSortModel(() => [
      {
        field: columnsIds.time,
        sort: newSortModel.find((item) => isStartTimeField(item))?.sort ?? 'asc',
      },
      // NOTE: This spread is not needed at the moment but it will be once we support multiple column sorting
      // ...newNormalizedModel,
    ])
  }

  const onFilterModelChange = async (newFilterModel: GridFilterModel) => {
    setFilterModel(newFilterModel as ToCoachEventDataGridFilterModel)

    // We set the filter model anyways but we try to throw an error while in dev for developers to fix it
    if (ENV.NODE_ENV === 'development') {
      // Might throw an error if the filter model is invalid (which is what we want)
      fetchToCoachVehicleCoachingEventsFilterModelSchema.self.parse(newFilterModel)
      return
    }
    return
  }

  const columns = useMemo((): Array<GridColDef<DataGridRow>> => {
    const base: Array<GridColDef<DataGridRow>> = [
      {
        ...GRID_CHECKBOX_SELECTION_COL_DEF,
        hideable: false,
        renderCell: (params) => {
          if (isNil(params.row.driverName)) {
            return (
              <Tooltip
                key="manual-assign"
                title={ctIntl.formatMessage({
                  id: 'coaching.events.disabledNoDriverCheckbox',
                })}
                placement="right"
              >
                <span>
                  <Checkbox disabled />
                </span>
              </Tooltip>
            )
          }
          return (
            GRID_CHECKBOX_SELECTION_COL_DEF.renderCell &&
            GRID_CHECKBOX_SELECTION_COL_DEF.renderCell(params)
          )
        },
      },
      createDateTimeColumn({
        field: columnsIds.time,
        headerName: ctIntl.formatMessage({
          id: 'coaching.events.columnHeader.time',
        }),
        filterOperators: getGridDateColumnOperators({ showTime: true }).filter(
          (operator) => {
            const parseResult =
              fetchToCoachVehicleCoachingEventsFilterModelSchema.item.time.shape.operator.safeParse(
                operator.value,
              )

            return parseResult.success
          },
        ) as FixMeAny,
        sortingOrder: ['desc', 'asc'], // Force user to sort by startTime, be it ascending or descending (in order for cursor-based pagination to work)
        valueGetter: (_, row) => row.time,
        valueFormatter: (value) => DateTime.fromJSDate(value).toFormat('F'),
      }),
      createDataGridTextColumn({
        field: columnsIds.driver,
        headerNameMsg: { id: 'Driver' },
        valueGetter: (_, row) => row.driverName,
        flex: 1,
        filterOperators: getGridStringOperators<DataGridRow>().filter((operator) => {
          const parseResult =
            fetchToCoachVehicleCoachingEventsFilterModelSchema.item.driver.shape.operator.safeParse(
              operator.value,
            )

          return parseResult.success
        }),
        renderCell: ({ row }) => {
          if (row.isDriverManualAssign) {
            return (
              <Tooltip
                key="manual-assign"
                title={ctIntl.formatMessage({
                  id: 'coaching.events.manualAssignDriver',
                })}
                placement="top"
              >
                <Typography sx={{ fontStyle: 'italic' }}>{row.driverName}*</Typography>
              </Tooltip>
            )
          }
          return row.driverName
        },
      }),
      createDataGridTextColumn({
        field: columnsIds.departmentsNames,
        headerNameMsg: { id: 'Department' },
        valueGetter: (_, row) => row.departmentsNames.join(', '),
        renderCell: ({ value }) => <CellTextWithMore value={value ?? ''} />,
        flex: 1,
        filterOperators: getGridStringOperators<DataGridRow>().filter((operator) => {
          const parseResult =
            fetchToCoachVehicleCoachingEventsFilterModelSchema.item.departmentsNames.shape.operator.safeParse(
              operator.value,
            )

          return parseResult.success
        }),
      }),
      createDataGridTextColumn({
        field: columnsIds.numberId,
        headerNameMsg: { id: 'Employee Number' },
        valueGetter: (_, row) => row.numberId,
        flex: 1,
      }),
      {
        type: 'singleSelect',
        field: columnsIds.eventType,
        headerName: ctIntl.formatMessage({ id: 'Event' }),
        valueGetter: (_, row) => row.eventTypeId,
        valueOptions: normalizeEventTypeColumnValueOptions({
          eventTypes: tableRequisites.eventTypes,
        }),
        flex: 1,
      } satisfies GridSingleSelectColDef<
        { label: string; value: TerminalEventTypeId },
        DataGridRow,
        TerminalEventTypeId | null
      >,
      createDataGridTextColumn({
        field: 'eventId',
        headerNameMsg: { id: 'coaching.events.columnHeader.eventId' },
        valueGetter: (_, row) => row.id,
        flex: 1,
      }),
      createDataGridTextColumn({
        field: columnsIds.vehicle,
        headerNameMsg: { id: 'Vehicle' },
        valueGetter: (_, row) => row.vehicleDescription,
        flex: 1,
      }),
      createDateTimeColumn({
        field: 'endTime',
        headerName: ctIntl.formatMessage({
          id: 'coaching.events.columnHeader.endTime',
        }),
        valueGetter: (_, row) => row.endTime,
      }),
      {
        field: 'assignedCoach',
        headerName: ctIntl.formatMessage({
          id: 'coaching.events.columnHeader.assignedCoach',
        }),
        minWidth: 160,
        valueGetter: (_, row) => row.assignedCoachGlobalUserId,
        // This is needed so that we get the most recent value on processRowUpdate
        valueSetter: (value, row) => ({
          ...row,
          assignedCoachGlobalUserId: value,
        }),
        renderCell: ({ row }) => {
          if (selectCoachAllSubUsersOptions === 'LOADING') {
            return null
          }
          if (!row.assignedCoachGlobalUserId) {
            return null
          }
          return (
            selectCoachAllSubUsersOptions.byGlobalId.get(row.assignedCoachGlobalUserId)
              ?.label ?? null
          )
        },
        flex: 1,
        editable: true,
        renderEditCell: ({ id, field, value, row }) => (
          <AssignedCoachInputCell
            id={id}
            value={value}
            field={field}
            vehicleId={row.vehicleId}
            driverId={row.driverId}
          />
        ),
      } satisfies GridColDef<DataGridRow, AssignedCoachColumnValue, string>,
      createDataGridTextColumn({
        field: 'coachingNotes',
        headerNameMsg: { id: 'Comment' },
        valueGetter: (_, row) => row.coachingNotes,
        flex: 1,
        editable: true,
      }),
      {
        field: columnsIds.priority,
        type: 'singleSelect',
        headerName: ctIntl.formatMessage({ id: 'Priority' }),
        // This is needed so that we get the most recent value on processRowUpdate
        valueSetter: (value, row) => exact({ ...row, priority: value }),
        valueGetter: (_, row) => row.priority,
        editable: true,
        renderCell: ({ row }) => {
          if (row.priority === '_UNKNOWN_') {
            return null // Unsupported priority by the FE. Might have been added recently to the DB
          }

          const { labelMsgId, color } = getCoachingPriorityLevelDetails(row.priority)
          return (
            <Chip
              label={labelMsgId}
              variant="filled"
              color={color}
            />
          )
        },
        valueOptions: priorityColumnValuesOptionsMeta.array,
        flex: 1,
        renderEditCell: ({ id, row, field }) => (
          <PriorityInputCell
            id={id}
            value={row.priority}
            field={field}
            options={priorityColumnValuesOptionsMeta.array}
          />
        ),
      } satisfies GridSingleSelectColDef<
        { label: string; value: CoachingPriorityLevelType },
        DataGridRow,
        PriorityColumnValue
      >,
      {
        type: 'actions',
        field: 'actions',
        width: Math.max(numberOfActionsAvailable * 40, 100),
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: (params) =>
          availableActions.map((action) => (
            <Fragment key={action}>
              {match(action)
                .with('view', () => (
                  <Tooltip
                    title={ctIntl.formatMessage({
                      id: 'global.datagrid.action.view',
                    })}
                    placement="top"
                    disableInteractive
                  >
                    <GridActionsCellItem
                      icon={<RemoveRedEyeIcon />}
                      label={ctIntl.formatMessage({
                        id: 'global.datagrid.action.view',
                      })}
                      onClick={() =>
                        dispatch({
                          type: 'clicked_row_view_action_icon',
                          rowId: params.row.id,
                        })
                      }
                    />
                  </Tooltip>
                ))
                .with('mark_as_coached', () => (
                  <Tooltip
                    title={ctIntl.formatMessage({
                      id: 'coaching.events.action.markAsCoached',
                    })}
                    placement="top"
                    disableInteractive
                  >
                    <GridActionsCellItem
                      icon={<ArrowCircleRightIcon />}
                      label={ctIntl.formatMessage({
                        id: 'coaching.events.action.markAsCoached',
                      })}
                      onClick={() => {
                        setCurrentModal({
                          type: 'mark_as_coached',
                          context: {
                            eventIds: [params.row.id],
                          },
                        })
                      }}
                    />
                  </Tooltip>
                ))
                .exhaustive()}
            </Fragment>
          )),
      },
    ]

    return base.map((column) => ({
      ...column,
      sortable: sortableColumnIdsArray.includes(column.field as SortableColumnId),
      filterable: filterableColumnIdsArray.includes(column.field as FilterableColumnId),
    }))
  }, [
    availableActions,
    createDateTimeColumn,
    dispatch,
    getGridDateColumnOperators,
    numberOfActionsAvailable,
    priorityColumnValuesOptionsMeta.array,
    selectCoachAllSubUsersOptions,
    tableRequisites.eventTypes,
  ])

  const rows = useMemo(
    (): ReadonlyArray<DataGridRow> =>
      selectCoachAllSubUsersOptions === 'LOADING'
        ? []
        : (toCoachCoachingEventsQuery.data?.pages.flatMap((page) => page.rows) ?? []),
    [selectCoachAllSubUsersOptions, toCoachCoachingEventsQuery.data?.pages],
  )

  const handleSelectionModalChange = (selection: GridRowSelectionModel) => {
    setRowsIdSelected(selection as Array<CoachingEventId>)
  }

  return (
    <ServerInfiniteLoadingPaginationContextProvider
      loadedPages={toCoachCoachingEventsQuery.data?.pages}
      pageSize={queryParams.serverModel.pageSize}
      getPageMetaData={useCallbackBranded(
        (page: UseToCoachCoachingEventsReturnedData['pages'][number]) => ({
          nextCursor: page.serverModelPageInfo.nextCursorRow,
          rowsCount: page.rows.length,
        }),
        [],
      )}
    >
      <UserDataGridWithSavedSettingsOnIDB<DataGridRow>
        apiRef={gridApiRef}
        Component={DataGridAsTabItem}
        dataGridId="coachingToCoachEventsDataGrid"
        data-testid="coachingToCoachEventsDataGrid"
        rows={rows}
        columns={columns}
        checkboxSelection={availableActions.includes('mark_as_coached')}
        rowSelectionModel={rowsIdSelected}
        onRowSelectionModelChange={handleSelectionModalChange}
        disableRowSelectionOnClick
        disableMultipleColumnsSorting // We currently don't support multiple sorting on BE side
        hideFooterPagination
        processRowUpdate={processRowUpdate}
        onRowsScrollEnd={onDataGridRowsScrollEnd}
        sortModel={sortModel}
        onSortModelChange={onSortModelChange}
        filterDebounceMs={500} // since were doing server side-filtering, we can afford a lower debounce (to not doo too many requests)
        sortingMode="server"
        filterMode="server"
        filterModel={filterModel}
        onFilterModelChange={onFilterModelChange}
        isRowSelectable={(params) => !isNil(params.row.driverName)}
        loading={
          toCoachCoachingEventsQuery.fetchStatus === 'fetching' ||
          exportQueryFetchStatus === 'fetching' ||
          updateCoachingEvents.status === 'pending' ||
          selectCoachAllSubUsersOptions === 'LOADING'
        }
        slots={{
          toolbar: DataGridToolbarWithLoadNewEventsButtonAndExport,
          loadingOverlay: LinearProgress,
          columnMenu: ColumnMenuWithCoachingDriverFilter,
          footerRowCount:
            ServerInfiniteLoadingPaginationContextProvider.UnknownFooterRowCount,
        }}
        sx={{
          '& .MuiDataGrid-overlayWrapperInner': {
            // Allows user to click buttons within the table while loading is true
            pointerEvents: 'none',
          },
        }}
        slotProps={{
          toolbar: {
            isExporting: exportQueryFetchStatus === 'fetching',
            onExportMenuItemClick: async (item) => {
              try {
                setExportQueryFetchStatus('fetching')

                const maxExportableRows = 2000
                const eventsToExport = await fetchToCoachCoachingEvents({
                  coachingDriversFilter: queryParams.coachingDriversFilter,
                  serverModel: {
                    filter: queryParams.serverModel.filter,
                    sort: queryParams.serverModel.sort,
                    pagination: { cursor: 'start', pageSize: maxExportableRows },
                  },
                })

                // Make sure the types are the same as the rows sent to the DataGrid
                const rowsToExport: typeof rows = eventsToExport.rows

                // TODO - translations for file name
                await downloadBasicTableDataAsSheetFile({
                  fileName: `To Coach Events (Limited to ${maxExportableRows} rows)`,
                  fileExtension: match(item)
                    .with('csv', () => 'csv' as const)
                    .with('excel', () => 'xlsx' as const)
                    .exhaustive(),
                  gridApiRef,
                  rowsToExport,
                })
              } catch {
                ctToast.fire('error', 'Something went wrong, please try again')
              } finally {
                setExportQueryFetchStatus('idle')
              }
            },
            loadNewRowsButton: showingLoadNewEventsButton
              ? {
                  props: {
                    onClick: () => dispatch({ type: 'clicked_load_more_button' }),
                  },
                }
              : false,
            gridToolbarRightContent: (
              <>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AssignmentIcon />}
                  disabled={rowsIdSelected.length === 0}
                  onClick={() => {
                    setCurrentModal({
                      type: 'mark_for_coaching',
                      context: {
                        eventIds: rowsIdSelected,
                        driverIds: Array_filterMap(rows, (r, { RemoveSymbol }) =>
                          rowsIdSelected.includes(r.id) && r.driverId !== null
                            ? r.driverId
                            : RemoveSymbol,
                        ),
                        vehicleIds: Array_filterMap(rows, (r, { RemoveSymbol }) =>
                          rowsIdSelected.includes(r.id) && r.vehicleId !== null
                            ? r.vehicleId
                            : RemoveSymbol,
                        ),
                      },
                    })
                  }}
                >
                  {ctIntl.formatMessage({
                    id: 'Assign coach',
                  })}
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<ArrowCircleRightIcon />}
                  disabled={rowsIdSelected.length === 0}
                  onClick={() => {
                    setCurrentModal({
                      type: 'mark_as_coached',
                      context: {
                        eventIds: rowsIdSelected,
                      },
                    })
                  }}
                >
                  {ctIntl.formatMessage({
                    id: 'coaching.events.action.markAsCoached',
                  })}
                </Button>
              </>
            ),
          } satisfies DataGridToolbarWithLoadNewEventsButtonAndExportProps,
          columnMenu: ColumnMenuWithCoachingDriverFilter.createProps({
            columnMenuItemDriversFilterProps,
          }) as object,
        }}
      />
      {match(currentModal)
        .with(null, () => null)
        .with(
          { type: 'mark_for_coaching' },
          ({ context: { eventIds, driverIds, vehicleIds } }) => (
            <MarkForCoachingModal
              onClose={() => setCurrentModal(null)}
              eventIds={eventIds}
              driverIds={driverIds}
              vehicleIds={vehicleIds}
              event={null}
            />
          ),
        )
        .with({ type: 'mark_as_coached' }, ({ context }) => (
          <MarkAsCoachedModal
            eventIds={context.eventIds}
            onClose={() => setCurrentModal(null)}
          />
        ))
        .exhaustive()}
    </ServerInfiniteLoadingPaginationContextProvider>
  )
}

const PriorityInputCell = ({
  id,
  value,
  field,
  options,
}: {
  id: GridRowId
  value: RowModel['priority']
  field: string
  options: Array<Option>
}) => {
  const apiRef = useGridApiContext()

  return (
    <Select
      sx={{
        width: '100%',
      }}
      value={value}
      size="small"
      label="Priority"
      onChange={async (e) => {
        const isValid = await apiRef.current.setEditCellValue({
          id,
          field,
          value: e.target.value,
        })

        if (isValid) {
          apiRef.current.stopCellEditMode({ id, field })
        }
      }}
    >
      {options.map((option) => {
        const { labelMsgId, color } = getCoachingPriorityLevelDetails(option.value)
        return (
          <MenuItem
            key={option.value}
            value={option.value}
          >
            <Chip
              id={option.value}
              label={labelMsgId}
              variant="filled"
              color={color}
            />
          </MenuItem>
        )
      })}
    </Select>
  )
}

const AssignedCoachInputCell = ({
  id,
  value,
  field,
  vehicleId,
  driverId,
}: {
  id: GridRowId
  value: AssignedCoachColumnValue | undefined
  field: string
  vehicleId: VehicleId | null
  driverId: DriverId | null
}) => {
  const apiRef = useGridApiContext()

  const selectCoachOptions = useCoachOptions({
    vehicleIds: vehicleId ? [vehicleId] : [],
    driverIds: driverId ? [driverId] : [],
  })

  return (
    <>
      <Autocomplete
        sx={{
          width: '100%',
        }}
        value={
          selectCoachOptions !== 'LOADING' && value
            ? selectCoachOptions.byGlobalId.get(value)
            : null
        }
        size="medium"
        {...getAutocompleteVirtualizedProps({
          options: selectCoachOptions === 'LOADING' ? [] : selectCoachOptions.array,
        })}
        loading={selectCoachOptions === 'LOADING'}
        loadingText={`${ctIntl.formatMessage({ id: 'loading' })}...`}
        onChange={async (_, coach) => {
          if (coach !== null) {
            const isValid = await apiRef.current.setEditCellValue({
              id,
              field,
              value: (coach ? coach.value : null) satisfies AssignedCoachColumnValue,
            })

            if (isValid) {
              apiRef.current.stopCellEditMode({ id, field })
            }
          }
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            label={ctIntl.formatMessage({ id: 'coaching.selectCoach' })}
          />
        )}
      />
    </>
  )
}
