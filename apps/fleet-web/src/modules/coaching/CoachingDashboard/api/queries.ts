import { useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'

import { apiCallerNoX } from 'api/api-caller'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import type { ClientUserId, DriverGroupId, DriverId } from 'api/types'
import { parseBETranslationId } from 'api/utils'
import { getCoachingVehicleHoursToCheckForInactivitySetting } from 'duxs/user-sensitive-selectors'
import { useTypedSelector } from 'src/redux-hooks'
import type { PromiseResolvedType } from 'src/types'
import { minutesToMs, toImmutable } from 'src/util-functions/functional-utils'
import { getStartAndEndOfDayForRange } from 'src/util-functions/luxon/utils'
import {
  Array_filterMap,
  Array_forEach,
  Array_sort,
} from 'src/util-functions/performance-critical-utils'
import { createQuery } from 'src/util-functions/react-query-utils'
import { isNilOrEmptyString } from 'src/util-functions/string-utils'
import { isTrue } from 'src/util-functions/validation'

import {
  driverGroupHierarchyLevelIdSchema,
  userGroupHierarchyLevelIdSchema,
  type DashboardFilterType,
  type DriverGroupHierarchyLevelId,
  type DriverType,
  type FetchCoachingDashboardData,
  type FetchCoachingDashboardFilters,
  type UserGroupHierarchyLevelId,
} from './types'

/******************************************** Get dashboard static filters ********************************************/
export function useCoachingDashboardFiltersQuery() {
  return useQuery(coachingDashboardFiltersQuery())
}

export const coachingDashboardFiltersQuery = () =>
  createQuery({
    queryKey: ['fetchCoachingDashboardFilters'] as const,
    queryFn: fetchCoachingDashboardFilters,
    // Since we have a decent amount of stale time, it's important to invalidate this query when any of the returned data changes
    staleTime: minutesToMs(10),
    ...makeQueryErrorHandlerWithToast(),
  })

async function fetchCoachingDashboardFilters() {
  const { dashboardFilter: filters }: FetchCoachingDashboardFilters.ApiOutput =
    await apiCallerNoX('ct_fleet_coaching_dashboard_filter')

  const driversById = new Map<DriverId, DriverType>()
  const driversArray: Array<DriverType> = []

  for (const rawDriver of filters.drivers) {
    const driver: DriverType = {
      id: rawDriver.driverId as DriverId,
      label: rawDriver.driverName,
      ascendentGroupIds: new Set(rawDriver.ascendentGroupIds ?? []),
    }

    driversById.set(driver.id, driver)
    driversArray.push(driver)
  }

  const usersAndDriverGroupsJoinByDriverGroupId = new Map<
    DriverGroupId,
    Set<ClientUserId>
  >()

  const usersAndDriversJoinByDriverId = new Map<DriverId, Set<ClientUserId>>()

  Array_forEach(filters.usersAndDriverGroupsJoin, (rawJoin) => {
    const set = usersAndDriverGroupsJoinByDriverGroupId.get(rawJoin.drivergroupid)
    if (set === undefined) {
      usersAndDriverGroupsJoinByDriverGroupId.set(
        rawJoin.drivergroupid,
        new Set([rawJoin.clientuserid]),
      )
    } else {
      set.add(rawJoin.clientuserid)
    }
  })

  Array_forEach(filters.usersAndDriversJoin, (rawJoin) => {
    const set = usersAndDriversJoinByDriverId.get(rawJoin.driverid)
    if (set === undefined) {
      usersAndDriversJoinByDriverId.set(
        rawJoin.driverid,
        new Set([rawJoin.clientuserid]),
      )
    } else {
      set.add(rawJoin.clientuserid)
    }
  })

  const parsedEvent: FetchCoachingDashboardFilters.ParsedEvent = {
    usersAndDriverGroupsJoinByDriverGroupId,
    usersAndDriversJoinByDriverId,
    eventTypes: filters.eventTypes.map((value) => ({
      name: parseBETranslationId(value.event_type_translation_id, {
        defaultValueIfEmpty: value.description ?? value.code,
      }),
      value: value.id,
    })),
    drivers: {
      byId: driversById,
      array: driversArray,
    },
    driverGroups: (() => {
      if (filters.driverGroups === 'NO_ACCESS') {
        return 'NO_ACCESS'
      }

      const byId = new Map<DriverGroupId, FetchCoachingDashboardFilters.DriverGroup>()
      const rootArray: Array<FetchCoachingDashboardFilters.DriverGroupRoot> = []
      const leafsById = new Map<
        DriverGroupId,
        FetchCoachingDashboardFilters.DriverGroup
      >()
      const hierarchyLevelsOrderedFromTopToBottom = new Map<
        DriverGroupHierarchyLevelId,
        FetchCoachingDashboardFilters.DriverHierarchyLevel
      >()

      const rawLegendById = new Map<
        DriverGroupHierarchyLevelId,
        {
          level: DriverGroupHierarchyLevelId
          description: string
        }
      >()

      if (filters.driverGroups.legend)
        for (const rawObj of filters.driverGroups.legend) {
          const id = driverGroupHierarchyLevelIdSchema.parse(rawObj.level)
          const obj = {
            level: id,
            description: rawObj.description ?? '',
          }
          rawLegendById.set(obj.level, obj)
        }

      function parseDriverGroup(
        rawGroup: FetchCoachingDashboardFilters.RawDriverGroup,
      ) {
        const childrenGroupsIds =
          rawGroup.childs?.map((child) => child.group_driver_id) ?? []

        const group: FetchCoachingDashboardFilters.DriverGroup = {
          id: rawGroup.group_driver_id as DriverGroupId,
          label: rawGroup.name,
          hierarchyLevelId: driverGroupHierarchyLevelIdSchema.parse(
            rawGroup.hierarchy_level,
          ),
          parentDriverGroupId: isNilOrEmptyString(rawGroup.parent_group_driver_id)
            ? null
            : rawGroup.parent_group_driver_id,
          childrenGroupsIds,
        }

        if (childrenGroupsIds.length === 0) {
          leafsById.set(group.id, group)
        }

        byId.set(group.id, group)
        if (group.parentDriverGroupId === null) {
          rootArray.push(group)
        }

        hierarchyLevelsOrderedFromTopToBottom.set(group.hierarchyLevelId, {
          id: group.hierarchyLevelId,
          label: rawLegendById.get(group.hierarchyLevelId)?.description ?? '',
        })

        if (rawGroup.childs) {
          for (const child of rawGroup.childs) {
            parseDriverGroup(child)
          }
        }

        return
      }

      if (filters.driverGroups.groups)
        for (const rawGroup of filters.driverGroups.groups) {
          parseDriverGroup(rawGroup)
        }

      return {
        leafsById,
        byId,
        rootArraySortedByLabel: Array_sort(rootArray, (a, b) =>
          a.label.localeCompare(b.label),
        ),
        hierarchyLevelsFromTopToBottom: [
          ...hierarchyLevelsOrderedFromTopToBottom.keys(),
        ],
        hierarchyLevelsById: hierarchyLevelsOrderedFromTopToBottom,
      }
    })(),
    userGroups: (() => {
      if (filters.userGroups === 'NO_ACCESS') {
        return 'NO_ACCESS'
      }

      const byId = new Map<
        ClientUserId,
        FetchCoachingDashboardFilters.UserGroupNormalized
      >()
      const byHierarchyLevelIdById = new Map<
        UserGroupHierarchyLevelId,
        Map<
          FetchCoachingDashboardFilters.UserGroupInHierarchy['id'],
          FetchCoachingDashboardFilters.UserGroupInHierarchy
        >
      >()
      const rootArray: Array<FetchCoachingDashboardFilters.UserGroupInHierarchy> = []
      const hierarchyLevelsOrderedFromTopToBottom = new Map<
        UserGroupHierarchyLevelId,
        FetchCoachingDashboardFilters.UserHierarchyLevel
      >()

      const rawLegendById = new Map<
        UserGroupHierarchyLevelId,
        {
          level: UserGroupHierarchyLevelId
          description: string
        }
      >()

      if (filters.userGroups.legend)
        for (const rawObj of filters.userGroups.legend) {
          const id = userGroupHierarchyLevelIdSchema.parse(rawObj.level)
          const obj = {
            level: id,
            description: rawObj.description ?? '',
          }
          rawLegendById.set(obj.level, obj)
        }

      function parseUserGroup({
        childs: childs_,
        parent_client_user_id: parent_client_user_id_,
        ...rawGroup
      }: FetchCoachingDashboardFilters.RawUserGroup): void {
        const rawGroupChilds = isTrue(rawGroup.is_endpoint) ? [] : (childs_ ?? [])
        const rawGroupParentClientUserId = isNilOrEmptyString(parent_client_user_id_)
          ? null
          : parent_client_user_id_

        const parsedGroupHierarchyLevelId = userGroupHierarchyLevelIdSchema.parse(
          rawGroup.hierarchy_level,
        )

        const baseGroup: FetchCoachingDashboardFilters.UserGroup_Base = {
          id: rawGroup.client_user_id,
          label: rawGroup.user_name,
          description: rawGroup.description ?? '',
        }

        const normalizedGroup: FetchCoachingDashboardFilters.UserGroupNormalized =
          baseGroup
        byId.set(normalizedGroup.id, normalizedGroup)

        const groupInHierarchy: FetchCoachingDashboardFilters.UserGroupInHierarchy = {
          id: baseGroup.id,
          label: baseGroup.label,
          description: baseGroup.description,
          hierarchyLevelId: parsedGroupHierarchyLevelId,
          parentGroupClientUserId: rawGroupParentClientUserId,
          childrenGroupsIdsWithLevel: rawGroupChilds.map((child) => ({
            id: child.client_user_id,
            hierarchyLevelId: userGroupHierarchyLevelIdSchema.parse(
              child.hierarchy_level,
            ),
          })),
        }

        const groupMap = byHierarchyLevelIdById.get(groupInHierarchy.hierarchyLevelId)
        if (groupMap) {
          groupMap.set(groupInHierarchy.id, groupInHierarchy)
        } else {
          byHierarchyLevelIdById.set(
            groupInHierarchy.hierarchyLevelId,
            new Map([[groupInHierarchy.id, groupInHierarchy]]),
          )
        }

        if (groupInHierarchy.parentGroupClientUserId === null) {
          rootArray.push(groupInHierarchy)
        }

        hierarchyLevelsOrderedFromTopToBottom.set(parsedGroupHierarchyLevelId, {
          id: parsedGroupHierarchyLevelId,
          label: rawLegendById.get(parsedGroupHierarchyLevelId)?.description ?? '',
        })

        if (rawGroupChilds) {
          for (const child of rawGroupChilds) {
            parseUserGroup(child)
          }
        }

        return
      }

      if (filters.userGroups.groups)
        for (const rawGroup of filters.userGroups.groups) {
          parseUserGroup(rawGroup)
        }

      return {
        byId,
        rootArraySortedByLabel: Array_sort(rootArray, (a, b) =>
          a.label.localeCompare(b.label),
        ),
        byHierarchyLevelIdById,
        hierarchyLevelsFromTopToBottom: [
          ...hierarchyLevelsOrderedFromTopToBottom.keys(),
        ],
        hierarchyLevelsById: hierarchyLevelsOrderedFromTopToBottom,
      }
    })(),
    vehicleGroupsSortedByLabel: (() => {
      if (filters.vehicleGroups === 'NO_ACCESS') {
        return 'NO_ACCESS'
      }

      const array: Array<FetchCoachingDashboardFilters.VehicleGroup> = []

      for (const item of filters.vehicleGroups) {
        const parsed: FetchCoachingDashboardFilters.VehicleGroup = {
          id: item.id,
          value: item.id,
          label: item.name,
        }
        array.push(parsed)
      }

      return Array_sort(array, (a, b) => a.label.localeCompare(b.label))
    })(),
  }

  return toImmutable(parsedEvent)
}

export type FetchCoachingDashboardFiltersResolved = PromiseResolvedType<
  typeof fetchCoachingDashboardFilters
>

/******************************************* Get dashboard data ********************************************/
export function useCoachingDashboardDataQuery(params: DashboardFilterType) {
  const coachingVehicleHoursToCheckForInactivity = useTypedSelector(
    getCoachingVehicleHoursToCheckForInactivitySetting,
  )

  const apiInput = useMemo((): FetchCoachingDashboardData.ApiInput => {
    const timesRange = (() => {
      const { rangeStart, rangeEnd } = getStartAndEndOfDayForRange(params.dateRange)
      return {
        startTime: rangeStart?.toJSDate(),
        endTime: rangeEnd?.toJSDate(),
      }
    })()

    return {
      startTime: timesRange?.startTime,
      endTime: timesRange?.endTime,
      driverId: params.driver?.id,
      eventType: params.eventType?.value,
      vehicleGroup: params.vehicleGroup?.value,
      driverGroupIds: Array_filterMap(
        Object.values(params.driverGroupsByHierarchyLevel.asObject()),
        (group, { RemoveSymbol }) => group?.id ?? RemoveSymbol,
      ),
      clientUserIds: Array_filterMap(
        Object.values(params.userGroupsByHierarchyLevel.asObject()),
        (group, { RemoveSymbol }) => group?.id ?? RemoveSymbol,
      ),
      vehiclesHoursToCheckForInactivity: coachingVehicleHoursToCheckForInactivity,
    }
  }, [
    coachingVehicleHoursToCheckForInactivity,
    params.dateRange,
    params.driver?.id,
    params.driverGroupsByHierarchyLevel,
    params.eventType?.value,
    params.userGroupsByHierarchyLevel,
    params.vehicleGroup?.value,
  ])

  return useQuery(coachingDashboardDataQuery(apiInput))
}

const coachingDashboardDataQuery = (params: FetchCoachingDashboardData.ApiInput) =>
  createQuery({
    queryKey: ['fetchCoachingDashboardData', params] as const,
    queryFn: () => fetchCoachingDashboardData(params),
    ...makeQueryErrorHandlerWithToast(),
  })

async function fetchCoachingDashboardData(params: FetchCoachingDashboardData.ApiInput) {
  const { dashboardBody: data }: FetchCoachingDashboardData.ApiOutput =
    await apiCallerNoX('ct_fleet_coaching_dashboard_data', params)

  const parsedTrendData = data.trend.data.map((item) => ({
    typeId: item.terminalEventTypeId,
    currentValue: Number(item.currentValue),
    previousValue: Number(item.previousValue),
  }))

  const trendBiggestValue = parsedTrendData.reduce((acc, curr) => {
    const currentValue = curr.currentValue
    const previousValue = curr.previousValue
    if (currentValue > previousValue) {
      return currentValue > acc ? currentValue : acc
    } else {
      return previousValue > acc ? previousValue : acc
    }
  }, 0)

  const parsedEvent: FetchCoachingDashboardData.ParsedEvent = {
    fleetActiveStatus: {
      activeVehicles: data.fleetActiveStatus.activeVehicles.map((v) => ({
        id: v.vehicle,
        lastReplyDate: isNilOrEmptyString(v.lastReplyDate)
          ? null
          : new Date(v.lastReplyDate),
        registration: v.registration,
        departments: v.departments,
      })),
      inactiveVehicles: data.fleetActiveStatus.inactiveVehicles.map((v) => ({
        id: v.vehicle,
        lastReplyDate: isNilOrEmptyString(v.lastReplyDate)
          ? null
          : new Date(v.lastReplyDate),
        registration: v.registration,
        departments: v.departments,
      })),
    },
    eventStatus: {
      newEvents: Number(data.eventStatus.newEvents),
      completedEvents: Number(data.eventStatus.completedEvents),
      unassignedEvents: Number(data.eventStatus.unassignedEvents),
    },
    trend: {
      data: parsedTrendData,
      totalValue: trendBiggestValue * 1.2,
    },
  }
  return toImmutable(parsedEvent)
}

export type FetchCoachingDashboardDataResolved = PromiseResolvedType<
  typeof fetchCoachingDashboardData
>
