# Business aware components

This folder should only contain components that are somehow **aware of business logic** and are used **across modules**, e.g:

- An `IconButton` component that shows a different icons for different statuses of a driver.
- A `TextField` that uses specific user settings to decide which way to format text.

### These components can be one of **2** different types:

- Connected
  - Components that are literally connected to the store to use a certain piece of logic. Can contain side effects, e.g:
    - A `UserDecimalNumberInputField` component that reads user settings from redux to determine which decimal separator to use to format numbers.

      Although `UserDecimalNumberInputField` will **only** work when the user is logged in (**which is a side effect**), it's still a pattern that is worth extracting for reuse.

- Unconnected
  - Although these contain logic, they don't require the app to be in a certain state to work (like **Connected** components do). They should also **not** have side effects.
