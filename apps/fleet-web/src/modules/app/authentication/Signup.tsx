import { useMemo, useState } from 'react'
import { Form, Formik } from 'formik'
import { String_toLowerCase, String_toUpperCase } from '@karoo/utils'
import { getCountryCallingCode } from 'react-phone-number-input/input'
import { useDispatch } from 'react-redux'
import { Link, type RouteComponentProps } from 'react-router-dom'
import styled from 'styled-components'
import type { Except } from 'type-fest'
import * as yup from 'yup'

import type { GetPreLoginData } from 'api/user/types'
import {
  getCtCountriesList,
  getStandardizedDefaultCountrySetting,
  getUserStyleAppName,
} from 'duxs/user'
import Icon from 'src/components/Icon'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
import { useTypedSelector } from 'src/redux-hooks'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import {
  cellPhoneSchema,
  emailSchema,
  getInputProps,
  setDirectFormikValue,
} from 'src/shared/formik'
import { messages } from 'src/shared/forms/messages'
import { ctIntl } from 'src/util-components/ctIntl'
import type { PhoneInputCountryPhoneCode } from 'src/util-components/phone-input'
import { isNilOrTrimmedEmptyString } from 'src/util-functions/string-utils'

import { Button, Checkbox, InputDropdown, PhoneInput, TextInput } from 'cartrack-ui-kit'
import AuthWrapper from './components/auth-wrapper'
import {
  AuthenticationModalContainer,
  ModalButtons,
  ModalSubtitle,
  ModalTitle,
} from './components/modal-styles'
import { Header, Instruction, StyledTabGroup } from './get-details'
import { getSignUpApiStatus, submittedSignUpForm } from './slice'

type CountryDropdownOption = {
  name: string
  value: GetPreLoginData.CtCountry['country_code']
}

const sourceList = [
  {
    default: false,
    name: 'Google',
    value: 'Google',
  },
  {
    default: false,
    name: 'Facebook',
    value: 'Facebook',
  },
  {
    default: false,
    name: 'Twitter',
    value: 'Twitter',
  },
  {
    default: false,
    name: 'Family or Friend',
    value: 'Family or Friend',
  },
  {
    default: false,
    name: 'Other',
    value: 'Other',
  },
]

const validationSchema = yup.object().shape({
  type: yup.string().oneOf(['personal', 'business']),
  name: yup.string().required(),
  companyName: yup.mixed().when('type', {
    is: 'business',
    then: yup.string().required(),
  }),
  email: emailSchema.required(),
  countryCode: yup.string<GetPreLoginData.CtCountry['country_code']>().required(),
  phoneCountryCode: yup.string<`+${string}`>().required(),
  phone: cellPhoneSchema.required(),
  ownCar: yup.mixed().when('type', {
    is: 'personal',
    then: yup.boolean().required(),
  }),
  numberVehicles: yup.mixed().when('type', {
    is: 'business',
    then: yup
      .number()
      .typeError(messages.validNumber)
      .integer(messages.validInteger)
      .min(1, ({ min }) =>
        ctIntl.formatMessage({ id: messages.validNumberMin }, { values: { min } }),
      )
      .required(messages.required),
  }),
  source: yup.string().required(),
})

type Props = RouteComponentProps
type ValidSchema = ReturnType<(typeof validationSchema)['validateSync']>
type PossibleSchema = Except<ValidSchema, 'countryCode' | 'phoneCountryCode'> & {
  countryCode: ValidSchema['countryCode'] | null
  phoneCountryCode: ValidSchema['phoneCountryCode'] | ''
}

type CountryToVerify = {
  name: string
  code: Lowercase<PhoneInputCountryPhoneCode['value']>
}

function Signup({ history }: Props) {
  const dispatch = useDispatch()
  const ctCountriesList = useTypedSelector(getCtCountriesList)

  const countryListByCode = useMemo(() => {
    const countries = new Map<
      ValidSchema['countryCode'],
      {
        code: ValidSchema['countryCode']
        name: string
      }
    >()
    for (const country of ctCountriesList) {
      countries.set(country.country_code, {
        code: country.country_code,
        name: country.label,
      })
    }

    return countries
  }, [ctCountriesList])
  const signUpApiStatus = useTypedSelector(getSignUpApiStatus)
  const companyName = useTypedSelector(getUserStyleAppName)
  const defaultCountrySetting = useTypedSelector(getStandardizedDefaultCountrySetting)

  const [phoneInputResetKey, setPhoneInputResetKey] = useState(0)
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false)
  const [currentModal, setCurrentModal] = useState<{
    type: 'different-country'
    params: {
      signUpCountry: CountryToVerify
      phoneCountry: CountryToVerify
    }
  } | null>(null)

  const initialValues: PossibleSchema = {
    type: 'personal',
    name: '',
    companyName: '',
    email: '',
    countryCode: defaultCountrySetting
      ? String_toLowerCase(defaultCountrySetting)
      : null,
    phoneCountryCode: '',
    phone: '',
    ownCar: false,
    numberVehicles: '',
    source: '',
  }

  const [phoneCountryToVerify, setPhoneCountryToVerify] = useState(
    (): CountryToVerify | null => {
      if (!initialValues.countryCode) {
        return null
      }
      const nonNullableDefaultCountry = String_toLowerCase(initialValues.countryCode)
      const countryFound = countryListByCode.get(nonNullableDefaultCountry)

      return {
        name: countryFound?.name ?? '',
        code: nonNullableDefaultCountry,
      }
    },
  )

  useEffectExceptOnMount(() => {
    if (signUpApiStatus === 'succeeded') {
      setIsSuccessModalOpen(true)
    }
  }, [signUpApiStatus])

  const getPhoneNumberSuffix = (phone: string, phoneCountryCode: `+${string}` | '') =>
    phone.replace(phoneCountryCode, '')

  return (
    <AuthWrapper>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={(validValues_) => {
          const validValues = validValues_ as ValidSchema
          const { phone, ...values } = validValues
          dispatch(
            submittedSignUpForm({
              ...values,
              // Sub optimal but currently has to be done for the sign_up endpoint
              phone: getPhoneNumberSuffix(phone, values.phoneCountryCode),
            }),
          )
        }}
      >
        {(form) => {
          const { forceOriginalValue, ...phoneProps } = getInputProps(form, 'phone')
          const signUpCountryToVerify = form.values.countryCode
            ? (countryListByCode.get(form.values.countryCode) ?? null)
            : null

          return (
            <Form>
              <GoBack onClick={() => history.goBack()}>
                <Icon icon={['fal', 'long-arrow-left']} />
                <span>{ctIntl.formatMessage({ id: 'Back' })}</span>
              </GoBack>
              <Header>
                {ctIntl.formatMessage(
                  {
                    id: `signup.header`,
                  },
                  {
                    values: {
                      companyName,
                    },
                  },
                )}
              </Header>
              <Instruction>
                {ctIntl.formatMessage(
                  {
                    id: 'signup.instruction',
                  },
                  {
                    values: {
                      companyName,
                    },
                  },
                )}
              </Instruction>
              <StyledTabGroup
                options={[
                  {
                    name: 'Personal',
                    value: 'personal',
                  },
                  {
                    name: 'Business',
                    value: 'business',
                  },
                ]}
                handleClick={(value) => {
                  form.setFieldValue('type', value)
                }}
                value={form.values.type}
              />
              <StyledTextInput
                {...getInputProps(form, 'name')}
                placeholder="Name"
                required
              />
              {form.values.type === 'business' && (
                <StyledTextInput
                  {...getInputProps(form, 'companyName')}
                  placeholder="Company Name"
                  required
                />
              )}
              <StyledEmailInput
                {...getInputProps(form, 'email')}
                placeholder="Email"
                required
              />
              <StyledCountryDropdown
                placeholder="Country"
                required
                options={ctCountriesList.map(
                  (country): CountryDropdownOption => ({
                    name: country.label,
                    value: country.country_code,
                  }),
                )}
                activeOption={form.values.countryCode}
                onChange={(country: CountryDropdownOption['value']) => {
                  setDirectFormikValue(form, 'countryCode', country)
                  const actualPhoneNumber = getPhoneNumberSuffix(
                    form.values.phone,
                    form.values.phoneCountryCode,
                  )

                  if (isNilOrTrimmedEmptyString(actualPhoneNumber)) {
                    const upperCaseCountry = String_toUpperCase(country)

                    const callingCode = getCountryCallingCode(upperCaseCountry)

                    setDirectFormikValue(form, 'phoneCountryCode', `+${callingCode}`)
                    setDirectFormikValue(form, 'phone', `+${callingCode}`)
                    setPhoneInputResetKey((prev) => prev + 1)
                  }
                }}
              />
              <PhoneInput
                {...phoneProps}
                key={phoneInputResetKey}
                defaultCountryCode={
                  form.values.countryCode
                    ? String_toUpperCase(form.values.countryCode)
                    : null
                }
                onChange={(value, phoneCountryCode, country) => {
                  if (country) {
                    setPhoneCountryToVerify({
                      name: country.label,
                      code: String_toLowerCase(country.value),
                    })
                  }

                  setDirectFormikValue(form, 'phoneCountryCode', phoneCountryCode ?? '')
                  setDirectFormikValue(form, 'phone', value ?? '')
                }}
                required
              />
              {form.values.type === 'business' ? (
                <StyledVehiclesInput
                  {...getInputProps(form, 'numberVehicles')}
                  placeholder="Number of vehicles in Fleet"
                  required
                />
              ) : (
                <StyledCheckbox
                  label="I own a car"
                  name="ownCar"
                  onChange={(value: boolean) => form.setFieldValue('ownCar', value)}
                  value={form.values.ownCar}
                />
              )}
              <StyledSourceDropdown
                placeholder="How did you hear about us?"
                options={sourceList}
                activeOption={form.values.source}
                onChange={(source: string) => form.setFieldValue('source', source)}
              />
              <Button
                fullWidth
                label="Sign Up"
                onClick={() => {
                  if (
                    phoneCountryToVerify &&
                    signUpCountryToVerify &&
                    phoneCountryToVerify.code !== signUpCountryToVerify.code
                  ) {
                    setCurrentModal({
                      type: 'different-country',
                      params: {
                        signUpCountry: signUpCountryToVerify,
                        phoneCountry: phoneCountryToVerify,
                      },
                    })
                  } else {
                    form.handleSubmit()
                  }
                }}
                disabled={!form.isValid}
              />
              {currentModal?.type === 'different-country' &&
                (() => {
                  const countryToVerify = currentModal.params
                  return (
                    <AuthenticationModalContainer isOpen>
                      <div>
                        <ModalTitle>
                          {ctIntl.formatMessage({ id: 'Attention' })}
                        </ModalTitle>
                        <ModalSubtitle>
                          {ctIntl.formatMessage(
                            {
                              id: 'signup.modal.diffCountries.content.mainMsg',
                            },
                            {
                              values: {
                                phoneNumberCountry: countryToVerify.phoneCountry.name,
                                signUpCountry: countryToVerify.signUpCountry.name,
                              },
                            },
                          )}
                        </ModalSubtitle>
                        <ModalSubtitle>
                          {ctIntl.formatMessage(
                            {
                              id: 'signup.modal.diffCountries.content.confirmMsg',
                            },
                            {
                              values: {
                                signUpCountry: countryToVerify.signUpCountry.name,
                              },
                            },
                          )}
                        </ModalSubtitle>
                        <ModalButtons>
                          <ModalButton
                            label="Cancel"
                            onClick={() => setCurrentModal(null)}
                          />
                          <ModalButton
                            onClick={() => {
                              setCurrentModal(null)
                              form.handleSubmit()
                            }}
                            solidGreen
                            label="Confirm"
                          />
                        </ModalButtons>
                      </div>
                    </AuthenticationModalContainer>
                  )
                })()}
            </Form>
          )
        }}
      </Formik>
      <AuthenticationModalContainer isOpen={isSuccessModalOpen}>
        <div>
          <ModalTitle>{ctIntl.formatMessage({ id: 'Signup success' })}</ModalTitle>
          <ModalSubtitle>
            {ctIntl.formatMessage({
              id: 'Your information has been sent, you will be contacted soon.',
            })}
          </ModalSubtitle>
          <Link to="/welcome">
            <ModalButton
              solidGreen
              label="OK"
            />
          </Link>
        </div>
      </AuthenticationModalContainer>
    </AuthWrapper>
  )
}

export default Signup

export const GoBack = styled.div`
  color: #9b9b9b;
  cursor: pointer;
  /* limits clickable area instead of full width */
  display: inline-flex;
  align-items: center;
  margin-bottom: ${spacing[5]};
  float: left;

  svg {
    font-size: 24px;
    margin-right: ${spacing[2]};
  }

  span {
    font-size: 14px;
  }
`

const StyledTextInput = styled(TextInput).attrs({
  extraClassNames: {
    errorClassNames: 'styled-error',
    placeholderClassNames: 'styled-placeholder',
  },
})`
  margin-bottom: ${spacing[2]};

  /** Styles below allow for errors to extend height of the input instead of being partially hidden */
  &&& {
    height: initial;
  }

  /** Very sub optimal but it's the way I found to get the desired behavior. I'm sorry :( */
  & .styled-placeholder {
    top: 14px;
    transform: translate(0px, -5px);
  }

  & .styled-error {
    text-align: left;
    left: inherit;
    position: inherit;
  }
`

const StyledEmailInput = styled(StyledTextInput)`
  margin-bottom: ${spacing[4]};
`

const StyledCountryDropdown = styled(InputDropdown)`
  margin-bottom: ${spacing[2]};
`

const StyledCheckbox = styled(Checkbox)`
  margin: ${spacing[1]} 0;
`

const StyledVehiclesInput = styled(StyledTextInput)`
  margin-top: ${spacing[4]};
`

const StyledSourceDropdown = styled(InputDropdown)`
  margin-bottom: ${spacing[4]};

  .InputDropdown-options {
    max-height: 82px;
  }
`

const ModalButton = styled(Button)`
  margin: 0 auto;
`
