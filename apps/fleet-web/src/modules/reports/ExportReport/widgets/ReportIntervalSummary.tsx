import { useMemo } from 'react'
import { isEmpty } from 'lodash'
import {
  Box,
  FormHelperText,
  Stack,
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineOppositeContent,
  timelineOppositeContentClasses,
  TimelineSeparator,
  Typography,
} from '@karoo-ui/core'
import InfoIcon from '@mui/icons-material/InfoOutlined'
import { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import { getLocale } from 'duxs/user'
import { useTypedSelector } from 'src/redux-hooks'
import type { ExcludeStrict } from 'src/types/utils'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import { formatOrdinals, mapDayOfWeekToText } from 'src/util-functions/format-utils'

import type { RecurringExportFormPossible } from '../export-form-schema'
import {
  DataToReceiveCustomPeriodOptions,
  DataToReceiveDurationOptions,
  formatIntervalSummaryDate,
  getIntervalSummaryDates,
  ReportFrequencyOptions,
} from '../util'
import { getRunDatePeriodForOneMonthAgoTillDate } from './utils'

type Props = {
  fieldValues: {
    reportFrequency: RecurringExportFormPossible['repeatInterval']['reportFrequency']
    intervalDaysOfWeek: RecurringExportFormPossible['repeatInterval']['intervalDaysOfWeek']
    intervalDayOfMonth: RecurringExportFormPossible['repeatInterval']['intervalDayOfMonth']
    customInterval: RecurringExportFormPossible['repeatInterval']['customInterval']
    sendDate: RecurringExportFormPossible['sendDate']
    dataToReceiveDuration: RecurringExportFormPossible['dataDuration']['dataToReceiveDuration']
    dataToReceiveCustomPeriod: RecurringExportFormPossible['dataDuration']['dataToReceiveCustomPeriod']
  }
}

export default function ReportIntervalSummary({ fieldValues }: Props) {
  const locale = useTypedSelector(getLocale) as ExcludeStrict<
    ReturnType<typeof getLocale>,
    undefined
  >

  const {
    reportFrequency,
    intervalDaysOfWeek,
    intervalDayOfMonth,
    customInterval,
    sendDate,
    dataToReceiveDuration,
    dataToReceiveCustomPeriod,
  } = fieldValues

  const intervalSummary = useMemo(() => {
    let dataDurationFirstRun = ''
    let dataDurationSecondRun = ''

    const { dayOfFirstRun, dayOfSecondRun } = getIntervalSummaryDates({
      sendDate,
      reportFrequency,
      intervalDaysOfWeek,
      intervalDayOfMonth,
      customInterval,
    })

    if (!dayOfFirstRun || !dayOfSecondRun) {
      return null
    }

    match(dataToReceiveDuration)
      .with(DataToReceiveDurationOptions.PREVIOUS_DAY, () => {
        const dataPeriodDateFirstRun = dayOfFirstRun.minus({ day: 1 })
        const dataPeriodDateSecondRun = dayOfSecondRun.minus({ day: 1 })
        dataDurationFirstRun = ctIntl.formatMessage(
          { id: 'reports.exportDrawer.dataDuration.oneDay' },
          {
            values: {
              date: dataPeriodDateFirstRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
            },
          },
        )
        dataDurationSecondRun = ctIntl.formatMessage(
          { id: 'reports.exportDrawer.dataDuration.oneDay' },
          {
            values: {
              date: dataPeriodDateSecondRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
            },
          },
        )
      })
      .with(
        DataToReceiveDurationOptions.PREVIOUS_7_DAYS,
        DataToReceiveDurationOptions.PREVIOUS_30_DAYS,
        () => {
          const dataPeriodStartDateFirstRun = dayOfFirstRun.minus({
            days:
              dataToReceiveDuration === DataToReceiveDurationOptions.PREVIOUS_7_DAYS
                ? 7
                : 30,
          })
          const dataPeriodEndDateFirstRun = dayOfFirstRun.minus({ day: 1 })
          const dataPeriodStartDateSecondRun = dayOfSecondRun.minus({
            days:
              dataToReceiveDuration === DataToReceiveDurationOptions.PREVIOUS_7_DAYS
                ? 7
                : 30,
          })
          const dataPeriodEndDateSecondRun = dayOfSecondRun.minus({ day: 1 })

          dataDurationFirstRun = ctIntl.formatMessage(
            { id: 'reports.exportDrawer.dataDuration.multipleDays' },
            {
              values: {
                startDate: dataPeriodStartDateFirstRun.toLocaleString(
                  DateTime.DATE_MED_WITH_WEEKDAY,
                ),
                endDate: dataPeriodEndDateFirstRun.toLocaleString(
                  DateTime.DATE_MED_WITH_WEEKDAY,
                ),
              },
            },
          )
          dataDurationSecondRun = ctIntl.formatMessage(
            { id: 'reports.exportDrawer.dataDuration.multipleDays' },
            {
              values: {
                startDate: dataPeriodStartDateSecondRun.toLocaleString(
                  DateTime.DATE_MED_WITH_WEEKDAY,
                ),
                endDate: dataPeriodEndDateSecondRun.toLocaleString(
                  DateTime.DATE_MED_WITH_WEEKDAY,
                ),
              },
            },
          )
        },
      )
      .with(DataToReceiveDurationOptions.PREVIOUS_MONTH, () => {
        const dataPeriodStartDateFirstRun = dayOfFirstRun
          .minus({ month: 1 })
          .startOf('month')
        const dataPeriodEndDateFirstRun = dataPeriodStartDateFirstRun.endOf('month')
        const dataPeriodStartDateSecondRun = dayOfSecondRun
          .minus({ month: 1 })
          .startOf('month')
        const dataPeriodEndDateSecondRun = dataPeriodStartDateSecondRun.endOf('month')

        dataDurationFirstRun = ctIntl.formatMessage(
          { id: 'reports.exportDrawer.dataDuration.multipleDays' },
          {
            values: {
              startDate: dataPeriodStartDateFirstRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
              endDate: dataPeriodEndDateFirstRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
            },
          },
        )
        dataDurationSecondRun = ctIntl.formatMessage(
          { id: 'reports.exportDrawer.dataDuration.multipleDays' },
          {
            values: {
              startDate: dataPeriodStartDateSecondRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
              endDate: dataPeriodEndDateSecondRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
            },
          },
        )
      })
      .with(DataToReceiveDurationOptions.ONE_MONTH_AGO_TO_DATE, () => {
        const {
          startDate: dataPeriodStartDateFirstRun,
          endDate: dataPeriodEndDateFirstRun,
        } = getRunDatePeriodForOneMonthAgoTillDate(dayOfFirstRun)

        const {
          startDate: dataPeriodStartDateSecondRun,
          endDate: dataPeriodEndDateSecondRun,
        } = getRunDatePeriodForOneMonthAgoTillDate(dayOfSecondRun)

        dataDurationFirstRun = ctIntl.formatMessage(
          { id: 'reports.exportDrawer.dataDuration.multipleDays' },
          {
            values: {
              startDate: dataPeriodStartDateFirstRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
              endDate: dataPeriodEndDateFirstRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
            },
          },
        )
        dataDurationSecondRun = ctIntl.formatMessage(
          { id: 'reports.exportDrawer.dataDuration.multipleDays' },
          {
            values: {
              startDate: dataPeriodStartDateSecondRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
              endDate: dataPeriodEndDateSecondRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
            },
          },
        )
      })
      .with(DataToReceiveDurationOptions.BEGINNING_OF_THE_MONTH_TILL_DATE, () => {
        const dataPeriodStartDateFirstRun = dayOfFirstRun.startOf('month')
        const dataPeriodEndDateFirstRun = dataPeriodStartDateFirstRun.hasSame(
          dayOfFirstRun,
          'day',
        )
          ? dayOfFirstRun
          : dayOfFirstRun.minus({ day: 1 })
        const dataPeriodStartDateSecondRun = dayOfSecondRun.startOf('month')
        const dataPeriodEndDateSecondRun = dataPeriodStartDateSecondRun.hasSame(
          dayOfSecondRun,
          'day',
        )
          ? dayOfSecondRun
          : dayOfSecondRun.minus({ day: 1 })

        dataDurationFirstRun = ctIntl.formatMessage(
          { id: 'reports.exportDrawer.dataDuration.multipleDays' },
          {
            values: {
              startDate: dataPeriodStartDateFirstRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
              endDate: dataPeriodEndDateFirstRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
            },
          },
        )
        dataDurationSecondRun = ctIntl.formatMessage(
          { id: 'reports.exportDrawer.dataDuration.multipleDays' },
          {
            values: {
              startDate: dataPeriodStartDateSecondRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
              endDate: dataPeriodEndDateSecondRun.toLocaleString(
                DateTime.DATE_MED_WITH_WEEKDAY,
              ),
            },
          },
        )
      })
      .with(DataToReceiveDurationOptions.CUSTOM_DURATION, () => {
        if (!isEmpty(dataToReceiveCustomPeriod)) {
          const dataPeriodStartDateFirstRun = match(dataToReceiveCustomPeriod[1])
            .with(DataToReceiveCustomPeriodOptions.HOURS, () =>
              dayOfFirstRun.minus({
                days: 1,
                hours: Number(dataToReceiveCustomPeriod[0]),
              }),
            )
            .with(DataToReceiveCustomPeriodOptions.DAYS, () =>
              dayOfFirstRun.minus({ days: Number(dataToReceiveCustomPeriod[0]) }),
            )
            .with(DataToReceiveCustomPeriodOptions.WEEKS, () =>
              dayOfFirstRun.minus({ weeks: Number(dataToReceiveCustomPeriod[0]) }),
            )
            .with(DataToReceiveCustomPeriodOptions.MONTHS, () =>
              dayOfFirstRun.minus({
                months: Number(dataToReceiveCustomPeriod[0]),
              }),
            )
            .exhaustive()
          const dataPeriodEndDateFirstRun = dayOfFirstRun.minus({ day: 1 })
          const dataPeriodStartDateSecondRun = match(dataToReceiveCustomPeriod[1])
            .with(DataToReceiveCustomPeriodOptions.HOURS, () =>
              dayOfSecondRun.minus({
                days: 1,
                hours: Number(dataToReceiveCustomPeriod[0]),
              }),
            )
            .with(DataToReceiveCustomPeriodOptions.DAYS, () =>
              dayOfSecondRun.minus({ days: Number(dataToReceiveCustomPeriod[0]) }),
            )
            .with(DataToReceiveCustomPeriodOptions.WEEKS, () =>
              dayOfSecondRun.minus({
                weeks: Number(dataToReceiveCustomPeriod[0]),
              }),
            )
            .with(DataToReceiveCustomPeriodOptions.MONTHS, () =>
              dayOfSecondRun.minus({
                months: Number(dataToReceiveCustomPeriod[0]),
              }),
            )
            .exhaustive()
          const dataPeriodEndDateSecondRun = dayOfSecondRun.minus({ day: 1 })
          dataDurationFirstRun = ctIntl.formatMessage(
            { id: 'reports.exportDrawer.dataDuration.multipleDays' },
            {
              values: {
                startDate: dataPeriodStartDateFirstRun.toLocaleString(
                  fieldValues.dataToReceiveCustomPeriod[1] ===
                    DataToReceiveCustomPeriodOptions.HOURS
                    ? DateTime.DATETIME_MED_WITH_WEEKDAY
                    : DateTime.DATE_MED_WITH_WEEKDAY,
                ),
                endDate: dataPeriodEndDateFirstRun.toLocaleString(
                  fieldValues.dataToReceiveCustomPeriod[1] ===
                    DataToReceiveCustomPeriodOptions.HOURS
                    ? DateTime.DATETIME_MED_WITH_WEEKDAY
                    : DateTime.DATE_MED_WITH_WEEKDAY,
                ),
              },
            },
          )
          dataDurationSecondRun = ctIntl.formatMessage(
            { id: 'reports.exportDrawer.dataDuration.multipleDays' },
            {
              values: {
                startDate: dataPeriodStartDateSecondRun.toLocaleString(
                  fieldValues.dataToReceiveCustomPeriod[1] ===
                    DataToReceiveCustomPeriodOptions.HOURS
                    ? DateTime.DATETIME_MED_WITH_WEEKDAY
                    : DateTime.DATE_MED_WITH_WEEKDAY,
                ),
                endDate: dataPeriodEndDateSecondRun.toLocaleString(
                  fieldValues.dataToReceiveCustomPeriod[1] ===
                    DataToReceiveCustomPeriodOptions.HOURS
                    ? DateTime.DATETIME_MED_WITH_WEEKDAY
                    : DateTime.DATE_MED_WITH_WEEKDAY,
                ),
              },
            },
          )
        }
      })
      .otherwise(() => null)

    return {
      firstRun: {
        date: formatIntervalSummaryDate(
          dayOfFirstRun,
          fieldValues.dataToReceiveCustomPeriod[1] ===
            DataToReceiveCustomPeriodOptions.HOURS,
        ),
        dataDuration: dataDurationFirstRun,
      },
      secondRun: {
        date: formatIntervalSummaryDate(
          dayOfSecondRun,
          fieldValues.dataToReceiveCustomPeriod[1] ===
            DataToReceiveCustomPeriodOptions.HOURS,
        ),
        dataDuration: dataDurationSecondRun,
      },
    }
  }, [
    sendDate,
    reportFrequency,
    intervalDaysOfWeek,
    intervalDayOfMonth,
    customInterval,
    dataToReceiveDuration,
    dataToReceiveCustomPeriod,
    fieldValues,
  ])

  return (
    intervalSummary && (
      <Box
        data-testid="ReportForm-Recurring-IntervalSummary"
        sx={{
          backgroundColor: '#E5F6FD',
          px: 2,
          pt: 2,
        }}
      >
        <Stack
          direction="row"
          spacing={1}
          alignItems="center"
        >
          <InfoIcon color="info" />
          <IntlTypography
            data-testid="ReportForm-Recurring-IntervalSummary-Title"
            msgProps={match(reportFrequency)
              .with(ReportFrequencyOptions.DAILY, () => ({
                id: 'reports.exportDrawer.intervalSummary.title.daily',
              }))
              .with(ReportFrequencyOptions.WEEKLY, () => ({
                id: 'reports.exportDrawer.intervalSummary.title.weekly',
                values: {
                  daysInWeek: intervalDaysOfWeek
                    .map((day) =>
                      ctIntl.formatMessage({
                        id: mapDayOfWeekToText(day as `${number}`),
                      }),
                    )
                    .join(', '),
                },
              }))
              .with(ReportFrequencyOptions.MONTHLY, () => ({
                id: 'reports.exportDrawer.intervalSummary.title.monthly',
                values: {
                  dayOfMonth: match(intervalDayOfMonth)
                    .with('last', () => ctIntl.formatMessage({ id: 'last' }))
                    .otherwise(() =>
                      formatOrdinals({
                        date:
                          intervalDayOfMonth === 'first'
                            ? '1'
                            : (intervalDayOfMonth?.split(':')[1] ?? ''),
                        locale,
                      }),
                    ),
                },
              }))
              .with(ReportFrequencyOptions.CUSTOM, () => ({
                id: 'reports.exportDrawer.intervalSummary.title.customInterval',
                values: {
                  count: customInterval[0],
                  timeUnit: ctIntl.formatMessage({ id: customInterval[1] }),
                },
              }))
              .otherwise(() => ({ id: '' }))}
          />
        </Stack>

        <Timeline
          sx={{
            [`& .${timelineOppositeContentClasses.root}`]: {
              flex: 0.2,
            },
            mb: 0,
            pb: 0,
          }}
        >
          <TimelineItem>
            <TimelineOppositeContent>
              <FormHelperText>
                {ctIntl.formatMessage({ id: 'First run' })}
              </FormHelperText>
            </TimelineOppositeContent>
            <TimelineSeparator>
              <TimelineDot color="primary" />
              <TimelineConnector sx={{ bgcolor: 'primary.light' }} />
            </TimelineSeparator>
            <TimelineContent>
              <Stack spacing={1}>
                <Typography data-testid="ReportForm-Recurring-IntervalSummary-FirstRunInterval">
                  {intervalSummary.firstRun.date}
                </Typography>
                <FormHelperText data-testid="ReportForm-Recurring-IntervalSummary-FirstRunDuration">
                  {intervalSummary.firstRun.dataDuration}
                </FormHelperText>
              </Stack>
            </TimelineContent>
          </TimelineItem>

          <TimelineItem>
            <TimelineOppositeContent>
              <FormHelperText>
                {ctIntl.formatMessage({ id: 'Next run' })}
              </FormHelperText>
            </TimelineOppositeContent>
            <TimelineSeparator>
              <TimelineDot color="primary" />
              <TimelineConnector sx={{ bgcolor: 'primary.light' }} />
            </TimelineSeparator>
            <TimelineContent>
              <Stack spacing={1}>
                <Typography data-testid="ReportForm-Recurring-IntervalSummary-SecondRunInterval">
                  {intervalSummary.secondRun.date}
                </Typography>
                <FormHelperText data-testid="ReportForm-Recurring-IntervalSummary-SecondRunDuration">
                  {intervalSummary.secondRun.dataDuration}
                </FormHelperText>
              </Stack>
            </TimelineContent>
          </TimelineItem>

          <TimelineItem sx={{ minHeight: '50px' }}>
            <TimelineOppositeContent />
            <TimelineSeparator>
              <TimelineDot color="primary" />
            </TimelineSeparator>
            <TimelineContent>
              <Typography>{ctIntl.formatMessage({ id: 'etc.' })}</Typography>
            </TimelineContent>
          </TimelineItem>
        </Timeline>
      </Box>
    )
  )
}
