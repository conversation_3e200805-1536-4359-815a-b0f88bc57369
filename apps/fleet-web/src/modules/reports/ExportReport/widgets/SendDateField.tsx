import { useRef, useState, type MouseEvent } from 'react'
import {
  Box,
  ClickAwayListener,
  DateCalendar,
  Fade,
  Paper,
  Popper,
  Stack,
  Typography,
} from '@karoo-ui/core'
import EditIcon from '@mui/icons-material/Edit'
// eslint-disable-next-line no-restricted-imports
import { MultiSectionDigitalClock } from '@mui/x-date-pickers'
import { DateTime } from 'luxon'
import {
  useController,
  type Control,
  type FieldPath,
  type FieldPathValue,
  type FieldValues,
} from 'react-hook-form'

import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'

type BaseFormValue = DateTime | null | undefined

type Props<TValues extends FieldValues, TNamePath extends FieldPath<TValues>> = {
  formProps: {
    name: FieldPathValue<TValues, TNamePath> extends BaseFormValue ? TNamePath : never
    control: Control<TValues>
  }
  label: string
  secondLabel?: string
  getDisabledDate?: (date: DateTime) => boolean
  disabled?: boolean
}

export default function SendDateField<
  TValues extends FieldValues,
  TNamePath extends FieldPath<TValues>,
>({
  formProps,
  label,
  secondLabel,
  getDisabledDate,
  disabled = false,
}: Props<TValues, TNamePath>) {
  const {
    field: { value: value_, onChange: onChange_ },
  } = useController(formProps)
  const fieldValue = value_ as BaseFormValue
  const fieldOnChange = onChange_ as (event: React.ChangeEvent | BaseFormValue) => void
  const ref = useRef<HTMLElement | null>()

  const [calendarProps, setCalendarProps] = useState<{
    open: boolean
    anchorEl: null | HTMLElement
  }>({
    open: false,
    anchorEl: null,
  })

  const toggleCalendar = (event?: MouseEvent<HTMLElement>) => {
    event?.stopPropagation()
    setCalendarProps((prev) => ({
      open: !prev.open,
      anchorEl: event ? (ref.current ?? null) : null,
    }))
  }

  // FIXME: the initial value is not updated here after form's initial values are updated
  const value = fieldValue ?? DateTime.local()

  return (
    <Box
      data-testid="ReportForm-SendDate"
      sx={{ display: 'flex', alignItems: 'center', gap: 0.5, flexWrap: 'wrap' }}
    >
      <IntlTypography
        display="inline-block"
        msgProps={{ id: label }}
      />
      <Box
        display="inline-block"
        sx={{ cursor: 'pointer' }}
        ref={ref}
      >
        <Box
          sx={{ position: 'relative', bottom: '2px' }}
          {...(disabled ? {} : { onClick: toggleCalendar })}
          data-testid="ReportForm-SendDateInput"
        >
          <Typography
            color={disabled ? 'grey' : 'primary'}
            sx={{ display: 'inline-block', marginRight: '5px' }}
          >
            {`${value.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)} ${
              value.hasSame(DateTime.local(), 'day')
                ? `(${ctIntl.formatMessage({ id: 'Today' })})`
                : ''
            }`}
          </Typography>
          {secondLabel && (
            <IntlTypography
              display="inline-block"
              msgProps={{
                id: secondLabel,
                values: { time: value.toLocaleString(DateTime.TIME_SIMPLE) },
              }}
              color={disabled ? 'grey' : 'primary'}
            />
          )}
          {disabled ? null : (
            <EditIcon
              sx={{ display: 'inline-block', position: 'relative', top: '5px' }}
              fontSize="small"
              color="primary"
            />
          )}
        </Box>

        <Popper
          transition
          disablePortal
          open={calendarProps.open}
          anchorEl={calendarProps.anchorEl}
          sx={(theme) => ({ zIndex: theme.zIndex.modal })}
        >
          {({ TransitionProps }) => (
            <Fade {...TransitionProps}>
              <Paper elevation={6}>
                <ClickAwayListener
                  onClickAway={() => {
                    if (calendarProps.open) {
                      toggleCalendar()
                    }
                  }}
                >
                  <Stack
                    direction="row"
                    spacing={2}
                  >
                    <Box>
                      <DateCalendar
                        disablePast
                        value={value}
                        onChange={(newDate) => {
                          fieldOnChange(newDate)
                        }}
                        shouldDisableDate={
                          getDisabledDate && ((date) => getDisabledDate(date))
                        }
                      />
                    </Box>
                    <Box>
                      <MultiSectionDigitalClock
                        disablePast
                        value={value}
                        onChange={(newDate) => {
                          fieldOnChange(newDate)
                        }}
                      />
                    </Box>
                  </Stack>
                </ClickAwayListener>
              </Paper>
            </Fade>
          )}
        </Popper>
      </Box>
    </Box>
  )
}
