import { useMemo } from 'react'
import { isEmpty, isNil } from 'lodash'
import { Box, Button, Divider, Grid, Stack } from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import { String_toLowerCase } from '@karoo/utils'
import { DateTime } from 'luxon'
import { useForm, useWatch } from 'react-hook-form'
import { match, P } from 'ts-pattern'
import type { Except } from 'type-fest'

import type {
  PromptKnownGeofenceGroupTypeFieldName,
  PromptKnownGeofenceTypeFieldName,
  PromptKnownNumOrNumberTypeFieldName,
  PromptKnownSingleSelectTypeFieldName,
  PromptKnownStringTypeFieldName,
  PromptKnownTimeTypeFieldName,
} from 'api/reports/types'
import { getPrimaryEmailSetting } from 'duxs/user-sensitive-selectors'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
import { VEHICLE_ALL_VALUE } from 'src/modules/components/unconnected/MultipleSelect/shared'
import { useTypedSelector } from 'src/redux-hooks'
import { GA4 } from 'src/shared/google-analytics4'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import FormSelect from 'src/util-components/react-hook-form/FormSelect'

import type { Report, ReportButtonContainerProps } from '../types'
import {
  dayOfWeekTypePromptsInitialValues,
  generateScheduleExportFormSchema,
  getGeofenceTypePromptsInitialValues,
  insertPromptWidgetType,
  numberTypePromptsInitialValues,
  singleSelectTypePromptsInitialValues,
  stringTypePromptsInitialValues,
  timeTypePromptsInitialValues,
  type RecurringExportFormPossible,
  type ScheduleExportFormValidSchema,
} from './export-form-schema'
import type {
  ExportReportFormValues,
  ReportApiDataDuration,
  ReportApiRepeatInterval,
} from './types'
import {
  AVAILABLE_FILE_FORMATS,
  DataToReceiveCustomPeriodOptions,
  DataToReceiveDurationOptions,
  DURATION_OPTIONS,
  getNextDayOfWeekOrMonth,
  handlePromptDateRangeOrDate,
  handlePromptDriver,
  handlePromptRegistration,
  hydratePromptDateRangeOrStartDate,
  hydratePromptValues,
  LIST_UPDOWN_OPTIONS,
  mapDataDurationOptionsToTranslationKeys,
  ReportFrequencyCustomOptions,
  ReportFrequencyOptions,
} from './util'
import DriverAndGroupSingleList from './widgets/DriverAndGroupSingleList'
import EmailSection from './widgets/EmailSection'
import FileFormatField from './widgets/FileFormatField'
import GeofenceField from './widgets/Geofence/GeofenceField'
import GeofenceGroupField from './widgets/Geofence/GeofenceGroupField'
import CustomDataToReceiveField from './widgets/Interval/CustomDataToReceiveField'
import CustomInvervalField from './widgets/Interval/CustomInvervalField'
import DayOfMonth from './widgets/Interval/DayOfMonth'
import DaysOfWeek from './widgets/Interval/DaysOfWeek'
import PasswordField from './widgets/PasswordField'
import { useReportProfileOptions } from './widgets/queries'
import RegistrationField from './widgets/Registration/RegistrationField'
import ReportIntervalSummary from './widgets/ReportIntervalSummary'
import SendDateField from './widgets/SendDateField'
import TimeField from './widgets/TimeField'

export type FilledRecurringReport = Pick<Report, 'name' | 'prompts' | 'report_id'> & {
  fileFormats?: Report['fileFormats']
  isMifleet: boolean
  filledValues?: Except<RecurringExportFormPossible, 'dateRangeOrStartOrEnd'>
}

type Props = {
  filledReport: FilledRecurringReport
  disabled?: boolean
  onClose: () => void
  onFormSubmit: (form: ExportReportFormValues) => void
  buttonContainer?: React.ComponentType<ReportButtonContainerProps>
  buttonContainerProps?: {
    handleTabChange: (values: RecurringExportFormPossible) => void
  }
}

const availableDataToReceiveDurationOptions = Object.values(
  DataToReceiveDurationOptions,
)

export default function ExportRecurringForm({
  filledReport,
  disabled = false,
  onClose,
  onFormSubmit,
  buttonContainer: ButtonContainer,
  buttonContainerProps,
}: Props) {
  const prompts = filledReport.prompts
  const availableFormats = useMemo(
    () =>
      !filledReport.fileFormats || isEmpty(filledReport.fileFormats)
        ? AVAILABLE_FILE_FORMATS
        : filledReport.fileFormats,
    [filledReport.fileFormats],
  )

  const primaryEmailSetting = useTypedSelector(getPrimaryEmailSetting)

  const dateRangeFieldPrompt = prompts
    ? (handlePromptDateRangeOrDate({ prompts }) ?? null)
    : null
  const registrationFieldPrompt = prompts ? handlePromptRegistration(prompts) : null
  const driverFieldPrompt = prompts ? handlePromptDriver(prompts) : null

  const today = useMemo(() => DateTime.now(), [])

  const initialValues = useMemo(
    (): RecurringExportFormPossible => ({
      sendDate: DateTime.local(),
      emails: match(primaryEmailSetting)
        .with(P.nullish, () => [])
        .with(P.string, (value) => [value])
        .otherwise((values) => values),
      fileFormat: availableFormats.some((f) => f.extension === 'pdf')
        ? 'pdf'
        : availableFormats[0].extension,
      password: '',
      isPasswordRequired: Boolean(filledReport.filledValues?.password),

      // NOTE: the reason we have date range in the recurring form is we need to fill the date range prompts values
      // even though they are not used for the form
      dateRangeOrStartOrEnd: {
        dateRange:
          dateRangeFieldPrompt?.widgetType === 'dateRange' ? [today, today] : null,
        dateTimeRange:
          dateRangeFieldPrompt?.widgetType === 'dateTimeRange' ? [today, today] : null,
        startDate:
          dateRangeFieldPrompt?.widgetType === 'startDate'
            ? today.startOf('day')
            : null,
        startDateTime:
          dateRangeFieldPrompt?.widgetType === 'startDateTime' ? today : null,
        endDate: dateRangeFieldPrompt?.widgetType === 'endDate' ? today : null,
        endDateTime: dateRangeFieldPrompt?.widgetType === 'endDateTime' ? today : null,
      },

      // interval
      repeatInterval: {
        reportFrequency: ReportFrequencyOptions.DAILY,
        intervalDaysOfWeek: [],
        intervalDayOfMonth: 'first',
        customInterval: ['2', ReportFrequencyCustomOptions.DAYS],
      },

      // data duration
      dataDuration: {
        dataToReceiveDuration: DataToReceiveDurationOptions.PREVIOUS_DAY,
        dataToReceiveCustomPeriod: ['2', DataToReceiveCustomPeriodOptions.DAYS],
      },

      // existing values
      ...filledReport.filledValues,

      // prompts
      prompts: {
        registration: match(registrationFieldPrompt)
          .with(
            { widgetType: P.union('vehicleNoAll', 'vehicleNoGroupNoAll') },
            () => null,
          )
          .with({ widgetType: 'vehicleSingle' }, () => ({
            ...VEHICLE_ALL_VALUE,
            label: ctIntl.formatMessage({ id: VEHICLE_ALL_VALUE.label }),
          }))
          .with({ widgetType: 'vehicleMultiple' }, () => [])
          .otherwise(() => null),

        driver: match(driverFieldPrompt)
          .with(
            { widgetType: 'driverSingle' },
            () =>
              ({
                type: 'all',
                label: ctIntl.formatMessage({ id: 'All Drivers' }),
                value: 'all',
              }) as const,
          )
          .with({ widgetType: 'driverNoAll' }, () => null)
          .otherwise(() => null),

        ...getGeofenceTypePromptsInitialValues(),
        ...stringTypePromptsInitialValues,
        ...numberTypePromptsInitialValues,
        ...timeTypePromptsInitialValues,
        ...dayOfWeekTypePromptsInitialValues,
        ...singleSelectTypePromptsInitialValues,

        // existing prompt values
        ...filledReport.filledValues?.prompts,
      },
    }),
    [
      dateRangeFieldPrompt?.widgetType,
      driverFieldPrompt,
      primaryEmailSetting,
      availableFormats,
      filledReport.filledValues,
      registrationFieldPrompt,
      today,
    ],
  )

  const validSchema = useMemo(
    () => generateScheduleExportFormSchema({ prompts }),
    [prompts],
  )

  const form = useForm<RecurringExportFormPossible>({
    resolver: zodResolverV4(validSchema),
    mode: 'all',
    defaultValues: initialValues,
  })

  const { control, handleSubmit, getValues, setValue: setFormValue } = form

  const onSubmit = handleSubmit((_values) => {
    const values = _values as ScheduleExportFormValidSchema
    const promptValues = hydratePromptValues({ prompts, promptValues: values.prompts })
    const dateRangeValues = hydratePromptDateRangeOrStartDate({
      prompts,
      fieldValue: values.dateRangeOrStartOrEnd,
    })

    const repeatInterval = match(values.repeatInterval.reportFrequency)
      .returnType<ReportApiRepeatInterval>()
      .with(ReportFrequencyOptions.DAILY, () => '1 day')
      .with(
        ReportFrequencyOptions.WEEKLY,
        () =>
          `weekly:${
            values.repeatInterval.intervalDaysOfWeek.toString() as
              | `${number}`
              | `${number},${string}`
          }`,
      )
      .with(ReportFrequencyOptions.MONTHLY, () =>
        match(values.repeatInterval.intervalDayOfMonth)
          .returnType<ReportApiRepeatInterval>()
          .with('first', () => 'monthly:1')
          .with('last', () => 'monthly:last')
          .otherwise(() => {
            const dayOfMonth = values.repeatInterval.intervalDayOfMonth
            if (dayOfMonth?.includes('custom') && dayOfMonth?.split(':')[1]) {
              return `monthly:${dayOfMonth?.split(':')[1] as `${number}`}`
            }

            return '' as FixMeAny
          }),
      )
      .with(
        ReportFrequencyOptions.CUSTOM,
        () =>
          `${values.repeatInterval.customInterval[0]} ${match(
            values.repeatInterval.customInterval[1],
          )
            .with(ReportFrequencyCustomOptions.DAYS, () => 'day' as const)
            .with(ReportFrequencyCustomOptions.WEEKS, () => 'week' as const)
            .with(ReportFrequencyCustomOptions.MONTHS, () => 'month' as const)
            .otherwise(() => '' as FixMeAny)}`,
      )
      .otherwise(() => '' as FixMeAny)

    const dataDuration = match(values.dataDuration.dataToReceiveDuration)
      .returnType<ReportApiDataDuration>()
      .with(DataToReceiveDurationOptions.PREVIOUS_DAY, () => '1 day')
      .with(DataToReceiveDurationOptions.PREVIOUS_7_DAYS, () => '7 days')
      .with(DataToReceiveDurationOptions.PREVIOUS_30_DAYS, () => '30 days')
      .with(DataToReceiveDurationOptions.PREVIOUS_MONTH, () => 'previous month')
      .with(
        DataToReceiveDurationOptions.ONE_MONTH_AGO_TO_DATE,
        () => 'month_ago_to_date',
      )
      .with(
        DataToReceiveDurationOptions.BEGINNING_OF_THE_MONTH_TILL_DATE,
        () => 'todate',
      )
      .with(
        DataToReceiveDurationOptions.CUSTOM_DURATION,
        () =>
          // iNet does not support custom: prefix at the moment so we remove it
          `${values.dataDuration.dataToReceiveCustomPeriod[0]} ${String_toLowerCase(
            values.dataDuration.dataToReceiveCustomPeriod[1],
          )}`,
      )
      .exhaustive()

    GA4.event({
      category: 'New report',
      action: 'Scheduled report',
    })

    onFormSubmit({
      id: filledReport.report_id,
      name: filledReport.name,
      emails: values.emails,
      fileFormat: values.fileFormat,
      sendDate: values.sendDate,
      repeatInterval,
      dataDuration,
      prompts: [...(dateRangeValues ?? []), ...(promptValues ?? [])],
      ...(values.password ? { password: values.password } : {}),
    })
  })

  const reportFrequency = useWatch({ name: 'repeatInterval.reportFrequency', control })
  const intervalDayOfMonth = useWatch({
    name: 'repeatInterval.intervalDayOfMonth',
    control,
  })
  const intervalDaysOfWeek = useWatch({
    name: 'repeatInterval.intervalDaysOfWeek',
    control,
  })
  const customInterval = useWatch({ name: 'repeatInterval.customInterval', control })
  const sendDate = useWatch({ name: 'sendDate', control })
  const dataToReceiveDuration = useWatch({
    name: 'dataDuration.dataToReceiveDuration',
    control,
  })
  const dataToReceiveCustomPeriod = useWatch({
    name: 'dataDuration.dataToReceiveCustomPeriod',
    control,
  })

  // auto-select first enabled send date in case repeat interval is daily/weekly/monthly
  useEffectExceptOnMount(() => {
    if (!disabled) {
      if (reportFrequency === ReportFrequencyOptions.DAILY) {
        setFormValue('sendDate', today, {
          shouldValidate: false,
        })
      }

      if (
        !isEmpty(intervalDaysOfWeek) &&
        reportFrequency === ReportFrequencyOptions.WEEKLY
      ) {
        const todayInDOWFormat = DateTime.local().toFormat('c')
        const closestDOWWithToday =
          intervalDaysOfWeek.find((dow) => dow >= todayInDOWFormat) ||
          intervalDaysOfWeek[0]
        const firstEnabledDate = getNextDayOfWeekOrMonth(
          closestDOWWithToday as `${number}`,
          'week',
        )

        setFormValue('sendDate', firstEnabledDate as DateTime, {
          shouldValidate: false,
        })
      }

      if (intervalDayOfMonth && reportFrequency === ReportFrequencyOptions.MONTHLY) {
        match(intervalDayOfMonth)
          .with('first', () => {
            const firstEnabledDate = getNextDayOfWeekOrMonth('1', 'month')

            setFormValue('sendDate', firstEnabledDate as DateTime, {
              shouldValidate: false,
            })
          })
          .with('last', () => {
            const firstEnabledDate = DateTime.local().endOf('month')

            setFormValue('sendDate', firstEnabledDate, { shouldValidate: false })
          })
          .otherwise(() => {
            if (intervalDayOfMonth.includes('custom')) {
              const selectedDOM = intervalDayOfMonth.split(':')[1]

              if (!isNil(selectedDOM)) {
                const firstEnabledDate = getNextDayOfWeekOrMonth(
                  selectedDOM as `${number}`,
                  'month',
                )

                setFormValue('sendDate', firstEnabledDate as DateTime, {
                  shouldValidate: false,
                })
              }
            }
          })
      }
    }

    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
    JSON.stringify(intervalDaysOfWeek),
    intervalDayOfMonth,
    setFormValue,
    reportFrequency,
  ])

  // NOTE: Update the data duration when repeat interval changes, but should effect the original values
  useEffectExceptOnMount(() => {
    setFormValue(
      'dataDuration.dataToReceiveDuration',
      match(reportFrequency)
        .with(
          ReportFrequencyOptions.DAILY,
          () => DataToReceiveDurationOptions.PREVIOUS_DAY,
        )
        .with(
          ReportFrequencyOptions.WEEKLY,
          () => DataToReceiveDurationOptions.PREVIOUS_7_DAYS,
        )
        .with(
          ReportFrequencyOptions.MONTHLY,
          () => DataToReceiveDurationOptions.PREVIOUS_30_DAYS,
        )
        .with(
          ReportFrequencyOptions.CUSTOM,
          () => DataToReceiveDurationOptions.CUSTOM_DURATION,
        )
        .exhaustive(),
      { shouldValidate: false },
    )
  }, [reportFrequency, setFormValue])

  const getDisabledSendDate = (date: DateTime) =>
    match(reportFrequency)
      .with(ReportFrequencyOptions.DAILY, () => false)
      .with(ReportFrequencyOptions.WEEKLY, () => {
        if (isEmpty(intervalDaysOfWeek)) {
          return false
        }

        const dateAsDOW = date.toFormat('c') as `${number}`

        return !intervalDaysOfWeek.includes(dateAsDOW)
      })
      .with(ReportFrequencyOptions.MONTHLY, () => {
        if (isNil(intervalDayOfMonth)) {
          return false
        }

        const dateAsDOM = date.toFormat('d')

        if (intervalDayOfMonth === 'first') {
          return dateAsDOM !== '1'
        } else if (intervalDayOfMonth === 'last') {
          return !date.hasSame(date.endOf('month'), 'day')
        } else if (intervalDayOfMonth.includes('custom')) {
          const selectedDOM = intervalDayOfMonth.split(':')[1]

          if (isNil(selectedDOM)) {
            return false
          }

          return dateAsDOM !== selectedDOM
        }

        return false
      })
      .with(ReportFrequencyOptions.CUSTOM, () => false)
      .otherwise(() => false)

  const renderBottomButton = () => {
    if (!ButtonContainer) {
      return (
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="flex-end"
          sx={{ pr: 3 }}
          data-testid="ReportDrawer-Recurring-BottomButtons"
        >
          <Button
            data-testid="ReportDrawer-CancelButton"
            variant="outlined"
            color="secondary"
            onClick={onClose}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </Button>

          <Button
            data-testid="ReportDrawer-SaveButton"
            variant="contained"
            type="submit"
            onClick={onSubmit}
          >
            {ctIntl.formatMessage({ id: 'Schedule Email' })}
          </Button>
        </Stack>
      )
    }

    return (
      <ButtonContainer
        onClose={onClose}
        onSubmit={onSubmit}
        handlePrevClick={() => {
          if (buttonContainerProps) {
            buttonContainerProps.handleTabChange(getValues())
          }
        }}
      />
    )
  }

  const reportProfileOptions = useReportProfileOptions()

  return (
    <>
      <Stack
        sx={{
          ml: -3,
          px: 3, // need to have padding-left here for the components like combobox won't be cut off
          gap: 3,
          pt: 1,
          overflowX: 'hidden',
          overflowY: 'auto',
          position: 'relative', // for the absolute positioned circular progress
        }}
        data-testid="Report-RecurringExportForm"
      >
        {registrationFieldPrompt && (
          <RegistrationField
            formProps={{
              name: 'prompts.registration',
              control,
            }}
            promptName={registrationFieldPrompt.name}
            registrationFieldName={registrationFieldPrompt.widgetType}
            disabled={disabled}
            isMifleet={filledReport.isMifleet}
            showReminderMsg
          />
        )}
        {driverFieldPrompt && (
          <DriverAndGroupSingleList
            formProps={{
              name: 'prompts.driver',
              control,
            }}
            promptName={driverFieldPrompt.name}
            includeAll={driverFieldPrompt.widgetType === 'driverSingle'}
            disabled={disabled}
            isMifleet={filledReport.isMifleet}
          />
        )}
        <Grid
          container
          rowSpacing={3}
          columnSpacing={1.5}
        >
          {insertPromptWidgetType({ prompts }).map((newPrompt) =>
            match(newPrompt)
              .with({ widgetType: 'geofence' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <GeofenceField
                    label={name}
                    formProps={{
                      name: `prompts.${identifier as PromptKnownGeofenceTypeFieldName}`,
                      control,
                    }}
                    disabled={disabled}
                  />
                </Grid>
              ))
              .with({ widgetType: 'geofenceGroup' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <GeofenceGroupField
                    label={name}
                    formProps={{
                      name: `prompts.${
                        identifier as PromptKnownGeofenceGroupTypeFieldName
                      }`,
                      control,
                    }}
                    disabled={disabled}
                  />
                </Grid>
              ))
              .with({ widgetType: 'string' }, ({ identifier, name }) => (
                <Grid
                  size={6}
                  key={identifier}
                >
                  <TextFieldControlled
                    data-testid={`ReportForm-Recurring-StringType-${identifier}`}
                    ControllerProps={{
                      name: `prompts.${identifier as PromptKnownStringTypeFieldName}`,
                      control,
                    }}
                    label={name}
                    required
                    disabled={disabled}
                    fullWidth
                  />
                </Grid>
              ))
              .with({ widgetType: 'number' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <TextFieldControlled
                    data-testid={`ReportForm-Recurring-NumberType-${identifier}`}
                    disabled={disabled}
                    ControllerProps={{
                      name: `prompts.${
                        identifier as PromptKnownNumOrNumberTypeFieldName
                      }`,
                      control,
                    }}
                    type="number"
                    label={name}
                    required
                    fullWidth
                  />
                </Grid>
              ))
              .with({ widgetType: 'time' }, ({ identifier, name }) => (
                <Grid
                  size={6}
                  key={identifier}
                >
                  <TimeField
                    promptName={name}
                    disabled={disabled}
                    formProps={{
                      name: `prompts.${identifier as PromptKnownTimeTypeFieldName}`,
                      control,
                    }}
                  />
                </Grid>
              ))
              .with({ widgetType: 'singleSelect' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <FormSelect
                    formProps={{
                      name: `prompts.${
                        identifier as PromptKnownSingleSelectTypeFieldName
                      }`,
                      control,
                    }}
                    disabled={disabled}
                    label={name}
                    labelId="ExportReport-Recurring-SingleSelect"
                    dataTestId={`ReportForm-SingleSelect-${identifier}`}
                    list={match(identifier as PromptKnownSingleSelectTypeFieldName)
                      .with('updown', () =>
                        LIST_UPDOWN_OPTIONS.map((o) => ({
                          label: { id: o.label },
                          value: o.value,
                        })),
                      )
                      .with('duration', () => DURATION_OPTIONS)
                      .with('profile_id', () => reportProfileOptions)
                      .exhaustive()}
                  />
                </Grid>
              ))
              .with({ widgetType: 'daysOfWeek' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <DaysOfWeek
                    disabled={disabled}
                    formProps={{
                      name: `prompts.dayofweek`,
                      control,
                    }}
                    label={name}
                  />
                </Grid>
              ))
              .otherwise(() => null),
          )}
        </Grid>

        <Divider />

        <Grid
          container
          rowSpacing={3}
          columnSpacing={1}
        >
          <Grid size={2.5}>
            <IntlTypography
              msgProps={{
                id: 'reports.exportDrawer.receiveFrequency.label',
              }}
              sx={{
                minWidth: 'fit-content',
                alignSelf: 'flex-start',
              }}
              pt={1}
            />
          </Grid>
          <Grid size={9.5}>
            <Stack
              sx={{ maxWidth: '500px' }}
              spacing={1}
            >
              <Box sx={{ width: '300px' }}>
                <FormSelect
                  dataTestId="ReportForm-Recurring-ReportFrequency"
                  label={ctIntl.formatMessage({ id: 'Frequency' })}
                  labelId="ReportForm-Recurring-ReportFrequency"
                  formProps={{ name: 'repeatInterval.reportFrequency', control }}
                  list={Object.values(ReportFrequencyOptions).map((o) => ({
                    label: { id: o },
                    value: o,
                  }))}
                  disabled={disabled}
                />
              </Box>

              {reportFrequency === ReportFrequencyOptions.WEEKLY && (
                <DaysOfWeek
                  label="reports.exportDrawer.frequency.dateToReceive"
                  formProps={{ name: 'repeatInterval.intervalDaysOfWeek', control }}
                  disabled={disabled}
                  cypressTestId="ReportForm-Recurring-DaysOfWeek"
                />
              )}
              {reportFrequency === ReportFrequencyOptions.MONTHLY && (
                <DayOfMonth
                  formProps={{ name: 'repeatInterval.intervalDayOfMonth', control }}
                  disabled={disabled}
                />
              )}
              {reportFrequency === ReportFrequencyOptions.CUSTOM && (
                <CustomInvervalField
                  formProps={{ name: 'repeatInterval.customInterval', control }}
                  disabled={disabled}
                  sendDate={sendDate}
                />
              )}
              <SendDateField
                formProps={{ name: 'sendDate', control }}
                label={
                  disabled
                    ? 'reports.exportDrawer.frequency.nextSendAt'
                    : 'reports.exportDrawer.frequency.sendDateLabel'
                }
                secondLabel="reports.exportDrawer.frequency.sendDateSecondLabel"
                getDisabledDate={getDisabledSendDate}
                disabled={disabled}
              />
            </Stack>
          </Grid>
        </Grid>

        <Grid
          container
          rowSpacing={3}
          columnSpacing={1}
        >
          <Grid size={2.5}>
            <IntlTypography
              msgProps={{ id: 'reports.exportDrawer.dataToReceiveDuration.label' }}
              sx={{
                minWidth: 'fit-content',
                alignSelf: 'flex-start',
              }}
              pt={1}
            />
          </Grid>
          <Grid size={9.5}>
            <Stack
              sx={{ maxWidth: '500px' }}
              spacing={2}
            >
              <Stack
                gap={1}
                sx={{ width: '300px' }}
              >
                <FormSelect
                  dataTestId="ReportForm-Recurring-DataDuration"
                  label={ctIntl.formatMessage({ id: 'Data period' })}
                  labelId="ReportForm-Recurring-DataDuration"
                  formProps={{ name: 'dataDuration.dataToReceiveDuration', control }}
                  list={availableDataToReceiveDurationOptions.map((o) => ({
                    label: { id: mapDataDurationOptionsToTranslationKeys(o) },
                    value: o,
                  }))}
                  disabled={disabled}
                  {...(dataToReceiveCustomPeriod[1] !==
                    DataToReceiveCustomPeriodOptions.HOURS && {
                    helpText: 'reports.exportDrawer.dataToReceiveDuration.subtitle',
                  })}
                />
                {dataToReceiveDuration ===
                  DataToReceiveDurationOptions.CUSTOM_DURATION && (
                  <CustomDataToReceiveField
                    formProps={{
                      name: 'dataDuration.dataToReceiveCustomPeriod',
                      control,
                    }}
                    disabled={disabled}
                    sendDate={sendDate}
                  />
                )}
              </Stack>
            </Stack>
          </Grid>
        </Grid>
        {!disabled && (
          <ReportIntervalSummary
            fieldValues={{
              reportFrequency,
              intervalDayOfMonth,
              intervalDaysOfWeek,
              customInterval,
              sendDate,
              dataToReceiveDuration,
              dataToReceiveCustomPeriod,
            }}
          />
        )}
        <Divider />
        <FileFormatField
          formProps={{ name: 'fileFormat', control }}
          fileFormats={availableFormats}
          disabled={disabled}
        />
        <EmailSection
          formProps={{ name: 'emails', control }}
          disabled={disabled}
        />
        <PasswordField
          formProps={{ name: 'password', control }}
          disabled={disabled}
        />
      </Stack>
      {renderBottomButton()}
    </>
  )
}
