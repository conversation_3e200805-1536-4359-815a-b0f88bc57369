import { isEmpty, isNil } from 'lodash'
import { String_split } from '@karoo/utils'
import { DateTime } from 'luxon'
import type { FieldError } from 'react-hook-form'
import * as R from 'remeda'
import { match, P } from 'ts-pattern'
import type { Except } from 'type-fest'

import {
  numOrNumberTypeKnownPromptFieldNames,
  PROMPT_KNOWN_DATE_OR_DATETIME_TYPE_FIELD_NAME,
  PROMPT_KNOWN_DATE_RANGE_FIELD_TYPE,
  PROMPT_KNOWN_DAY_OF_WEEK_TYPE_FIELD_NAME,
  PROMPT_KNOWN_GEOFENCE_GROUP_TYPE_FIELD_NAME,
  PROMPT_KNOWN_GEOFENCE_TYPE_FIELD_NAME,
  singleSelectTypeKnownPromptFieldNames,
  stringTypeKnownPromptFieldNames,
  timeTypeKnownPromptFieldNames,
  type DayOfWeekValuePromptTypeApiInput,
  type DriverValuePromptTypeApiInput,
  type GeofenceGroupValuePromptTypeApiInput,
  type GeofenceValuePromptTypeApiInput,
  type NumberValuePromptTypeApiInput,
  type PromptKnownDateOrDateTimeTypeFieldName,
  type PromptKnownDateRangeFieldType,
  type PromptKnownDayOfWeekTypeFieldName,
  type PromptKnownDriverTypeFieldName,
  type PromptKnownFieldName,
  type PromptKnownFieldType,
  type PromptKnownGeofenceTypeFieldName,
  type PromptKnownNumOrNumberTypeFieldName,
  type PromptKnownSingleSelectTypeFieldName,
  type PromptKnownStringTypeFieldName,
  type PromptKnownTimeTypeFieldName,
  type PromptKnownVehicleTypeFieldName,
  type RegistrationValuePromptTypeApiInput,
  type ReportPromptRawValidation,
  type ReportPromptValueTypeApiInput,
  type ReportPromptWithValue,
  type SingleSelectValuePromptTypeApiInput,
  type StringValuePromptTypeApiInput,
  type TimeValuePromptTypeApiInput,
} from 'api/reports/types'
import type {
  GeofenceGroupId,
  GeofenceId,
  VehicleGroupIdWithGPrefix,
  VehicleId,
} from 'api/types'
import type { ExtractStrict } from 'src/types/utils'
import { ctIntl } from 'src/util-components/ctIntl'
import { isNilOrEmptyObject } from 'src/util-functions/functional-utils'
import { isNilOrEmptyString } from 'src/util-functions/string-utils'

import type {
  OneTimeExportFormValidValues,
  ScheduleExportFormValidSchema,
} from './export-form-schema'
import type {
  DateRangeType,
  DateRangeTypes,
  DateTimeRangeType,
  DriverFieldValue,
  DriverTypes,
  EndDateTimeType,
  EndDateType,
  GeofenceFieldValue,
  GeofenceGroupFieldValue,
  PromptsType,
  PromptType,
  PromptValidationRange,
  RegistrationHandledValueTypes,
  StartDateTimeType,
  StartDateType,
} from './types'

export const STARTDATE_NAME = 'start_date'
export const ENDDATE_NAME = 'end_date'
export const PI_STARTDATE_NAME = 'pi_start_date'
export const PI_ENDDATE_NAME = 'pi_end_date'

export const EXPORT_FIELD_NONE_VALUE = {
  label: 'reports.exportDrawer.widget.noSelection',
  value: 'none',
} as const

export const getExportFieldNoneValue = () => ({
  ...EXPORT_FIELD_NONE_VALUE,
  label: ctIntl.formatMessage({ id: EXPORT_FIELD_NONE_VALUE.label }),
})

export const AVAILABLE_FILE_FORMATS = [
  {
    description: 'Adobe Acrobat Reader (pdf)',
    extension: 'pdf',
  },
  {
    description: 'Microsoft Excel (xls)',
    extension: 'xls',
  },
] as const

export const dateRangeOrDatePromptFilterFunc = (p: PromptType) =>
  PROMPT_KNOWN_DATE_OR_DATETIME_TYPE_FIELD_NAME.includes(
    p.identifier?.trim() as PromptKnownDateOrDateTimeTypeFieldName,
  ) &&
  PROMPT_KNOWN_DATE_RANGE_FIELD_TYPE.includes(
    p.type?.trim() as PromptKnownDateRangeFieldType,
  )

type RegistrationPromptGuard = Except<PromptType, 'type'> & {
  type:
    | 'VEHLIS'
    | 'VEHLIST'
    | 'VEHLIST_NOALL'
    | 'VEHLIST_NOGROUPS_NOALL'
    | 'VEHLISTDETAIL'
}
export const registrationPromptFilterFunc = (
  p: PromptType,
): p is RegistrationPromptGuard =>
  p.type === 'VEHLIS' ||
  p.type === 'VEHLIST' ||
  p.type === 'VEHLIST_NOALL' ||
  p.type === 'VEHLIST_NOGROUPS_NOALL' ||
  p.type === 'VEHLISTDETAIL'

type DriverPromptGuard = Except<PromptType, 'type'> & {
  type: 'DRIVERLIST' | 'DRIVERLIST_NOALL'
}
export const driverPromptFilterFunc = (p: PromptType): p is DriverPromptGuard =>
  p.type === 'DRIVERLIST' || p.type === 'DRIVERLIST_NOALL'

// We need to handle the date range or start date separately coz the date range is consist of two prompts
// and it can be only start date as well
export enum ReportFrequencyOptions {
  DAILY = 'Daily',
  WEEKLY = 'Weekly',
  MONTHLY = 'Monthly',
  CUSTOM = 'Custom interval',
}

export enum ReportFrequencyCustomOptions {
  DAYS = 'Days',
  WEEKS = 'Weeks',
  MONTHS = 'Months',
}

export const DataToReceiveCustomPeriodOptions = {
  HOURS: 'Hours',
  DAYS: 'Days',
  WEEKS: 'Weeks',
  MONTHS: 'Months',
} as const
export type DataToReceiveCustomPeriodOptions =
  (typeof DataToReceiveCustomPeriodOptions)[keyof typeof DataToReceiveCustomPeriodOptions]

export const DataToReceiveDurationOptions = {
  PREVIOUS_DAY: 'Previous day',
  PREVIOUS_7_DAYS: 'Previous 7 days',
  PREVIOUS_30_DAYS: 'Previous 30 days',
  PREVIOUS_MONTH: 'Previous month',
  ONE_MONTH_AGO_TO_DATE: 'One month ago to date',
  BEGINNING_OF_THE_MONTH_TILL_DATE: 'Beginning of the month till date',
  CUSTOM_DURATION: 'Custom duration',
} as const
export type DataToReceiveDurationOptions =
  (typeof DataToReceiveDurationOptions)[keyof typeof DataToReceiveDurationOptions]

export const mapDataDurationOptionsToTranslationKeys = (
  option: DataToReceiveDurationOptions,
) =>
  match(option)
    .with(
      DataToReceiveDurationOptions.PREVIOUS_DAY,
      () => 'reports.dataDurationOptions.previousDay',
    )
    .with(
      DataToReceiveDurationOptions.PREVIOUS_7_DAYS,
      () => 'reports.dataDurationOptions.previous7Days',
    )
    .with(
      DataToReceiveDurationOptions.PREVIOUS_30_DAYS,
      () => 'reports.dataDurationOptions.previous30Days',
    )
    .with(
      DataToReceiveDurationOptions.PREVIOUS_MONTH,
      () => 'reports.dataDurationOptions.previousMonth',
    )
    .with(
      DataToReceiveDurationOptions.BEGINNING_OF_THE_MONTH_TILL_DATE,
      () => 'reports.dataDurationOptions.toDate',
    )
    .with(
      DataToReceiveDurationOptions.CUSTOM_DURATION,
      () => 'reports.dataDurationOptions.customDuration',
    )
    .with(
      DataToReceiveDurationOptions.ONE_MONTH_AGO_TO_DATE,
      () => 'reports.dataDurationOptions.oneMonthAgoToDate',
    )
    .exhaustive()

export const LIST_UPDOWN_OPTIONS = [
  {
    label: 'UP',
    value: 'UP',
  },
  {
    label: 'DOWN',
    value: 'DOWN',
  },
  {
    label: 'NONE',
    value: 'NONE',
  },
]

export const DURATION_OPTIONS = [
  { label: '00:01', value: '00:01' },
  { label: '00:02', value: '00:02' },
  { label: '00:03', value: '00:03' },
  { label: '00:04', value: '00:04' },
  { label: '00:05', value: '00:05' },
  { label: '00:06', value: '00:06' },
  { label: '00:07', value: '00:07' },
  { label: '00:08', value: '00:08' },
  { label: '00:09', value: '00:09' },
  { label: '00:10', value: '00:10' },
  { label: '00:15', value: '00:15' },
  { label: '00:20', value: '00:20' },
  { label: '00:30', value: '00:30' },
  { label: '00:60', value: '00:60' },
  { label: '01:30', value: '01:30' },
  { label: '02:00', value: '02:00' },
]

export const REPORT_FIELD_DATE_TIME_FORMAT = 'yyyy-LL-dd HH:mm:ss'
export const toReportFieldBEDateTimeFormat = (date: DateTime) =>
  date
    .setLocale('en-ZA') // See https://gitlab.cartrack.com/cartrack-base/cartrack-external/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-web/-/issues/2168
    .toFormat(REPORT_FIELD_DATE_TIME_FORMAT)

export const REPORT_FIELD_DATE_TIME_WITH_TZ_FORMAT = 'yyyy-LL-dd HH:mm:ssZZZ'

export const toReportFieldBEDateTimeWithTZFormat = (date: DateTime) =>
  date
    .setLocale('en-ZA') // See https://gitlab.cartrack.com/cartrack-base/cartrack-external/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-web/-/issues/2168
    .toFormat(REPORT_FIELD_DATE_TIME_WITH_TZ_FORMAT)

function pickPromptFields<PromptTypeType extends PromptKnownFieldType>(
  prompt: PromptType,
) {
  return {
    type: prompt.type as PromptTypeType,
    identifier: prompt.identifier,
  }
}

export const handlePromptDateRangeOrDate = ({
  prompts,
}: {
  prompts:
    | Array<PromptType & { value?: ReportPromptWithValue['value'] }>
    | null
    | undefined
}) => {
  if (!prompts) return null

  const dateRangePrompts = prompts
    .filter((p) => dateRangeOrDatePromptFilterFunc(p))
    // convert all the date and datetime
    .map((p) => {
      const type = p.type?.toUpperCase()
      return {
        ...p,
        type: match(type)
          .with(P.union('DATE-S', 'DATE-E', 'DATE'), () => 'DATE')
          .with(
            P.union('DATETIME-S', 'DATETIME-E', 'DATETIME', 'DATETIME-5MIN'),
            () => 'DATETIME',
          )
          .otherwise((t) => t) as PromptKnownDateRangeFieldType,
      }
    })
    // sort by the identifier
    .sort((a, b) => a.identifier.localeCompare(b.identifier))

  if (dateRangePrompts.length > 0) {
    // extract the validations, it should only be used in the date range
    const validations = dateRangePrompts.reduce<{
      start?: Array<ReportPromptRawValidation>
      end?: Array<ReportPromptRawValidation>
    }>((acc, prompt) => {
      if (prompt.validation) {
        const validation = prompt.validation
        return {
          ...acc,
          ...(prompt.identifier.includes('start')
            ? { start: validation }
            : { end: validation }),
        }
      } else {
        return acc
      }
    }, {})

    return {
      validations,
      // NOTE: be careful of the order of the array values, they are sorted by the their identifiers
      ...match(dateRangePrompts)
        .returnType<{
          widgetType?: DateRangeTypes
          value?: [string, string] | string
        }>()
        .with(
          [
            { identifier: ENDDATE_NAME, type: 'DATE' },
            { identifier: STARTDATE_NAME, type: 'DATE' },
          ],
          [
            { identifier: PI_ENDDATE_NAME, type: 'DATE' },
            { identifier: PI_STARTDATE_NAME, type: 'DATE' },
          ],
          [
            { identifier: 'end_ts', type: 'DATE' },
            { identifier: 'start_ts', type: 'DATE' },
          ],
          [
            { identifier: 'end_time', type: 'DATE' },
            { identifier: 'start_time', type: 'DATE' },
          ],
          [
            { identifier: 'end_timestamp', type: 'DATE' },
            { identifier: 'start_timestamp', type: 'DATE' },
          ],
          ([end, start]) => {
            if ('value' in end && 'value' in start) {
              return {
                widgetType: 'dateRange',
                value: [start.value, end.value] as [string, string],
              }
            }
            return { widgetType: 'dateRange' }
          },
        )
        .with(
          [
            { identifier: ENDDATE_NAME, type: 'DATETIME' },
            { identifier: STARTDATE_NAME, type: 'DATETIME' },
          ],
          [
            { identifier: PI_ENDDATE_NAME, type: 'DATETIME' },
            { identifier: PI_STARTDATE_NAME, type: 'DATETIME' },
          ],
          [
            { identifier: 'end_ts', type: 'DATETIME' },
            { identifier: 'start_ts', type: 'DATETIME' },
          ],
          [
            { identifier: 'end_timestamp', type: 'DATETIME' },
            { identifier: 'start_timestamp', type: 'DATETIME' },
          ],

          ([end, start]) => {
            if ('value' in end && 'value' in start) {
              return {
                widgetType: 'dateTimeRange',
                value: [start.value, end.value] as [string, string],
              }
            }
            return { widgetType: 'dateTimeRange' }
          },
        )
        .with(
          [{ identifier: P.union(STARTDATE_NAME, 'date', 'day'), type: 'DATE' }],
          ([start]) => {
            if ('value' in start) {
              return {
                widgetType: 'startDate',
                value: start.value as string,
              }
            }
            return { widgetType: 'startDate' }
          },
        )
        .with(
          [{ identifier: P.union(STARTDATE_NAME, 'time'), type: 'DATETIME' }],
          ([start]) => {
            if ('value' in start) {
              return {
                widgetType: 'startDateTime',
                value: start.value as string,
              }
            }
            return { widgetType: 'startDateTime' }
          },
        )
        .with([{ identifier: ENDDATE_NAME, type: 'DATE' }], ([end]) => {
          if ('value' in end) {
            return {
              widgetType: 'endDate',
              value: end.value as string,
            }
          }
          return { widgetType: 'endDate' }
        })
        .with([{ identifier: 'end_timestamp', type: 'DATETIME' }], ([end]) => {
          if ('value' in end) {
            return {
              widgetType: 'endDateTime',
              value: end.value as string,
            }
          }
          return { widgetType: 'endDateTime' }
        })
        .otherwise(() => ({})),
    }
  }
  return null
}

export const hydratePromptDateRangeOrStartDate = ({
  prompts,
  fieldValue,
}: {
  prompts: PromptsType
  fieldValue: {
    dateRange: DateRangeType
    dateTimeRange: DateTimeRangeType
    startDate: StartDateType
    startDateTime: StartDateTimeType
    endDate: EndDateType
    endDateTime: EndDateTimeType
  }
}) => {
  if (!prompts) return null

  const dateRangePrompts = prompts
    .filter((p) => dateRangeOrDatePromptFilterFunc(p))
    // sort by the identifier
    .sort((a, b) => a.identifier.localeCompare(b.identifier))

  const dateRangeOrStartDateFieldName = handlePromptDateRangeOrDate({ prompts })

  if (dateRangeOrStartDateFieldName) {
    return match(dateRangeOrStartDateFieldName.widgetType)
      .with('dateRange', 'dateTimeRange', (fieldName) => {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const startDate = fieldValue[fieldName]![0].toLocal()
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const endDate = fieldValue[fieldName]![1].toLocal()

        return [
          {
            ...pickPromptFields<PromptKnownDateRangeFieldType>(dateRangePrompts[1]),
            // if no time value, the time part should be the 00:00:00
            value:
              fieldName === 'dateRange'
                ? toReportFieldBEDateTimeFormat(startDate.startOf('day'))
                : toReportFieldBEDateTimeWithTZFormat(startDate),
          },
          {
            ...pickPromptFields<PromptKnownDateRangeFieldType>(dateRangePrompts[0]),
            // if no time value, the time part should be the 23:59:59
            value:
              fieldName === 'dateRange'
                ? toReportFieldBEDateTimeFormat(endDate.endOf('day'))
                : toReportFieldBEDateTimeWithTZFormat(endDate),
          },
        ]
      })
      .with('startDate', () => [
        {
          ...pickPromptFields<PromptKnownDateRangeFieldType>(dateRangePrompts[0]),
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          value: toReportFieldBEDateTimeFormat(fieldValue.startDate!.startOf('day')),
        },
      ])
      .with('startDateTime', () => [
        {
          ...pickPromptFields<PromptKnownDateRangeFieldType>(prompts[0]),
          value: toReportFieldBEDateTimeWithTZFormat(
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            fieldValue.startDateTime!.toLocal(),
          ),
        },
      ])
      .with('endDate', () => [
        {
          ...pickPromptFields<PromptKnownDateRangeFieldType>(dateRangePrompts[0]),
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          value: toReportFieldBEDateTimeWithTZFormat(fieldValue.endDate!.toLocal()),
        },
      ])
      .with('endDateTime', () => [
        {
          ...pickPromptFields<PromptKnownDateRangeFieldType>(dateRangePrompts[0]),
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          value: toReportFieldBEDateTimeWithTZFormat(fieldValue.endDateTime!.toLocal()),
        },
      ])
      .with(undefined, () => [])
      .exhaustive()
  }

  return null
}

export const handlePromptRegistration = (prompts: PromptsType) => {
  if (!prompts) return null

  const registrationPrompt = prompts.find((p) => registrationPromptFilterFunc(p))

  if (registrationPrompt) {
    return match(registrationPrompt.type)
      .returnType<{ name: string; widgetType: RegistrationHandledValueTypes }>()
      .with('VEHLIS', 'VEHLIST', () => ({
        name: 'Vehicle',
        widgetType: 'vehicleSingle',
      }))
      .with('VEHLIST_NOALL', () => ({ name: 'Vehicle', widgetType: 'vehicleNoAll' }))
      .with('VEHLIST_NOGROUPS_NOALL', () => ({
        name: 'Vehicle',
        widgetType: 'vehicleNoGroupNoAll',
      }))
      .with('VEHLISTDETAIL', () => ({
        name: 'Vehicles',
        widgetType: 'vehicleMultiple',
      }))
      .exhaustive()
  }
  return null
}

export const hydratePromptRegistration = ({
  prompts,
  registrationValue,
}: {
  prompts: PromptsType
  registrationValue:
    | OneTimeExportFormValidValues['prompts']['registration']
    | ScheduleExportFormValidSchema['prompts']['registration']
}): RegistrationValuePromptTypeApiInput | null => {
  if (!prompts) return null

  const registrationPrompt = prompts.find((p) => registrationPromptFilterFunc(p))

  const registrationFieldName = handlePromptRegistration(prompts)

  // fill the registration value
  if (registrationFieldName && registrationPrompt && registrationValue !== null) {
    if (registrationPrompt.type === 'VEHLISTDETAIL') {
      return {
        type: 'VEHLISTDETAIL',
        identifier: registrationPrompt.identifier as PromptKnownVehicleTypeFieldName,
        value: match(registrationValue)
          .returnType<
            ExtractStrict<
              RegistrationValuePromptTypeApiInput,
              { type: 'VEHLISTDETAIL' }
            >['value']
          >()
          .with({ type: 'all' }, ({ value }) => [{ vehicle_id: value }])
          .with(P.array(P.any), (arrValue) =>
            arrValue.map((v) =>
              v.type === 'vehicle' ? { vehicle_id: v.value } : { group_id: v.value },
            ),
          )
          .otherwise(() => []),
      }
    }

    return {
      type: registrationPrompt.type,
      identifier: registrationPrompt.identifier as PromptKnownVehicleTypeFieldName,
      value: match(registrationValue)
        .returnType<VehicleId | VehicleGroupIdWithGPrefix | 'all'>()
        .with(
          { type: 'all' },
          { type: 'vehicle' },
          { type: 'vehicleGroup' },
          ({ value }) => value,
        )
        .otherwise(() => 'all'),
    }
  }
  return null
}

export const handlePromptDriver = (prompts: PromptsType) => {
  if (!prompts) return null

  const driverPrompt = prompts.find((p) => driverPromptFilterFunc(p))

  if (driverPrompt) {
    // explicit set the input and output type
    return {
      name: driverPrompt.name,
      widgetType: match(driverPrompt.type)
        .returnType<DriverTypes>()
        .with('DRIVERLIST', () => 'driverSingle')
        .with('DRIVERLIST_NOALL', () => 'driverNoAll')
        .exhaustive(),
    }
  }
  return null
}

export const getNextDayOfWeekOrMonth = (
  day: `${number}`,
  unit: 'week' | 'month',
  fromDate?: DateTime,
): DateTime | null => {
  if (Number.isNaN(Number(day)) || !(unit === 'week' || unit === 'month')) {
    return null
  }

  const anchorDate =
    isNil(fromDate) || fromDate.startOf('day') === DateTime.local().startOf('day')
      ? DateTime.local()
      : fromDate
  const anchorDateFormatted = anchorDate.toFormat(unit === 'week' ? 'c' : 'd')
  const dayDifference = Number(day) - Number(anchorDateFormatted)

  if (dayDifference >= 0) {
    // day I need is on this week/month
    return anchorDate.plus({ days: dayDifference })
  } else {
    // day I need is on next week/month
    return unit === 'week'
      ? anchorDate.plus({ days: 7 + dayDifference })
      : anchorDate.plus({ month: 1 }).set({ day: Number(day) })
  }
}

export const formatIntervalSummaryDate = (
  date: DateTime,
  showTime: boolean = false,
) => {
  const isDateToday = date.hasSame(DateTime.local(), 'day')

  return date.toFormat(
    `cccc${
      isDateToday ? ` '(${ctIntl.formatMessage({ id: 'Today' })})'` : ''
    } - dd MMM y${showTime ? ', t' : ''}`,
  )
}

export const hydratePromptDriver = ({
  prompts,
  driverValue,
}: {
  prompts: PromptsType
  driverValue: DriverFieldValue
}): DriverValuePromptTypeApiInput | null => {
  if (!prompts) return null

  const driverPrompt = prompts.find((p) => driverPromptFilterFunc(p))

  if (driverPrompt && driverValue !== null) {
    return {
      type: driverPrompt.type,
      identifier: driverPrompt.identifier as PromptKnownDriverTypeFieldName,
      value: match(driverValue)
        .with({ type: 'driver' }, ({ driverId }) => driverId)
        .with({ type: 'driverGroup' }, ({ groupIdWithGPrefix }) => groupIdWithGPrefix)
        .with({ type: 'all' }, ({ value }) => value)
        .exhaustive(),
    }
  }
  return null
}

type PromptIdentifierExtendsBase =
  | PromptKnownStringTypeFieldName
  | PromptKnownNumOrNumberTypeFieldName
  | PromptKnownTimeTypeFieldName
  | PromptKnownDayOfWeekTypeFieldName
  | PromptKnownSingleSelectTypeFieldName
  | PromptKnownGeofenceTypeFieldName
export function hydratePromptFromIdentifier<
  PromptIdentifier extends PromptIdentifierExtendsBase,
>({
  prompts,
  fieldValue,
  identifier,
  promptType,
}: {
  prompts: PromptsType
  fieldValue: string | DateTime | Array<string>
  identifier: PromptIdentifier
  promptType?: PromptKnownFieldType
}): {
  type: PromptKnownFieldType
  identifier: PromptIdentifier
  value: string | Array<string>
} | null {
  if (!prompts) return null

  const prompt = prompts.find(
    (
      prompt,
    ): prompt is Except<NonNullable<PromptsType>[number], 'identifier'> & {
      identifier: PromptIdentifier
    } => {
      const isDayOfWeek = promptType === 'DOW'
      return isDayOfWeek ? prompt.type === promptType : prompt.identifier === identifier
    },
  )

  if (prompt && !isNilOrEmptyString(fieldValue)) {
    return {
      type: prompt.type,
      identifier: prompt.identifier,
      value: DateTime.isDateTime(fieldValue)
        ? fieldValue.toFormat('HH:mm')
        : match(prompt.identifier as PromptIdentifierExtendsBase)
            .with(P.union(...PROMPT_KNOWN_DAY_OF_WEEK_TYPE_FIELD_NAME), () =>
              fieldValue.toString(),
            )
            .otherwise(() => fieldValue),
    }
  }
  return null
}

export function hydrateGeofenceTypePrompt<
  PromptType extends ExtractStrict<PromptKnownFieldType, 'GEOFENCE' | 'GEOFENCEGROUP'>,
>({
  prompts,
  fieldValue,
  identifier,
}: {
  prompts: PromptsType
  fieldValue: GeofenceFieldValue | GeofenceGroupFieldValue | null
  identifier: PromptKnownFieldName
}): {
  type: PromptType
  identifier: PromptKnownFieldName
  value: GeofenceId | GeofenceGroupId | 'none'
} | null {
  if (!prompts) return null

  const prompt = prompts.find(
    (
      prompt,
    ): prompt is Except<NonNullable<PromptsType>[number], 'type'> & {
      type: PromptType
    } => prompt.identifier === identifier,
  )

  if (prompt && !isNilOrEmptyObject(fieldValue)) {
    return {
      type: prompt.type,
      identifier: prompt.identifier,
      value: fieldValue.value,
    }
  }
  return null
}

export const hydratePromptValues = ({
  prompts,
  promptValues,
}: {
  prompts: PromptsType
  promptValues:
    | OneTimeExportFormValidValues['prompts']
    | ScheduleExportFormValidSchema['prompts']
}) => {
  if (!prompts) return null

  return Object.keys(promptValues).reduce((acc, key) => {
    match(key)
      .with('registration', (fieldKey) => {
        const regValue = hydratePromptRegistration({
          prompts,
          registrationValue: promptValues[fieldKey],
        })

        if (regValue) acc.push(regValue)
      })
      .with('driver', (fieldKey) => {
        const driverValue = hydratePromptDriver({
          prompts,
          driverValue: promptValues[fieldKey],
        })
        if (driverValue) acc.push(driverValue)
      })
      .with(P.union(...PROMPT_KNOWN_GEOFENCE_GROUP_TYPE_FIELD_NAME), (fieldKey) => {
        const value = hydrateGeofenceTypePrompt({
          prompts,
          identifier: fieldKey,
          fieldValue: promptValues[fieldKey] ?? null,
        })
        if (value) {
          acc.push(value as GeofenceGroupValuePromptTypeApiInput)
        }
      })
      .with(P.union(...PROMPT_KNOWN_GEOFENCE_TYPE_FIELD_NAME), (fieldKey) => {
        const value = hydrateGeofenceTypePrompt({
          prompts,
          identifier: fieldKey,
          fieldValue: promptValues[fieldKey] ?? null,
        })
        if (value) {
          acc.push(value as GeofenceValuePromptTypeApiInput)
        }
      })
      .with(
        P.union(...timeTypeKnownPromptFieldNames),
        P.union(...numOrNumberTypeKnownPromptFieldNames),
        P.union(...stringTypeKnownPromptFieldNames),
        P.union(...singleSelectTypeKnownPromptFieldNames),
        (fieldKey) => {
          const value = hydratePromptFromIdentifier({
            prompts,
            identifier: fieldKey,
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            fieldValue: promptValues[fieldKey]!,
          })

          if (value)
            acc.push(
              value as
                | SingleSelectValuePromptTypeApiInput
                | StringValuePromptTypeApiInput
                | NumberValuePromptTypeApiInput
                | TimeValuePromptTypeApiInput,
            )
        },
      )
      .with('dayofweek', (fieldKey) => {
        const value = hydratePromptFromIdentifier({
          prompts,
          identifier: fieldKey,
          promptType: 'DOW',
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          fieldValue: promptValues[fieldKey]!,
        })

        if (value) {
          acc.push(value as DayOfWeekValuePromptTypeApiInput)
        }
      })
      .otherwise(() => null)

    return acc
  }, [] as Array<ReportPromptValueTypeApiInput>)
}

export const getValidatedDateTimeRange = ({
  timeZone = 'default', // the default zone set by Settings.defaultZone (which is the user's configured timezone)
  validations,
  limitFutureDay,
}: {
  validations: PromptValidationRange
  timeZone?: string
  limitFutureDay: boolean
}) => {
  const validRange: { start: DateTime | undefined; end: DateTime | undefined } = {
    start: undefined,
    end: undefined,
  }
  const startValidations = validations.start
  const endValidations = validations.end

  // calculate the min date
  if (startValidations) {
    startValidations.map((validation) => {
      if (validation.type === 'days') {
        validRange.start = DateTime.local({ zone: timeZone }).minus({
          days: Number(validation.min),
        })
      }
    })
  }

  if (endValidations) {
    endValidations.map((validation) => {
      // fetch the range
      if (
        validation.type === 'ref' &&
        validation.range &&
        validation.range !== '0' &&
        validation.ref?.includes('start')
      ) {
        validRange.end = DateTime.local({ zone: timeZone }).plus({
          days: Number(validation.range),
        })
      } else if (validation.type === 'date' && !isNil(validation.max)) {
        // calculate the max date
        const endDate = DateTime.fromFormat(validation.max, 'dd-LL-yyyy', {
          zone: timeZone,
        })
        validRange.end = endDate.isValid ? endDate : undefined
      }
    })
  }

  if (!validRange.end) {
    if (limitFutureDay) {
      validRange.end = DateTime.local({ zone: timeZone })
    } else {
      validRange.end = DateTime.local({ zone: timeZone }).plus({ year: 2 })
    }
  }

  if (!validRange.start) {
    validRange.start = DateTime.local({ zone: timeZone }).minus({ year: 2 })
  }

  // Make sure valid start time is start of the day
  validRange.start = DateTime.isDateTime(validRange.start)
    ? validRange.start.startOf('day')
    : undefined
  // Make sure valid end time is end of the day
  validRange.end = DateTime.isDateTime(validRange.end)
    ? validRange.end.endOf('day')
    : undefined

  return validRange
}

export const getIntervalSummaryDates = ({
  sendDate,
  reportFrequency,
  intervalDaysOfWeek,
  intervalDayOfMonth,
  customInterval,
}: {
  sendDate: DateTime | null
  reportFrequency: ReportFrequencyOptions
  intervalDaysOfWeek: Array<`${number}`>
  intervalDayOfMonth: 'first' | 'last' | `custom:${number}`
  customInterval: [`${number}`, ReportFrequencyCustomOptions]
}): {
  dayOfFirstRun: DateTime | null
  dayOfSecondRun: DateTime | null
} =>
  match(reportFrequency)
    .returnType<{
      dayOfFirstRun: DateTime | null
      dayOfSecondRun: DateTime | null
    }>()
    .with(ReportFrequencyOptions.DAILY, () => {
      const dayOfFirstRun = sendDate
      const dayOfSecondRun = dayOfFirstRun?.plus({ day: 1 }) ?? null
      return { dayOfFirstRun, dayOfSecondRun }
    })
    .with(ReportFrequencyOptions.WEEKLY, () => {
      if (isEmpty(intervalDaysOfWeek) || !sendDate) {
        return { dayOfFirstRun: null, dayOfSecondRun: null }
      }
      const sendDateAsDOW = sendDate.toFormat('c')
      const dayOfFirstRunDOW =
        intervalDaysOfWeek.find((dow) => dow >= sendDateAsDOW) || intervalDaysOfWeek[0]
      const dayOfSecondRunDOW =
        intervalDaysOfWeek.find((dow) => dow > dayOfFirstRunDOW) ||
        intervalDaysOfWeek[0]
      const dayOfFirstRun = getNextDayOfWeekOrMonth(
        dayOfFirstRunDOW,
        'week',
        sendDate,
      ) as DateTime
      const dayOfSecondRun = getNextDayOfWeekOrMonth(
        dayOfSecondRunDOW,
        'week',
        dayOfFirstRun.plus({ day: 1 }),
      )

      return { dayOfFirstRun, dayOfSecondRun }
    })
    .with(ReportFrequencyOptions.MONTHLY, () => {
      if (isNil(intervalDayOfMonth) || !sendDate) {
        return { dayOfFirstRun: null, dayOfSecondRun: null }
      }

      const sendDateAsDOM = sendDate.toFormat('d')

      const dayOfFirstRun = ((): DateTime | null => {
        if (intervalDayOfMonth === 'first') {
          return sendDateAsDOM === '1'
            ? sendDate
            : getNextDayOfWeekOrMonth('1', 'month')
        }
        if (intervalDayOfMonth === 'last') {
          return sendDate.hasSame(sendDate.endOf('month'), 'day')
            ? sendDate
            : DateTime.local().endOf('month')
        }

        // Custom
        const selectedDOM = String_split(intervalDayOfMonth, ':')[1]
        if (!isNil(selectedDOM)) {
          return sendDateAsDOM === selectedDOM
            ? sendDate
            : getNextDayOfWeekOrMonth(selectedDOM, 'month')
        }
        return null
      })()

      return {
        dayOfFirstRun,
        dayOfSecondRun: dayOfFirstRun?.plus({ month: 1 }) ?? null,
      }
    })
    .with(ReportFrequencyOptions.CUSTOM, () => {
      if (isEmpty(customInterval)) {
        return { dayOfFirstRun: null, dayOfSecondRun: null }
      }

      const dayOfFirstRun = sendDate
      const dayOfSecondRun = match(customInterval[1])
        .with(ReportFrequencyCustomOptions.DAYS, () =>
          dayOfFirstRun?.plus({ days: Number(customInterval[0]) }),
        )
        .with(ReportFrequencyCustomOptions.WEEKS, () =>
          dayOfFirstRun?.plus({ weeks: Number(customInterval[0]) }),
        )
        .with(ReportFrequencyCustomOptions.MONTHS, () =>
          dayOfFirstRun?.plus({ months: Number(customInterval[0]) }),
        )
        .exhaustive()

      return { dayOfFirstRun, dayOfSecondRun: dayOfSecondRun ?? null }
    })
    .exhaustive()

export const getErrorMessageOrChildErrorMessage = (error: FieldError | undefined) => {
  if (R.isArray(error) && error.length > 0) {
    return (error[0] as FieldError).message
  }
  return error?.message ?? ''
}
