import { useCallback, useEffect, useMemo } from 'react'
import { isEmpty } from 'lodash'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FormControlLabel,
  <PERSON>rid,
  Stack,
  Switch,
  type DateRange,
  type PickersShortcutsItem,
} from '@karoo-ui/core'
import { TextFieldControlled, useControlledForm } from '@karoo-ui/core-rhf'
import { DateTime } from 'luxon'
import { useWatch } from 'react-hook-form'
import { match, P } from 'ts-pattern'

import type {
  PromptKnownGeofenceGroupTypeFieldName,
  PromptKnownGeofenceTypeFieldName,
  PromptKnownNumOrNumberTypeFieldName,
  PromptKnownSingleSelectTypeFieldName,
  PromptKnownStringTypeFieldName,
  PromptKnownTimeTypeFieldName,
} from 'api/reports/types'
import {
  getComputedPrivacyHideLocationsFromDay,
  getPrimaryEmailSetting,
  getReportPermissions,
} from 'duxs/user-sensitive-selectors'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import { VEHICLE_ALL_VALUE } from 'src/modules/components/unconnected/MultipleSelect/shared'
import { useTypedSelector } from 'src/redux-hooks'
import { GA4 } from 'src/shared/google-analytics4'
import { ctIntl } from 'src/util-components/ctIntl'
import FormSelect from 'src/util-components/react-hook-form/FormSelect'

import type { Report, ReportButtonContainerProps } from '../types'
import {
  dayOfWeekTypePromptsInitialValues,
  generateOnceExportFormSchema,
  getGeofenceTypePromptsInitialValues,
  insertPromptWidgetType,
  numberTypePromptsInitialValues,
  singleSelectTypePromptsInitialValues,
  stringTypePromptsInitialValues,
  timeTypePromptsInitialValues,
  type OneTimeExportFormPossibleValues,
  type OneTimeExportFormValidValues,
} from './export-form-schema'
import type { ExportReportFormValues } from './types'
import {
  AVAILABLE_FILE_FORMATS,
  DURATION_OPTIONS,
  handlePromptDateRangeOrDate,
  handlePromptDriver,
  handlePromptRegistration,
  hydratePromptDateRangeOrStartDate,
  hydratePromptValues,
  LIST_UPDOWN_OPTIONS,
} from './util'
import DateOrDateTimeField from './widgets/DateRange/DateOrDateTimeField'
import DateOrDateTimeRangeField from './widgets/DateRange/DateOrDateTimeRangeField'
import DriverAndGroupSingleList from './widgets/DriverAndGroupSingleList'
import EmailSection from './widgets/EmailSection'
import FileFormatField from './widgets/FileFormatField'
import GeofenceField from './widgets/Geofence/GeofenceField'
import GeofenceGroupField from './widgets/Geofence/GeofenceGroupField'
import DaysOfWeek from './widgets/Interval/DaysOfWeek'
import PasswordField from './widgets/PasswordField'
import { useReportProfileOptions } from './widgets/queries'
import RegistrationField from './widgets/Registration/RegistrationField'
import SendDateField from './widgets/SendDateField'
import TimeField from './widgets/TimeField'

export type FilledOneTimeReport = Pick<Report, 'name' | 'prompts' | 'report_id'> & {
  isMifleet: boolean
  fileFormats?: Report['fileFormats']
  delivery_types?: Report['delivery_types']
  filledValues?: Partial<OneTimeExportFormPossibleValues>
}

type Props = {
  filledReport: FilledOneTimeReport
  onClose: () => void
  disabled?: boolean
  onFormSubmit: (form: ExportReportFormValues) => void
  hiddenBottomButtons?: boolean
  buttonContainer?: React.ComponentType<ReportButtonContainerProps> | null
  buttonContainerProps?: {
    handleTabChange: (values: OneTimeExportFormPossibleValues) => void
  }
}

export default function ExportOneTimeForm({
  filledReport,
  onClose,
  onFormSubmit,
  disabled = false,
  buttonContainer: ButtonContainer,
  buttonContainerProps,
  hiddenBottomButtons = false,
}: Props) {
  const prompts = filledReport.prompts
  const availableFormats = useMemo(
    () =>
      !filledReport.fileFormats || isEmpty(filledReport.fileFormats)
        ? AVAILABLE_FILE_FORMATS
        : filledReport.fileFormats,
    [filledReport.fileFormats],
  )
  const primaryEmailSetting = useTypedSelector(getPrimaryEmailSetting)
  const privacyHideLocationsFromDay = useTypedSelector(
    getComputedPrivacyHideLocationsFromDay,
  )
  const permissions = useTypedSelector(getReportPermissions)
  const shortcuts = useDateRangeShortcutItems({
    todayTimeStrategy: 'now',
  })

  // if the report delivery type have `Web Link` can be download
  const onlySendThroughEmails = useMemo(
    () =>
      Boolean(
        (filledReport.delivery_types &&
          !filledReport.delivery_types.includes('Web Link')) ||
          filledReport.filledValues?.emails ||
          !permissions.download,
      ),
    [
      filledReport.delivery_types,
      filledReport.filledValues?.emails,
      permissions.download,
    ],
  )

  const dateRangeShortcutsItems = useMemo(
    () =>
      [
        shortcuts.last7Days,
        shortcuts.last15Days,
        shortcuts.thisMonth,
        shortcuts.lastMonth,
        shortcuts.reset,
      ] as const satisfies ReadonlyArray<PickersShortcutsItem<DateRange<DateTime>>>,
    [shortcuts],
  )

  const dateRangeFieldPrompt = useMemo(
    () => (prompts ? (handlePromptDateRangeOrDate({ prompts }) ?? null) : null),
    [prompts],
  )
  const registrationFieldPrompt = prompts ? handlePromptRegistration(prompts) : null
  const driverFieldPrompt = prompts ? handlePromptDriver(prompts) : null
  const primaryEmailValue = match(primaryEmailSetting)
    .with(P.nullish, () => [])
    .with(P.string, (value) => [value])
    .otherwise((values) => values)

  const initialValues = useMemo(
    (): OneTimeExportFormPossibleValues => ({
      sendDate: DateTime.local(),
      sendReportToEmail: onlySendThroughEmails,
      emails: onlySendThroughEmails ? primaryEmailValue : [],
      fileFormat: availableFormats.some((f) => f.extension === 'pdf')
        ? 'pdf'
        : availableFormats[0].extension,
      password: '',

      isPasswordRequired: Boolean(filledReport.filledValues?.password),
      // data range
      dateRangeOrStartOrEnd: {
        dateRange: null,
        dateTimeRange: null,
        startDate:
          dateRangeFieldPrompt?.widgetType === 'startDate' ? DateTime.local() : null,
        startDateTime:
          dateRangeFieldPrompt?.widgetType === 'startDateTime'
            ? DateTime.local()
            : null,
        endDate:
          dateRangeFieldPrompt?.widgetType === 'endDate' ? DateTime.local() : null,
        endDateTime:
          dateRangeFieldPrompt?.widgetType === 'endDateTime' ? DateTime.local() : null,
      },

      // prompts
      prompts: {
        driver: match(driverFieldPrompt)
          .with(
            { widgetType: 'driverSingle' },
            () =>
              ({
                type: 'all',
                label: ctIntl.formatMessage({ id: 'All Drivers' }),
                value: 'all',
              }) as const,
          )
          .with({ widgetType: 'driverNoAll' }, () => null)
          .otherwise(() => null),
        registration: match(registrationFieldPrompt)
          .with(
            { widgetType: P.union('vehicleNoAll', 'vehicleNoGroupNoAll') },
            () => null,
          )
          .with({ widgetType: 'vehicleSingle' }, () => ({
            ...VEHICLE_ALL_VALUE,
            label: ctIntl.formatMessage({ id: VEHICLE_ALL_VALUE.label }),
          }))
          .with({ widgetType: 'vehicleMultiple' }, () => [])
          .otherwise(() => null),

        ...getGeofenceTypePromptsInitialValues(),
        ...stringTypePromptsInitialValues,
        ...numberTypePromptsInitialValues,
        ...timeTypePromptsInitialValues,
        ...dayOfWeekTypePromptsInitialValues,
        ...singleSelectTypePromptsInitialValues,
      },

      // existing values
      ...filledReport.filledValues,
    }),
    [
      onlySendThroughEmails,
      primaryEmailValue,
      availableFormats,
      dateRangeFieldPrompt?.widgetType,
      driverFieldPrompt,
      registrationFieldPrompt,
      filledReport.filledValues,
    ],
  )

  const validSchema = useMemo(
    () => generateOnceExportFormSchema({ prompts, privacyHideLocationsFromDay }),
    [privacyHideLocationsFromDay, prompts],
  )

  const form = useControlledForm<OneTimeExportFormPossibleValues>({
    resolver: zodResolverV4(validSchema),
    mode: 'all',
    // use values instead of defaultValues since initialValues would be updated
    defaultValues: initialValues,
  })

  const { control, handleSubmit, getValues, trigger, setValue: setFormValue } = form

  const sendDate = useWatch({ name: 'sendDate', control })
  const sendReportToEmail = useWatch({ name: 'sendReportToEmail', control })
  const dateRange = useWatch({ name: 'dateRangeOrStartOrEnd.dateRange', control })
  const dateTimeRange = useWatch({
    name: 'dateRangeOrStartOrEnd.dateTimeRange',
    control,
  })
  const startDate = useWatch({ name: 'dateRangeOrStartOrEnd.startDate', control })
  const startDateTime = useWatch({
    name: 'dateRangeOrStartOrEnd.startDateTime',
    control,
  })

  const onSendToEmailOrSendDateChange = useEffectEvent(() => {
    trigger([
      'emails',
      'dateRangeOrStartOrEnd.dateRange',
      'dateRangeOrStartOrEnd.dateTimeRange',
      'dateRangeOrStartOrEnd.startDate',
      'dateRangeOrStartOrEnd.startDateTime',
    ])
  })
  useEffectExceptOnMount(() => {
    onSendToEmailOrSendDateChange()
  }, [sendReportToEmail, sendDate])

  useEffect(() => {
    if (sendReportToEmail) {
      const startOrEndDay: DateTime | null = match(dateRangeFieldPrompt)
        .with({ widgetType: 'dateRange' }, () =>
          dateRange === null ? null : dateRange[1],
        )
        .with({ widgetType: 'dateTimeRange' }, () =>
          dateTimeRange === null ? null : dateTimeRange[1],
        )
        .with({ widgetType: 'startDate' }, () =>
          startDate === null ? null : startDate,
        )
        .with({ widgetType: 'startDateTime' }, () =>
          startDateTime === null ? null : startDateTime,
        )
        .otherwise(() => null)

      if (startOrEndDay !== null) {
        const isDateTimeRange =
          dateRangeFieldPrompt?.widgetType === 'dateRange' ||
          dateRangeFieldPrompt?.widgetType === 'dateTimeRange'
        const today = DateTime.local()

        // if the end date and the send day is current day we do nothing
        if (startOrEndDay.hasSame(today, 'day') && sendDate.hasSame(today, 'day')) {
          return
        }

        const minSendDate = startOrEndDay.plus({ days: 1 }).startOf('day')

        // If the date range or start date(time) is later than the current time,
        // automatically change the Send by Date field by default to day after the end date
        if (
          today < startOrEndDay &&
          (sendDate < minSendDate ||
            (isDateTimeRange &&
              today < minSendDate &&
              !startOrEndDay.hasSame(today, 'day')))
        ) {
          setFormValue('sendDate', minSendDate, { shouldValidate: false })
        }
      }
    }
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sendReportToEmail, dateRange, dateTimeRange, startDate, startDateTime])

  const onSubmit = handleSubmit((_values) => {
    const values = _values as OneTimeExportFormValidValues
    const promptValues = hydratePromptValues({ prompts, promptValues: values.prompts })
    const dateRangeValues = hydratePromptDateRangeOrStartDate({
      prompts,
      fieldValue: values.dateRangeOrStartOrEnd,
    })

    // update the send date to current time if the one in form is earlier than now
    const currentTime = DateTime.local()
    const sendDate = currentTime > values.sendDate ? currentTime : values.sendDate

    GA4.event({
      category: 'New report',
      action: sendReportToEmail ? 'Send report by email' : 'Download report',
    })

    onFormSubmit({
      id: filledReport.report_id,
      name: filledReport.name,
      prompts: [...(dateRangeValues ?? []), ...(promptValues ?? [])],
      ...(sendReportToEmail ? { emails: values.emails, sendDate } : {}),
      fileFormat: values.fileFormat,
      ...(values.password ? { password: values.password } : {}),
    })
  })

  const bottomButtons = useCallback(
    () =>
      !ButtonContainer || !buttonContainerProps ? (
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="flex-end"
          sx={{ pr: 3 }}
          data-testid="ReportDrawer-OneTime-BottomButtons"
        >
          <Button
            data-testid="ReportDrawer-CancelButton"
            variant="outlined"
            color="secondary"
            onClick={onClose}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </Button>

          <Button
            data-testid="ReportDrawer-SaveButton"
            variant="contained"
            type="submit"
            onClick={onSubmit}
          >
            {ctIntl.formatMessage({
              id: sendReportToEmail ? 'Send Email' : 'Download Report',
            })}
          </Button>
        </Stack>
      ) : (
        <ButtonContainer
          onClose={onClose}
          onSubmit={onSubmit}
          handlePrevClick={() => {
            buttonContainerProps.handleTabChange(getValues())
          }}
          sendReportToEmail={getValues().sendReportToEmail}
        />
      ),
    [
      ButtonContainer,
      buttonContainerProps,
      getValues,
      onClose,
      onSubmit,
      sendReportToEmail,
    ],
  )

  const reportProfileOptions = useReportProfileOptions()

  return (
    <>
      <Stack
        sx={{
          ml: -3,
          px: 3, // need to have padding-left here for the components like combobox won't be cut off
          pt: 1,
          gap: 3,
          overflowX: 'hidden',
          overflowY: 'auto',
          position: 'relative', // for the absolute positioned circular progress
        }}
        data-testid="Report-OneTimeExportForm"
      >
        {registrationFieldPrompt && (
          <RegistrationField
            formProps={{ name: 'prompts.registration', control }}
            promptName={registrationFieldPrompt.name}
            registrationFieldName={registrationFieldPrompt.widgetType}
            isMifleet={filledReport.isMifleet}
            disabled={disabled}
          />
        )}
        {driverFieldPrompt && (
          <DriverAndGroupSingleList
            formProps={{ name: 'prompts.driver', control }}
            promptName={driverFieldPrompt.name}
            includeAll={driverFieldPrompt.widgetType === 'driverSingle'}
            isMifleet={filledReport.isMifleet}
            disabled={disabled}
          />
        )}
        {match(dateRangeFieldPrompt)
          .with({ widgetType: 'dateRange' }, ({ widgetType, validations }) => (
            <DateOrDateTimeRangeField
              type="date"
              formProps={{ name: `dateRangeOrStartOrEnd.${widgetType}`, control }}
              shortcutItems={dateRangeShortcutsItems}
              validations={validations}
              disabled={disabled}
              limitFutureDay={!sendReportToEmail}
            />
          ))
          .with({ widgetType: 'dateTimeRange' }, ({ widgetType, validations }) => (
            <DateOrDateTimeRangeField
              type="dateTime"
              formProps={{ name: `dateRangeOrStartOrEnd.${widgetType}`, control }}
              shortcutItems={dateRangeShortcutsItems}
              validations={validations}
              disabled={disabled}
              limitFutureDay={!sendReportToEmail}
            />
          ))
          .with(
            {
              widgetType: P.union(
                'startDate',
                'startDateTime',
                'endDate',
                'endDateTime',
              ),
            },
            ({ widgetType, validations }) => (
              <DateOrDateTimeField
                formProps={{ name: `dateRangeOrStartOrEnd.${widgetType}`, control }}
                validations={validations}
                disabled={disabled}
                limitFutureDay={!sendReportToEmail}
                widgetType={widgetType}
              />
            ),
          )
          .otherwise(() => null)}
        <Grid
          container
          rowSpacing={3}
          columnSpacing={1.5}
        >
          {insertPromptWidgetType({ prompts }).map((newPrompt) =>
            match(newPrompt)
              .with({ widgetType: 'geofence' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <GeofenceField
                    label={name}
                    formProps={{
                      name: `prompts.${identifier as PromptKnownGeofenceTypeFieldName}`,
                      control,
                    }}
                    disabled={disabled}
                  />
                </Grid>
              ))
              .with({ widgetType: 'geofenceGroup' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <GeofenceGroupField
                    label={name}
                    formProps={{
                      name: `prompts.${
                        identifier as PromptKnownGeofenceGroupTypeFieldName
                      }`,
                      control,
                    }}
                    disabled={disabled}
                  />
                </Grid>
              ))
              .with({ widgetType: 'string' }, ({ identifier, name }) => (
                <Grid
                  size={6}
                  key={identifier}
                >
                  <TextFieldControlled
                    data-testid={`ReportForm-OneTime-StringType-${identifier}`}
                    ControllerProps={{
                      name: `prompts.${identifier as PromptKnownStringTypeFieldName}`,
                      control,
                    }}
                    label={name}
                    required
                    disabled={disabled}
                    fullWidth
                  />
                </Grid>
              ))
              .with({ widgetType: 'number' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <TextFieldControlled
                    data-testid={`ReportForm-OneTime-NumberType-${identifier}`}
                    ControllerProps={{
                      name: `prompts.${
                        identifier as PromptKnownNumOrNumberTypeFieldName
                      }`,
                      control,
                    }}
                    type="number"
                    label={name}
                    required
                    disabled={disabled}
                    fullWidth
                  />
                </Grid>
              ))
              .with({ widgetType: 'time' }, ({ identifier, name }) => (
                <Grid
                  size={6}
                  key={identifier}
                >
                  <TimeField
                    promptName={name}
                    formProps={{
                      name: `prompts.${identifier as PromptKnownTimeTypeFieldName}`,
                      control,
                    }}
                    disabled={disabled}
                  />
                </Grid>
              ))
              .with({ widgetType: 'singleSelect' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <FormSelect
                    formProps={{
                      name: `prompts.${
                        identifier as PromptKnownSingleSelectTypeFieldName
                      }`,
                      control,
                    }}
                    label={name}
                    labelId={`ReportForm-SingleSelect-${identifier}`}
                    dataTestId={`ReportForm-SingleSelect-${identifier}`}
                    list={match(identifier as PromptKnownSingleSelectTypeFieldName)
                      .with('updown', () =>
                        LIST_UPDOWN_OPTIONS.map((o) => ({
                          label: { id: o.label },
                          value: o.value,
                        })),
                      )
                      .with('duration', () => DURATION_OPTIONS)
                      .with('profile_id', () => reportProfileOptions)
                      .exhaustive()}
                    disabled={disabled}
                  />
                </Grid>
              ))
              .with({ widgetType: 'daysOfWeek' }, ({ identifier, name }) => (
                <Grid
                  size={12}
                  key={identifier}
                >
                  <DaysOfWeek
                    formProps={{
                      name: `prompts.dayofweek`,
                      control,
                    }}
                    label={name}
                    disabled={disabled}
                  />
                </Grid>
              ))
              .otherwise(() => null),
          )}
        </Grid>
        <Divider />
        <FileFormatField
          formProps={{ name: 'fileFormat', control }}
          fileFormats={availableFormats}
          disabled={disabled}
        />
        <Stack spacing={1}>
          <FormControlLabel
            disabled={onlySendThroughEmails || disabled}
            sx={{
              position: 'relative',
              right: '12px',
            }}
            control={
              <Switch
                checked={sendReportToEmail}
                onChange={(_, value) => {
                  setFormValue('sendReportToEmail', value, { shouldValidate: false })

                  if (value) {
                    setFormValue('emails', primaryEmailValue)
                  } else {
                    setFormValue('emails', [])
                  }
                }}
                data-testid="ReportForm-SendEmailSwitch"
              />
            }
            label={ctIntl.formatMessage({
              id: 'reports.exportDrawer.sendReportViaEmail.label',
            })}
          />
          {sendReportToEmail && (
            <>
              <SendDateField
                formProps={{ name: 'sendDate', control }}
                label="reports.exportDrawer.sendReportViaEmail.sendBy"
                disabled={disabled}
              />
              <EmailSection
                formProps={{ name: 'emails', control }}
                disabled={disabled}
              />
            </>
          )}
        </Stack>
        {sendReportToEmail && (
          <PasswordField
            formProps={{ name: 'password', control }}
            disabled={disabled}
          />
        )}
      </Stack>
      {hiddenBottomButtons ? null : bottomButtons()}
    </>
  )
}
