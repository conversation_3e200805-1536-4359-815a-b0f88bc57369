/* eslint-disable no-param-reassign */
/// <reference types="@testing-library/cypress" />
import type { CyHttpMessages } from 'cypress/types/net-stubbing'
import { createMemoryHistory } from 'history'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import type {
  ReportPromptValueTypeApiInput,
  UpdateScheduledReportsV2Api,
} from 'api/reports/types'
import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { endpointsMocks } from 'src/cypress-ct/mocks/endpoints'
import {
  geofenceEndpointMocks,
  geofenceGroupsMock,
  geofencesMock,
} from 'src/cypress-ct/mocks/endpoints/geofence'
import {
  driverGroupsMock,
  driversMock,
  getReportStatusData,
  GLOBAL_scheduledReportList,
  multipleEmailsString,
  timezoneToParseDates,
  vehicleGroupsMock,
  vehiclesMock,
} from 'src/cypress-ct/mocks/endpoints/report'
import {
  cyExpect,
  exhaustiveEndpointCallCheck,
  matchWithMethod,
  mountWithProviders,
  runTestsInOrderWithLoggingAndSetup,
} from 'src/cypress-ct/utils'
import { REPORTS } from 'src/modules/app/components/routes/reports'
import { capitalize } from 'src/util-functions/string-utils'

import ReportRoutes from '../index'

const basePath = REPORTS.subMenusRoutes.STATUS_AND_MANAGEMENT.path

const globalHistory = createMemoryHistory({
  initialEntries: [basePath],
})

const GridContent = () => cy.get('.MuiDataGrid-virtualScrollerRenderZone').children()

const mountReportsStatusAndManagement = () => {
  mountWithProviders(<ReportRoutes />, {
    history: globalHistory,
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user({ defaultTimezone: timezoneToParseDates }).mockState,
        vehicles: duxsMocks.vehicles({
          vehicles: vehiclesMock,
          vehicleGroups: vehicleGroupsMock,
        }),
        drivers: duxsMocks.drivers({
          drivers: driversMock,
          driverGroups: driverGroupsMock,
        }),
        reports: duxsMocks.reports,
      },
    },
  })
}

const reportStatusData = getReportStatusData(timezoneToParseDates)

const reportProfileDataMock = endpointsMocks.ct_fleet_get_report_profile_data()

const handleApiCalls = (req: CyHttpMessages.IncomingHttpRequest) =>
  match(req.body)
    .with({ method: 'ct_fleet_get_report_options_v2' }, () =>
      req.reply(endpointsMocks.ct_fleet_get_report_options_v2()),
    )
    .with({ method: 'ct_fleet_get_custom_report_resources' }, () =>
      req.reply(endpointsMocks.ct_fleet_get_custom_report_resources()),
    )
    .with(matchWithMethod(req, 'ct_fleet_get_schedule_reports_v2'), () =>
      req.reply(endpointsMocks.ct_fleet_get_schedule_reports_v2()),
    )
    .with({ method: 'ct_fleet_get_report_status_v2' }, () =>
      req.reply(
        endpointsMocks.ct_fleet_get_report_status_v2({
          timezone: timezoneToParseDates,
        }),
      ),
    )
    .with({ method: 'ct_fleet_cancel_schedule_reports' }, () =>
      req.reply(endpointsMocks.ct_fleet_cancel_schedule_reports()),
    )
    .with({ method: 'ct_fleet_get_geofence_v2' }, () =>
      req.reply(geofenceEndpointMocks.ct_fleet_get_geofence_v2()),
    )
    .with({ method: 'ct_fleet_get_prelogin_data' }, () => {
      req.reply({ delay: 50, body: { id: 10, result: {} } })
    })
    .with({ method: 'ct_fleet_get_favourite_reports' }, () => {
      req.reply(endpointsMocks.ct_fleet_get_favourite_reports())
    })
    .with({ method: 'ct_fleet_get_report_preview' }, () => {
      req.reply(endpointsMocks.ct_fleet_get_report_preview())
    })
    .with({ method: 'ct_fleet_get_report_profile_data' }, () => {
      req.reply(reportProfileDataMock)
    })
    .otherwise(exhaustiveEndpointCallCheck)

beforeEach(() => {
  cy.intercept('POST', '/jsonrpc/public/index.php', handleApiCalls)

  mountReportsStatusAndManagement()
})

describe('Email report status tab', () => {
  describe('table behaviour', () => {
    it('show the email report status', () => {
      const emails = multipleEmailsString.split(';')

      cy.findByTestId('Reports-StatusTable').should('exist')

      // the items inside one week should be 7
      GridContent().should('have.length', 7)

      // the header should have 6 columns
      cy.findAllByRole('columnheader').should('have.length', 6)

      // emails folded
      GridContent()
        .first()
        .findAllByRole('gridcell')
        .eq(4)
        .should('have.text', `${emails[0]}, +${emails.length - 1} More`)

      // same day pending should be processing
      GridContent()
        .eq(1)
        .findAllByRole('gridcell')
        .last()
        .should('have.text', 'Processing')

      // if the interval value is null, display the One Time in interval column
      GridContent()
        .eq(1)
        .findAllByRole('gridcell')
        .eq(3)
        .should('have.text', 'One Time')

      // the vehicle group id without prefix should not display
      GridContent().eq(6).findAllByRole('gridcell').eq(2).should('have.text', '')

      // the wrong vehicle id should be blank
      GridContent().eq(3).findAllByRole('gridcell').eq(2).should('have.text', '')

      // list should be able to be sorted by vehicles
      const clickVehicleSortIconButton = () =>
        cy
          .get('.MuiDataGrid-columnHeader')
          .children()
          .eq(2)
          .find('.MuiDataGrid-columnHeaderTitleContainer')
          .find('button[title="Sort"]')
          .invoke('show')
          .click({ force: true })
      clickVehicleSortIconButton()

      // then the first item should have empty vehicle
      GridContent().first().findAllByRole('gridcell').eq(2).should('have.text', 'All')

      // NOTE: click two more times to recover the table sorting
      clickVehicleSortIconButton()
      clickVehicleSortIconButton()

      // trigger the send date range
      cy.findByTestId('Reports-StatusTable-DateRangeFilter').click()

      cy.get('.MuiPickersLayout-root')
        .find('.MuiPickersLayout-shortcuts')
        .findByText('All Time')
        .click()

      cy.get('.MuiTablePagination-displayedRows').should(
        'have.text',
        `1–7 of ${reportStatusData.length}`,
      )

      GridContent().should('have.length', 7)
    })
  })

  describe('click item to show one time readonly form drawer', () => {
    it('report with dateRange format 1, registration all, driver all, multiple emails', () => {
      const selectedReport = reportStatusData[0]

      // click the first one, it should be one time report
      GridContent().first().click()

      cy.findByTestId('Reports-Export-Drawer').should('exist')

      cy.findByTestId('Reports-ExportForm-OneTime-Title').should(
        'have.text',
        selectedReport.report_name,
      )

      cy.findByTestId('Reports-ExportForm-OneTime-Status')
        .find('span')
        .should('have.text', 'Sent')

      // registration
      cy.findByTestId('ReportForm-Registration-Single')
        .find('input')
        .should('be.disabled')
        .should('have.value', 'All Vehicles')

      // should not have reminder text
      cy.findByTestId('ReportForm-Registration-ReminderText').should('not.exist')

      // driver
      cy.findByTestId('ReportForm-DriversAndGroups')
        .find('input')
        .should('have.value', 'All Drivers')
        .should('be.disabled')

      // date range
      cy.findByTestId('ReportForm-DateRange')
        .find('input')
        .first()
        .should('have.value', '2023/11/01')
        .should('have.attr', 'disabled')
      cy.findByTestId('ReportForm-DateRange')
        .find('input')
        .last()
        .should('have.value', '2023/11/01')
        .should('have.attr', 'disabled')

      // start/end time
      cy.findByTestId(
        `ReportDrawer-Form-Time-${
          selectedReport.prompts.find(
            (p) => p.type === 'TIME' && p.identifier.includes('start'),
          )?.identifier
        }`,
      )
        .find('input')
        .should('be.disabled')
        .should('have.value', '02:01')

      cy.findByTestId(
        `ReportDrawer-Form-Time-${
          selectedReport.prompts.find(
            (p) => p.type === 'TIME' && p.identifier.includes('end'),
          )?.identifier
        }`,
      )
        .find('input')
        .should('be.disabled')
        .should('have.value', '02:00')

      // geofence
      cy.findByTestId('ReportForm-Geofence')
        .find('input')
        .should('be.disabled')
        .should('have.value', geofencesMock['1a'].geofence_name)

      // geofence group
      cy.findByTestId('ReportForm-GeofenceGroup')
        .find('input')
        .should('be.disabled')
        .should('have.value', geofenceGroupsMock['323'].name)

      // string type
      cy.findByTestId('ReportForm-OneTime-StringType-km_cost')
        .find('input')
        .should('be.disabled')
        .should('have.value', '3.33')

      // number type
      cy.findByTestId('ReportForm-OneTime-NumberType-in_interval')
        .find('input')
        .should('be.disabled')
        .should('have.value', '30')

      // file format
      cy.findByTestId('ReportForm-FileFormat-pdf')
        .find('input[type="radio"]')
        .should('be.disabled')
        .should('be.checked')

      // emails
      cy.findByTestId('ReportForm-Email-List').children().should('have.length', 3)

      // bottom button not exist
      cy.findByTestId('ReportDrawer-CancelButton').should('not.exist')

      // close the drawer
      cy.findByTestId('ReportForm-Close').click()
    })

    it('report with dateTimeRange format 2, registration and driver single value, emails empty', () => {
      const selectedReport = reportStatusData[1]

      // click the second one, it should be one time report
      GridContent().eq(1).click()

      cy.findByTestId('Reports-Export-Drawer').should('exist')

      cy.findByTestId('Reports-ExportForm-OneTime-Title').should(
        'have.text',
        selectedReport.report_name,
      )

      cy.findByTestId('Reports-ExportForm-OneTime-Status')
        .find('span')
        .should('have.text', 'Processing')

      // registration
      cy.findByTestId('ReportForm-Registration-Single')
        .find('input')
        .should('be.disabled')
        .should('have.value', vehiclesMock[0].name)

      // should not have reminder text
      cy.findByTestId('ReportForm-Registration-ReminderText').should('not.exist')

      // driver
      cy.findByTestId('ReportForm-DriversAndGroups')
        .find('input')
        .should('have.value', driversMock[0].name)
        .should('be.disabled')

      // date range
      cy.findByTestId('ReportForm-DateTimeRange')
        .find('input')
        .first()
        .should('have.value', '2023/10/27 00:00')
        .should('have.attr', 'disabled')
      cy.findByTestId('ReportForm-DateTimeRange')
        .find('input')
        .last()
        .should('have.value', '2023/10/27 23:59')
        .should('have.attr', 'disabled')

      // string type
      cy.findByTestId('ReportForm-OneTime-StringType-branch_id')
        .find('input')
        .should('be.disabled')
        .should('have.value', '33333')

      cy.findByTestId('ReportForm-OneTime-StringType-user_id')
        .find('input')
        .should('be.disabled')
        .should('have.value', '33333')

      cy.findByTestId('ReportForm-OneTime-StringType-user_name')
        .find('input')
        .should('be.disabled')
        .should('have.value', '33333')

      cy.findByTestId('ReportForm-SingleSelect-profile_id')
        .find('input')
        .should('be.disabled')
        .should(
          'have.value',
          reportProfileDataMock.body.result.ct_fleet_get_user_report_profile[0]
            .report_profile_id,
        )

      // number type
      cy.findByTestId('ReportForm-OneTime-NumberType-interval')
        .find('input')
        .should('be.disabled')
        .should('have.value', '30')

      cy.findByTestId('ReportForm-OneTime-NumberType-time_open')
        .find('input')
        .should('be.disabled')
        .should('have.value', '30')

      // email is empty
      cy.findByTestId('ReportForm-SendEmailSwitch').should('not.be.checked')
      cy.findByTestId('ReportForm-EmailSection').should('not.exist')
      cy.findByTestId('ReportForm-PasswordField').should('not.exist')

      // close the drawer
      cy.findByTestId('ReportForm-Close').click()
    })

    it('report with dateTimeRange format 3, registration/driver no all with group id', () => {
      const selectedReport = reportStatusData[2]

      // click the 3rd one, it should be one time report
      GridContent().eq(2).click()

      cy.findByTestId('Reports-Export-Drawer').should('exist')

      cy.findByTestId('Reports-ExportForm-OneTime-Title').should(
        'have.text',
        selectedReport.report_name,
      )

      cy.findByTestId('Reports-ExportForm-OneTime-Status')
        .find('span')
        .should('have.text', 'Failed')

      // registration
      cy.findByTestId('ReportForm-Registration-Single')
        .find('input')
        .should('be.disabled')
        .should('have.value', vehicleGroupsMock[0].name)

      // should not have reminder text
      cy.findByTestId('ReportForm-Registration-ReminderText').should('not.exist')

      // driver
      cy.findByTestId('ReportForm-DriversAndGroups')
        .find('input')
        .should('have.value', driverGroupsMock[0].name)
        .should('be.disabled')

      // date range
      cy.findByTestId('ReportForm-DateTimeRange')
        .find('input')
        .first()
        .should('have.value', '2023/11/01 07:20')
        .should('have.attr', 'disabled')
      cy.findByTestId('ReportForm-DateTimeRange')
        .find('input')
        .last()
        .should('have.value', '2023/11/02 00:53')
        .should('have.attr', 'disabled')

      // password not checked
      cy.findByTestId('ReportForm-Password-Checkbox')
        .find('input')
        .should('not.be.checked')
        .should('be.disabled')

      // close the drawer
      cy.findByTestId('ReportForm-Close').click()
    })

    it('report with startDate format 4, registration no all, no group and vehicle not exist, file format csv', () => {
      const selectedReport = reportStatusData[3]

      // click the 4th one, it should be one time report
      GridContent().eq(3).click()

      cy.findByTestId('Reports-Export-Drawer').should('exist')

      cy.findByTestId('Reports-ExportForm-OneTime-Title').should(
        'have.text',
        selectedReport.report_name,
      )

      cy.findByTestId('Reports-ExportForm-OneTime-Status')
        .find('span')
        .should('have.text', 'Upcoming')

      // NOTE: registration vehicle not exist
      cy.findByTestId('ReportForm-Registration-Single')
        .find('input')
        .should('be.disabled')
        .should('have.value', '')

      // should not have reminder text
      cy.findByTestId('ReportForm-Registration-ReminderText').should('not.exist')

      // date range
      cy.findByTestId('ReportForm-StartDate')
        .find('input')
        .first()
        .should('have.value', '2023/11/01')
        .should('have.attr', 'disabled')

      // file format
      cy.findByTestId('ReportForm-FileFormat-csv')
        .find('input[type="radio"]')
        .should('be.disabled')
        .should('be.checked')

      // close the drawer
      cy.findByTestId('ReportForm-Close').click()
    })

    it('report with startDate mifleet format 5, registration multiple values', () => {
      const selectedReport = reportStatusData[4]

      // click the 5th one, it should be one time report
      GridContent().eq(4).click()

      cy.findByTestId('Reports-Export-Drawer').should('exist')

      cy.findByTestId('Reports-ExportForm-OneTime-Title').should(
        'have.text',
        selectedReport.report_name,
      )

      cy.findByTestId('Reports-ExportForm-OneTime-Status')
        .find('span')
        .should('have.text', 'Upcoming')

      // registration
      cy.findByTestId('ReportForm-Registration-Multiple')
        .find('input')
        .should('be.disabled')
      // vehicle group name
      cy.findByTestId('ReportForm-Registration-Multiple')
        .find('.MuiChip-root > span')
        .first()
        .should('have.text', vehicleGroupsMock[0].name)

      // vehicle name
      cy.findByTestId('ReportForm-Registration-Multiple')
        .find('.MuiChip-root > span')
        .last()
        .should('have.text', vehiclesMock[0].name)

      // should not have reminder text
      cy.findByTestId('ReportForm-Registration-ReminderText').should('not.exist')

      // date range
      cy.findByTestId('ReportForm-DateRange')
        .find('input')
        .first()
        .should('have.value', '2023/10/17')
        .should('have.attr', 'disabled')
      cy.findByTestId('ReportForm-DateRange')
        .find('input')
        .last()
        .should('have.value', '2023/10/17')
        .should('have.attr', 'disabled')

      // close the drawer
      cy.findByTestId('ReportForm-Close').click()
    })
  })

  describe('click item to show recurring readonly form drawer', () => {
    it('report with registration multiple values with value "all"', () => {
      const selectedReport = reportStatusData[5]

      // click the 6th one, it should be one time report
      GridContent().eq(5).click()
      cy.findByTestId('Reports-Export-Drawer').should('exist')

      cy.findByTestId('Reports-ExportForm-Recurring-Title').should(
        'have.text',
        selectedReport.report_name,
      )

      cy.findByTestId('Reports-ExportForm-Recurring-Status')
        .find('span')
        .should('have.text', 'Upcoming')

      // registration
      cy.findByTestId('ReportForm-Registration-Multiple')
        .find('input')
        .should('be.disabled')
      // vehicle group name
      cy.findByTestId('ReportForm-Registration-Multiple')
        .find('.MuiChip-root > span')
        .first()
        .should('have.text', 'All Vehicles')

      // should have reminder text
      cy.findByTestId('ReportForm-Registration-ReminderText').should('exist')

      // interval frequency
      cy.findByTestId('ReportForm-Recurring-ReportFrequency')
        .find('input')
        .should('be.disabled')
        .should('have.value', 'Custom interval')

      cy.findByTestId('ReportForm-Recurring-Custom-Frequency')
        .find('input')
        .should('have.value', '2')

      cy.findByTestId('ReportForm-Recurring-Custom-Interval')
        .find('input')
        .should('have.value', 'Days')

      // data duration
      cy.findByTestId('ReportForm-Recurring-DataDuration')
        .find('input')
        .should('have.value', 'Previous day')

      // interval summary
      cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('not.exist')

      // password
      cy.findByTestId('ReportForm-Password-Checkbox')
        .find('input')
        .should('be.checked')
        .should('be.disabled')
      cy.findByTestId('ReportForm-PasswordInput')
        .find('input')
        .should('be.disabled')
        .should('have.value', selectedReport.password)

      // bottom button not exist
      cy.findByTestId('ReportDrawer-CancelButton').should('not.exist')

      // close the drawer
      cy.findByTestId('ReportForm-Close').click()
    })

    it('report with registration no all type and value with group without prefix', () => {
      const selectedReport = reportStatusData[6]

      // click the 6th one, it should be one time report
      GridContent().eq(6).click()

      cy.findByTestId('Reports-Export-Drawer').should('exist')
      cy.findByTestId('Reports-ExportForm-Recurring-Title').should(
        'have.text',
        selectedReport.report_name,
      )
      cy.findByTestId('Reports-ExportForm-Recurring-Status')
        .find('span')
        .should('have.text', 'Upcoming')

      // registration, the vehicle group id has no prefix g-, so it's empty
      cy.findByTestId('ReportForm-Registration-Single')
        .find('input')
        .should('be.disabled')
        .should('have.value', '')

      // data duration
      cy.findByTestId('ReportForm-Recurring-DataDuration')
        .find('input')
        .should('have.value', 'Previous 7 days')

      // bottom button not exist
      cy.findByTestId('ReportDrawer-CancelButton').should('not.exist')

      // close the drawer
      cy.findByTestId('ReportForm-Close').click()
    })
  })
})

describe('Manage Recurring Reports tab', () => {
  const navigateToManageRecurringReports = () => {
    cy.findByTestId('Report-Management-manage-recurring-reports').click()
    cy.findByTestId('Reports-ManageRecurring-Table').should('exist')
  }

  const checkFirstScheduledReport = ({ disabled }: { disabled: boolean }) => {
    const selectedReport = GLOBAL_scheduledReportList[0]

    const emails = multipleEmailsString.split(';')
    cy.findByTestId('Report-ExportDrawer').should('exist')

    // title
    cy.findByTestId('Reports-ExportForm-Recurring-Title').should(
      'have.text',
      selectedReport.report_name,
    )

    // no status chip
    cy.findByTestId('Reports-ExportForm-Recurring-Status').should('not.exist')

    // registration
    cy.findByTestId('ReportForm-Registration-Single')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', 'All Vehicles')

    // should have reminder text
    cy.findByTestId('ReportForm-Registration-ReminderText').should('exist')

    // driver
    cy.findByTestId('ReportForm-DriversAndGroups')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', 'All Drivers')

    // start/end time
    cy.findByTestId(
      `ReportDrawer-Form-Time-${
        selectedReport.prompts.find(
          (p) => p.type === 'TIME' && p.identifier.includes('start'),
        )?.identifier
      }`,
    )
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '05:05')

    cy.findByTestId(
      `ReportDrawer-Form-Time-${
        selectedReport.prompts.find(
          (p) => p.type === 'TIME' && p.identifier.includes('end'),
        )?.identifier
      }`,
    )
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '06:06')

    // geofence
    cy.findByTestId('ReportForm-Geofence')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', geofencesMock['1a'].geofence_name)

    // geofence group
    cy.findByTestId('ReportForm-GeofenceGroup')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', geofenceGroupsMock['323'].name)

    // number type
    cy.findByTestId('ReportForm-Recurring-NumberType-in_interval')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '30')

    // string type
    cy.findByTestId('ReportForm-Recurring-StringType-km_cost')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '3.33')

    // NUM type
    cy.findByTestId('ReportForm-Recurring-NumberType-time_open')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '30')

    // interval type
    cy.findByTestId('ReportForm-Recurring-NumberType-interval')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '30')

    // BRANCH type
    cy.findByTestId('ReportForm-Recurring-StringType-branch_id')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '33333')

    // USER_ID type
    cy.findByTestId('ReportForm-Recurring-StringType-user_id')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '33333')

    // PROFILE_ID type
    cy.findByTestId('ReportForm-SingleSelect-profile_id')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should(
        'have.value',
        reportProfileDataMock.body.result.ct_fleet_get_user_report_profile[0]
          .report_profile_id,
      )

    // USER_NAME type
    cy.findByTestId('ReportForm-Recurring-StringType-user_name')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '33333')

    // TIMEFINE type
    cy.findByTestId('ReportForm-SingleSelect-duration')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', '00:05')

    // UPDOWN LIST type
    cy.findByTestId('ReportForm-SingleSelect-updown')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', 'UP')

    // file format
    cy.findByTestId('ReportForm-FileFormat-xls')
      .find('input[type="radio"]')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('be.checked')

    // emails
    cy.findByTestId('ReportForm-Email-List')
      .children()
      .should('have.length', emails.length)

    // interval frequency
    cy.findByTestId('ReportForm-Recurring-ReportFrequency')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', 'Daily')

    // data duration
    cy.findByTestId('ReportForm-Recurring-DataDuration')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', 'Previous day')

    // interval summary not exist
    cy.findByTestId('ReportForm-Recurring-IntervalSummary').should(
      disabled ? 'not.exist' : 'exist',
    )

    // password
    cy.findByTestId('ReportForm-Password-Checkbox')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('be.checked')

    cy.findByTestId('ReportForm-PasswordInput')
      .find('input')
      .should(disabled ? 'be.disabled' : 'not.be.disabled')
      .should('have.value', selectedReport.password)
  }

  const updateFirstScheduledReport = () => {
    const selectedReport = GLOBAL_scheduledReportList[0]
    const emails = selectedReport.delivery_to.split(';')

    const updatedValue = {
      startTime: '06:06',
      endTime: '07:07',
      geofence: geofencesMock['2b'].geofence_id,
      geofenceGroup: geofenceGroupsMock['324'].group_geofence_id,
      number: '20',
      km_cost: '2.22',
      string: '22',
      updown: 'DOWN',
      duration: '00:07',
      vehicle: vehicleGroupsMock[2],
      driver: driverGroupsMock[0],
      // eslint-disable-next-line sonarjs/no-hardcoded-credentials
      password: '12345',
      frequencyUnit: 'month',
      frequencyNumber: '4',
      emails: emails.slice(0, emails.length - 1).join(';'),
      fileFormat: 'pdf',
    }
    const updatedScheduedReport = {
      id: selectedReport.report_id,
      schedule: true,
      prompts: [
        {
          type: 'GEOFENCE',
          identifier: 'geofence_id',
          value: updatedValue.geofence,
        },
        {
          type: 'GEOFENCEGROUP',
          identifier: 'geofence_group',
          value: updatedValue.geofenceGroup,
        },
        {
          type: 'INTERVAL',
          identifier: 'interval',
          value: updatedValue.number,
        },
        {
          type: 'NUM',
          identifier: 'time_open',
          value: updatedValue.number,
        },
        {
          type: 'NUMBER',
          identifier: 'in_interval',
          value: updatedValue.number,
        },
        {
          type: 'DESC',
          identifier: 'km_cost',
          value: updatedValue.km_cost,
        },
        {
          type: 'USER_NAME',
          identifier: 'user_name',
          value: updatedValue.string,
        },
        {
          type: 'USER_ID',
          identifier: 'user_id',
          value: updatedValue.string,
        },
        {
          type: 'PROFILE',
          identifier: 'profile_id',
          value:
            reportProfileDataMock.body.result.ct_fleet_get_user_report_profile[0]
              .report_profile_id,
        },
        {
          type: 'BRANCH',
          identifier: 'branch_id',
          value: updatedValue.string,
        },
        {
          type: 'LIST(UP/DOWN/NONE)',
          identifier: 'updown',
          value: updatedValue.updown,
        },
        {
          type: 'TIMEFINE',
          identifier: 'duration',
          value: updatedValue.duration,
        },
        {
          type: 'TIME',
          identifier: 'working_hours_start',
          value: updatedValue.startTime,
        },
        {
          type: 'TIME',
          identifier: 'working_hours_end',
          value: updatedValue.endTime,
        },
        {
          type: 'VEHLIST',
          identifier: 'vehicle_limit',
          value: `g-${updatedValue.vehicle.id}`,
        },
        {
          type: 'DRIVERLIST',
          identifier: 'driver_name',
          value: `g-${updatedValue.driver.id}`,
        },
      ] satisfies Array<ReportPromptValueTypeApiInput>,
      delivery_type: 'Email',
      file_format: updatedValue.fileFormat,
      password: updatedValue.password,
      emails: updatedValue.emails,
      repeat_interval: updatedValue.frequencyNumber + ' ' + updatedValue.frequencyUnit,
      data_duration: '30 days',
    }

    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with(
          matchWithMethod<UpdateScheduledReportsV2Api.ApiInput>(
            req,
            'ct_fleet_update_scheduled_report_v2',
          ),
          ({ params }) => {
            cyExpect(params.file_format).toEqual(updatedScheduedReport.file_format)
            cyExpect(params.password).toEqual(updatedScheduedReport.password)
            cyExpect(params.emails).toEqual(updatedScheduedReport.emails)
            cyExpect(params.repeat_interval).toEqual(
              updatedScheduedReport.repeat_interval,
            )
            cyExpect(params.data_duration).toEqual(updatedScheduedReport.data_duration)

            for (const [index, prompt] of params.prompts
              .filter((prompt) => prompt.type !== 'DATE')
              .entries()) {
              cyExpect(prompt, `prompt ${index} ${prompt.identifier}`).toDeepEqual(
                updatedScheduedReport.prompts.find(
                  (p) => p.identifier === prompt.identifier,
                ) ?? ('NOT FOUND' as any),
              )
            }

            req.alias = 'updateSchedule'
            req.reply({
              delay: 50,
              body: {
                id: 10,
                result: {},
              },
            })
          },
        )
        .otherwise(() => handleApiCalls(req))
    })

    // registration
    cy.findByTestId('ReportForm-Registration-Single').click()
    cy.findByRole('option', {
      name: new RegExp(updatedValue.vehicle.name, 'i'),
    }).click()

    // driver
    cy.findByTestId('ReportForm-DriversAndGroups').click()
    cy.findByRole('option', {
      name: new RegExp(updatedValue.driver.name, 'i'),
    }).click()

    // start/end time
    // type in the start time
    cy.findByTestId(
      `ReportDrawer-Form-Time-${
        selectedReport.prompts.find(
          (p) => p.type === 'TIME' && p.identifier.includes('start'),
        )?.identifier
      }`,
    )
      .find('input')
      .type(updatedValue.startTime)

    // use dropdown to select time
    cy.findByTestId(
      `ReportDrawer-Form-Time-${
        selectedReport.prompts.find(
          (p) => p.type === 'TIME' && p.identifier.includes('end'),
        )?.identifier
      }`,
    )
      .find('button')
      .click()
    cy.get('[aria-label="7 hours"]').click()
    cy.get('[aria-label="7 minutes"]').click()

    // geofence
    cy.findByTestId('ReportForm-Geofence').find('input').type('{downArrow}{enter}')

    // geofence group
    cy.findByTestId('ReportForm-GeofenceGroup').find('input').type('{downArrow}{enter}')

    // number type
    cy.findByTestId('ReportForm-Recurring-NumberType-in_interval')
      .find('input')
      .type(`{selectAll}{del}${updatedValue.number}`)

    // string type
    cy.findByTestId('ReportForm-Recurring-StringType-km_cost')
      .find('input')
      .type(`{selectAll}{del}${updatedValue.km_cost}`)

    // NUM type
    cy.findByTestId('ReportForm-Recurring-NumberType-time_open')
      .find('input')
      .type(`{selectAll}{del}${updatedValue.number}`)

    // interval type
    cy.findByTestId('ReportForm-Recurring-NumberType-interval')
      .find('input')
      .type(`{selectAll}{del}${updatedValue.number}`)

    // BRANCH type
    cy.findByTestId('ReportForm-Recurring-StringType-branch_id')
      .find('input')
      .type(`{selectAll}{del}${updatedValue.string}`)

    // USER_ID type
    cy.findByTestId('ReportForm-Recurring-StringType-user_id')
      .find('input')
      .type(`{selectAll}{del}${updatedValue.string}`)

    // PROFILE_ID type
    cy.findByTestId('ReportForm-SingleSelect-profile_id').click()
    cy.findByRole('option', { name: /Profile 1/i }).click()

    // USER_NAME type
    cy.findByTestId('ReportForm-Recurring-StringType-user_name')
      .find('input')
      .type(`{selectAll}{del}${updatedValue.string}`)

    // TIMEFINE type
    cy.findByTestId('ReportForm-SingleSelect-duration-Selection').click()
    cy.findByTestId(`ReportForm-SingleSelect-duration-${updatedValue.duration}`).click()

    // UPDOWN LIST type
    cy.findByTestId('ReportForm-SingleSelect-updown-Selection').click()
    cy.findByTestId(`ReportForm-SingleSelect-updown-${updatedValue.updown}`).click()

    // file format
    cy.findByTestId('ReportForm-FileFormat-pdf').find('input[type="radio"]').click()

    // emails
    cy.findByTestId(`ReportForm-Email-Remove-${emails.length - 1}`).click()

    // interval frequency

    // monthly
    cy.findByTestId('ReportForm-Recurring-ReportFrequency-Selection').click()
    cy.findByTestId('ReportForm-Recurring-ReportFrequency-Monthly').click()
    cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom').click()
    cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom-Select').click()
    cy.findByRole('option', { name: /14/i }).click()

    // custom: 4 month
    cy.findByTestId('ReportForm-Recurring-ReportFrequency-Selection').click()
    cy.findByTestId('ReportForm-Recurring-ReportFrequency-Custom interval').click()
    cy.findByTestId('ReportForm-Recurring-Custom-Interval').click()
    cy.findByTestId(
      `ReportForm-Recurring-Custom-Interval-${capitalize(updatedValue.frequencyUnit)}s`,
    ).click()
    cy.findByTestId('ReportForm-Recurring-Custom-Frequency')
      .find('input')
      .type(`{selectAll}{del}${updatedValue.frequencyNumber}`)

    // data duration
    cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
    cy.findByTestId('ReportForm-Recurring-DataDuration-Previous 30 days').click()

    // password
    cy.findByTestId('ReportForm-PasswordInput')
      .find('input')
      .type(`{selectAll}{del}${updatedValue.password}`)

    // submit
    cy.findByTestId('ReportDrawer-SaveButton').click()
    cy.findByTestId('ConfirmationModal-confirm-button').click()

    cy.wait('@updateSchedule')
    cy.findByTestId('Report-ExportDrawer').should('not.exist')
  }

  describe('table behaviour', () => {
    it('show the manage recurring report', () => {
      navigateToManageRecurringReports()

      const firstScheduledReport = GLOBAL_scheduledReportList[0]
      const emails = multipleEmailsString.split(';')

      // the items inside one week should be 4
      GridContent().should('have.length', GLOBAL_scheduledReportList.length)

      // indicator text
      cy.findByTestId('Reports-ManageRecurring-Table-Toolbar-Summary').should(
        'have.text',
        `${GLOBAL_scheduledReportList.length} Active Recurring Reports`,
      )

      // the header should have 7 columns
      cy.findAllByRole('columnheader').should('have.length', 7)

      const firstItem = () => GridContent().first().findAllByRole('gridcell')
      firstItem().eq(0).should('have.text', firstScheduledReport.report_name)
      firstItem().eq(1).should('have.text', 'All')
      firstItem().eq(2).should('have.text', 'Daily')

      // emails folded
      firstItem()
        .eq(3)
        .should('have.text', `${emails[0]}, +${emails.length - 1} More`)
    })

    it('delete one report', () => {
      navigateToManageRecurringReports()
      const localList = R.clone(GLOBAL_scheduledReportList)

      const matchLocalMocks = (
        req: CyHttpMessages.IncomingHttpRequest,
        { otherwise }: { otherwise: () => void },
      ) => {
        match(req.body)
          .with(matchWithMethod(req, 'ct_fleet_cancel_schedule_reports'), () => {
            req.reply(endpointsMocks.ct_fleet_cancel_schedule_reports([]))
          })
          .with(matchWithMethod(req, 'ct_fleet_get_schedule_reports_v2'), () =>
            req.reply(endpointsMocks.ct_fleet_get_schedule_reports_v2(localList)),
          )
          .otherwise(() => otherwise())
      }

      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        matchLocalMocks(req, {
          otherwise: () => handleApiCalls(req),
        })
      })

      // click the first item's delete button
      cy.findByTestId('Report-Delete-1').click()
      cy.findByTestId('Reports-ManageRecurring-Table-DeleteModal').should('exist')
      cy.findByTestId('ConfirmationModal-confirm-button').click()

      cy.wait('@ct_fleet_cancel_schedule_reports')
      cy.findByTestId('Reports-ManageRecurring-Table-DeleteModal').should('not.exist')

      const deletedReport = localList[0]
      // Remove item from the list
      localList.splice(0, 1)

      cy.wait('@ct_fleet_get_schedule_reports_v2')

      // the first item should be removed
      GridContent().should('have.length', localList.length)
      // indicator text
      cy.findByTestId('Reports-ManageRecurring-Table-Toolbar-Summary').should(
        'have.text',
        `${localList.length} Active Recurring Reports`,
      )

      GridContent().findByText(deletedReport.report_name).should('not.exist')
    })
  })

  describe('click item to show recurring edit form drawer', () => {
    runTestsInOrderWithLoggingAndSetup('schedule report forms', {
      setupIfFirstTest: () => {
        navigateToManageRecurringReports()
      },
      tests: [
        {
          name: 'report 1, 1 day interval, 1 day duration, start/end time range, all value for vehicle driver, all possible prompts',
          fn: ({ setupIfFirstTest }) => {
            setupIfFirstTest()

            GridContent().first().click()

            checkFirstScheduledReport({ disabled: false })

            // close drawer
            cy.findByTestId('ReportForm-Close').click()
            return cy.findByTestId('Report-ExportDrawer').should('not.exist')
          },
        },
        {
          name: 'click edit button on readonly drawer form and submit the form',
          fn: ({ setupIfFirstTest }) => {
            setupIfFirstTest()

            cy.findByTestId('ReportDrawer-Recurring-BottomButtons').should('not.exist')

            GridContent().first().click()
            // cy.findByTestId(
            //   'Reports-ManageRecurring-Table-Recurring-Form-Edit-Button',
            // ).click()

            checkFirstScheduledReport({ disabled: false })

            cy.findByTestId('ReportDrawer-Recurring-BottomButtons').should('exist')

            // close drawer
            cy.findByTestId('ReportForm-Close').click()
            return cy.findByTestId('Report-ExportDrawer').should('not.exist')
          },
        },
        {
          name: 'report 2, 1 week interval, 30 days duration, single time, single value for vehicle/driver',
          fn: ({ setupIfFirstTest }) => {
            setupIfFirstTest()

            const selectedReport = GLOBAL_scheduledReportList[1]
            GridContent().eq(1).click()

            cy.findByTestId('Report-ExportDrawer').should('exist')

            // title
            cy.findByTestId('Reports-ExportForm-Recurring-Title').should(
              'have.text',
              selectedReport.report_name,
            )

            // no status chip
            cy.findByTestId('Reports-ExportForm-Recurring-Status').should('not.exist')

            // registration
            cy.findByTestId('ReportForm-Registration-Single')
              .find('input')
              .should('not.be.disabled')
              .should('have.value', vehiclesMock[0].name)

            // should not have reminder text
            cy.findByTestId('ReportForm-Registration-ReminderText').should('not.exist')

            // driver
            cy.findByTestId('ReportForm-DriversAndGroups')
              .find('input')
              .should('have.value', driversMock[0].name)
              .should('not.be.disabled')

            // start/end time
            cy.findByTestId(
              `ReportDrawer-Form-Time-${
                selectedReport.prompts.find(
                  (p) => p.type === 'TIME' && p.identifier.includes('start'),
                )?.identifier
              }`,
            )
              .find('input')
              .should('not.be.disabled')
              .should('have.value', '05:05')

            // interval frequency
            cy.findByTestId('ReportForm-Recurring-ReportFrequency')
              .find('input')
              .should('not.be.disabled')
              .should('have.value', 'Custom interval')

            cy.findByTestId('ReportForm-Recurring-Custom-Frequency')
              .find('input')
              .should('have.value', '1')

            cy.findByTestId('ReportForm-Recurring-Custom-Interval')
              .find('input')
              .should('have.value', 'Weeks')

            // data duration
            cy.findByTestId('ReportForm-Recurring-DataDuration')
              .find('input')
              .should('have.value', 'Previous 30 days')

            // interval summary exists
            cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('exist')

            // file format
            cy.findByTestId('ReportForm-FileFormat-xls')
              .find('input[type="radio"]')
              .should('not.be.disabled')
              .should('be.checked')

            // emails
            cy.findByTestId('ReportForm-Email-List').children().should('have.length', 1)

            // password
            cy.findByTestId('ReportForm-Password-Checkbox')
              .find('input')
              .should('not.be.checked')
              .should('not.be.disabled')
            cy.findByTestId('ReportForm-PasswordInput').should('not.exist')

            // close drawer
            cy.findByTestId('ReportForm-Close').click()
            return cy.findByTestId('Report-ExportDrawer').should('not.exist')
          },
        },
        {
          name: 'report 3, 2 month interval, previous month duration, multiple values for vehicle, driver group',
          fn: ({ setupIfFirstTest }) => {
            setupIfFirstTest()

            const selectedReport = GLOBAL_scheduledReportList[2]
            GridContent().eq(2).click()

            cy.findByTestId('Report-ExportDrawer').should('exist')

            // title
            cy.findByTestId('Reports-ExportForm-Recurring-Title').should(
              'have.text',
              selectedReport.report_name,
            )

            // registration
            cy.findByTestId('ReportForm-Registration-Multiple')
              .find('input')
              .should('not.be.disabled')
            // vehicle group name
            cy.findByTestId('ReportForm-Registration-Multiple')
              .find('.MuiChip-root > span')
              .first()
              .should('have.text', vehicleGroupsMock[0].name)

            // vehicle name
            cy.findByTestId('ReportForm-Registration-Multiple')
              .find('.MuiChip-root > span')
              .last()
              .should('have.text', vehiclesMock[0].name)

            // should have reminder text
            cy.findByTestId('ReportForm-Registration-ReminderText').should('exist')

            // driver
            cy.findByTestId('ReportForm-DriversAndGroups')
              .find('input')
              .should('have.value', driverGroupsMock[0].name)
              .should('not.be.disabled')

            // interval frequency
            cy.findByTestId('ReportForm-Recurring-ReportFrequency')
              .find('input')
              .should('not.be.disabled')
              .should('have.value', 'Custom interval')

            cy.findByTestId('ReportForm-Recurring-Custom-Frequency')
              .find('input')
              .should('have.value', '2')

            cy.findByTestId('ReportForm-Recurring-Custom-Interval')
              .find('input')
              .should('have.value', 'Months')

            // data duration
            // TODO - Ask Joseph why this is here
            // cy.findByTestId('ReportForm-Recurring-DataDuration')
            //   .find('input')
            //   .should('have.value', 'Previous month')

            // interval summary exists
            cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('exist')

            // file format
            cy.findByTestId('ReportForm-FileFormat-xls')
              .find('input[type="radio"]')
              .should('not.be.disabled')
              .should('be.checked')

            // emails
            cy.findByTestId('ReportForm-Email-List').children().should('have.length', 1)

            // password
            cy.findByTestId('ReportForm-Password-Checkbox')
              .find('input')
              .should('not.be.checked')
              .should('not.be.disabled')
            cy.findByTestId('ReportForm-PasswordInput').should('not.exist')

            // close drawer
            cy.findByTestId('ReportForm-Close').click()
            return cy.findByTestId('Report-ExportDrawer').should('not.exist')
          },
        },
        {
          name: 'report 4, weekly  interval, custom month duration, with password',
          fn: ({ setupIfFirstTest }) => {
            setupIfFirstTest()

            const selectedReport = GLOBAL_scheduledReportList[3]
            GridContent().eq(3).click()

            cy.findByTestId('Report-ExportDrawer').should('exist')

            // title
            cy.findByTestId('Reports-ExportForm-Recurring-Title').should(
              'have.text',
              selectedReport.report_name,
            )

            // should have reminder text
            cy.findByTestId('ReportForm-Registration-ReminderText').should('exist')

            // interval frequency
            cy.findByTestId('ReportForm-Recurring-ReportFrequency')
              .find('input')
              .should('not.be.disabled')
              .should('have.value', 'Weekly')

            // Monday
            cy.findByTestId('ReportForm-Recurring-DaysOfWeek-1')
              .find('input')
              .should('not.be.disabled')
              .should('be.checked')

            // Tuesday
            cy.findByTestId('ReportForm-Recurring-DaysOfWeek-2')
              .find('input')
              .should('not.be.disabled')
              .should('be.checked')

            // data duration
            cy.findByTestId('ReportForm-Recurring-DataDuration')
              .find('input')
              .should('have.value', 'Custom duration')

            // custom:3 months
            cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Amount')
              .find('input')
              .should('have.value', '3')

            cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Unit')
              .find('input')
              .should('have.value', 'Months')

            // interval summary exists
            cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('exist')

            // file format
            cy.findByTestId('ReportForm-FileFormat-xls')
              .find('input[type="radio"]')
              .should('not.be.disabled')
              .should('be.checked')

            // emails
            cy.findByTestId('ReportForm-Email-List').children().should('have.length', 1)

            // password
            cy.findByTestId('ReportForm-Password-Checkbox')
              .find('input')
              .should('not.be.checked')
              .should('not.be.disabled')
            cy.findByTestId('ReportForm-PasswordInput').should('not.exist')

            // close drawer
            cy.findByTestId('ReportForm-Close').click()
            return cy.findByTestId('Report-ExportDrawer').should('not.exist')
          },
        },
      ],
    })

    // it('delete report from drawer delete button', () => {
    //   GridContent().first().click()

    //   cy.findByTestId('Report-ExportDrawer').should('exist')

    //   // delete button
    //   cy.findByTestId(
    //     'Reports-ManageRecurring-Table-Recurring-Form-Delete-Button',
    //   ).click()
    //   cy.findByTestId('Reports-ManageRecurring-Table-DeleteModal').should('exist')
    //   cy.findByTestId('ConfirmationModal-confirm-button').click()

    //   cy.findByTestId('Reports-ManageRecurring-Table-DeleteModal').should('not.exist')

    //   // drawer closed
    //   cy.findByTestId('Report-ExportDrawer').should('not.exist')

    //   // one item should be removed
    //   GridContent().should('have.length', scheduledReportList.length - 1)
    //   // indicator text
    //   cy.findByTestId('Reports-ManageRecurring-Table-Toolbar-Summary').should(
    //     'have.text',
    //     `${scheduledReportList.length - 1} Active Recurring Reports`,
    //   )
    // })
  })

  describe('click edit icon to show recurring edit form drawer and submit', () => {
    it('report 1, 1 day interval, 1 day duration, start/end time range, all value for vehicle driver, all possible prompts', () => {
      navigateToManageRecurringReports()
      cy.findByTestId('Report-Edit-1').click()
      cy.findByTestId('ReportDrawer-Recurring-BottomButtons').should('not.exist')
      checkFirstScheduledReport({ disabled: false })

      cy.findByTestId('ReportDrawer-Recurring-BottomButtons').should('exist')

      // NOTE: fill form is costy, let's just do it once
      updateFirstScheduledReport()
    })
  })
})
