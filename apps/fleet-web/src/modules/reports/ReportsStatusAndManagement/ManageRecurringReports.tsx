import { useMemo } from 'react'
import {
  BaseGridToolbarContainer,
  BaseGridToolbarContainerWithItems,
  Box,
  DataGridAsTabItem,
  GridActionsCellItem,
  gridExpandedRowCountSelector,
  GridToolbarWithQuickFilter,
  LinearProgress,
  Tooltip,
  Typography,
  useDataGridDateColumns,
  useGridApiRef,
  useGridSelector,
  type GridApi,
  type GridColDef,
  type GridRowParams,
} from '@karoo-ui/core'
import DeleteIcon from '@mui/icons-material/Delete'
import EditIcon from '@mui/icons-material/Edit'
import { useHistory } from 'react-router'
import * as R from 'remeda'
import type { Except } from 'type-fest'

import type { ReportId } from 'api/types'
import { buildRouteQueryStringKeepingExistingSearchParams } from 'api/utils'
import { getLocale } from 'duxs/user'
import { getVehicleGroups, getVehiclesById } from 'duxs/vehicles'
import { useModal } from 'src/hooks'
import { useTypedSelector } from 'src/redux-hooks'
import { createDataGridTextColumn } from 'src/shared/data-grid/utils'
import type { ExcludeStrict } from 'src/types/utils'
import { CellTextWithMore } from 'src/util-components/CellTextWithMore'
import { ctIntl } from 'src/util-components/ctIntl'
import { getTranslatedFormatRepeatInterval } from 'src/util-functions/format-utils'

import {
  useFetchScheduledReportsQuery,
  type FetchScheduledReportsResolved,
} from '../api/queries'
import { EmailColumnTooltip, reportFormSearchParamsSchema } from '../util'
import DeleteRecurringReportConfirmationModal from './DeleteRecurringReportConfirmationModal'
import { parseVehicleRegistrationToGroupAndVehiclesNames } from './util'

type DataGridRow = Except<FetchScheduledReportsResolved[number], 'parameters'> & {
  parameters: Except<
    FetchScheduledReportsResolved[number]['parameters'],
    'registration'
  > & {
    registration: string | Array<string> | undefined
  }
}

export default function ManageRecurringReports({ basePath }: { basePath: string }) {
  const history = useHistory()
  const apiRef = useGridApiRef()
  const scheduleReportsQuery = useFetchScheduledReportsQuery()
  const { createDateTimeColumn } = useDataGridDateColumns({ filterMode: 'client' })
  const vehicleGroups = useTypedSelector(getVehicleGroups)
  const vehiclesById = useTypedSelector(getVehiclesById)
  const locale = useTypedSelector(getLocale) as ExcludeStrict<
    ReturnType<typeof getLocale>,
    undefined
  >

  const [isDeleteModalOpen, deleteModal] = useModal<{ id: ReportId; name: string }>()

  const rows = useMemo((): Array<DataGridRow> => {
    const scheduledReports = scheduleReportsQuery.data
    if (!scheduledReports) {
      return []
    }
    return scheduledReports.map((report): DataGridRow => {
      const parameters = report.parameters
      let vehicleRegistration: string | Array<string> = ''

      // TODO: currently only handle the vehicle and vehicle group
      if (parameters?.registration) {
        vehicleRegistration = parseVehicleRegistrationToGroupAndVehiclesNames({
          registration: parameters.registration,
          vehicleGroups,
          vehiclesById,
        })
      }

      return {
        ...report,
        parameters: {
          ...report.parameters,
          registration: vehicleRegistration,
        },
      }
    })
  }, [scheduleReportsQuery.data, vehicleGroups, vehiclesById])

  const columns = useMemo(
    (): Array<GridColDef<DataGridRow>> => [
      createDataGridTextColumn({
        field: 'name',
        headerNameMsg: { id: 'Report Name' },
        valueGetter: (_, row) => row.reportName,
        flex: 1,
      }),
      {
        field: 'parameters',
        headerName: ctIntl.formatMessage({ id: 'Vehicles' }),
        valueGetter: (_, { parameters: { registration } }) =>
          R.isArray(registration) ? registration.join(', ') : (registration ?? ''),
        renderCell: ({ value }) =>
          value?.includes(',') ? <CellTextWithMore value={value} /> : (value ?? ''),
        flex: 1,
        minWidth: 250,
      } satisfies GridColDef<DataGridRow, string>,
      createDataGridTextColumn({
        field: 'repeatInterval',
        headerNameMsg: { id: 'Recurring Interval' },
        valueGetter: (_, row) => {
          const formattedInterval =
            getTranslatedFormatRepeatInterval(row.repeatInterval, locale) ||
            ctIntl.formatMessage({ id: 'Unavailable interval' })

          return formattedInterval
        },
        flex: 1,
      }),
      createDataGridTextColumn({
        field: 'recipients',
        headerNameMsg: { id: 'Recipients' },
        valueGetter: (_, row) => (row.emails === null ? '' : row.emails.join(';')),
        renderCell: ({ row }) => {
          if (row.emails) {
            const emails = row.emails
            if (emails.length > 1) {
              return <EmailColumnTooltip emails={emails} />
            }
            return row.emails
          }
          return ''
        },
        flex: 2,
      }),

      createDateTimeColumn({
        field: 'description',
        headerName: ctIntl.formatMessage({ id: 'Next send date' }),
        valueGetter: (_, row) => row.nextRunDate,
        flex: 1,
      }),
      createDateTimeColumn({
        field: 'lastUpdated',
        headerName: ctIntl.formatMessage({ id: 'Last Sent Date' }),
        valueGetter: (_, row) => row.lastRan,
        flex: 1,
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        width: 120,
        align: 'right',
        getActions: ({ row: { reportName, id } }) => [
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Edit' })}
            arrow
            key="edit"
            placement="top"
          >
            <GridActionsCellItem
              data-testid={`Report-Edit-${id}`}
              label={ctIntl.formatMessage({ id: 'Edit' })}
              icon={<EditIcon />}
              onClick={() =>
                history.push(
                  `${basePath}/exportScheduledReport?${buildRouteQueryStringKeepingExistingSearchParams(
                    {
                      location: history.location,
                      schema: reportFormSearchParamsSchema,
                      searchParams: { type: 'edit', id },
                    },
                  )}`,
                )
              }
            />
          </Tooltip>,
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Delete' })}
            arrow
            key="delete"
            placement="top"
          >
            <GridActionsCellItem
              data-testid={`Report-Delete-${id}`}
              label={ctIntl.formatMessage({ id: 'Delete' })}
              icon={<DeleteIcon />}
              onClick={() => deleteModal.open({ id, name: reportName })}
            />
          </Tooltip>,
        ],
      },
    ],
    [createDateTimeColumn, locale, history, basePath, deleteModal],
  )

  return (
    <>
      {isDeleteModalOpen && deleteModal.data !== undefined && (
        <DeleteRecurringReportConfirmationModal
          onClose={deleteModal.close}
          {...deleteModal.data}
        />
      )}
      <DataGridAsTabItem
        hideFooterSelectedRowCount
        apiRef={apiRef}
        data-testid="Reports-ManageRecurring-Table"
        autoPageSize
        pagination
        rows={rows}
        columns={columns}
        sx={{ '& .MuiDataGrid-row': { cursor: 'pointer' } }}
        onRowClick={({ row: { id } }: GridRowParams<DataGridRow>) =>
          history.push(
            `${basePath}/exportScheduledReport?${buildRouteQueryStringKeepingExistingSearchParams(
              {
                location: history.location,
                schema: reportFormSearchParamsSchema,
                searchParams: { type: 'edit', id }, // Change from view to edit, keep the readonly part codes in case
              },
            )}`,
          )
        }
        loading={scheduleReportsQuery.isFetching}
        slots={{
          toolbar: CustomDataGridToolbar,
          loadingOverlay: LinearProgress,
        }}
        slotProps={{
          toolbar: {
            apiRef,
          } satisfies CustomDataGridToolbarProps,
          pagination: { showFirstButton: true, showLastButton: true },
        }}
      />
    </>
  )
}
export type CustomDataGridToolbarProps = { apiRef: React.MutableRefObject<GridApi> }

function CustomDataGridToolbar({ apiRef }: CustomDataGridToolbarProps) {
  const rowCount = useGridSelector(apiRef, gridExpandedRowCountSelector)
  return (
    <BaseGridToolbarContainer
      sx={{ display: 'grid', alignItems: 'center', pl: '10px' }} // pl: 10px to align with the first header of table
    >
      <BaseGridToolbarContainerWithItems
        sx={{
          justifyContent: 'space-between',
        }}
      >
        <Typography
          sx={{ fontWeight: 'bold', fontSize: 'inherit' }}
          data-testid="Reports-ManageRecurring-Table-Toolbar-Summary"
        >
          {ctIntl.formatMessage(
            {
              id: 'reccuringReport.numberOfActiveReports',
            },
            { values: { number: rowCount.toString() } },
          )}
        </Typography>

        <Box>
          <GridToolbarWithQuickFilter />
        </Box>
      </BaseGridToolbarContainerWithItems>
    </BaseGridToolbarContainer>
  )
}
