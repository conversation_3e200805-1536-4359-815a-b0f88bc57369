import { use<PERSON>allback, useMemo, useState } from 'react'
import {
  Box,
  DataGrid,
  GRID_TREE_DATA_GROUPING_FIELD,
  LinearProgress,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  useMemoBranded,
  type GridColDef,
  type GridGroupingColDefOverride,
} from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { match, P } from 'ts-pattern'

import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import FormSelection from 'src/util-components/selects/form-select'

import type { EventCategory } from '../api/types'
import { SectionWithTitle } from '../components/SectionWithTitle'
import { CustomGridTreeDataGroupingCell } from './CustomGridTreeDataGroupingCell'
import { ScoreChangeIndicator } from './ScoreChangeIndicator'
import type { GroupMetric, RateType, RiskEventRow } from './types'
import { eventCategoryOptions, formatTimeInMin } from './utils'

export const RiskEventDataGrid = ({
  dateRange,
  rowData,
  isSingleGroup,
  eventCategoryFilter,
  setEventCategoryFilter,
}: {
  dateRange: [DateTime, DateTime]
  rowData: Array<RiskEventRow>
  eventCategoryFilter: EventCategory
  setEventCategoryFilter: (value: EventCategory) => void
  isSingleGroup: boolean
}) => {
  const gridApiRef = useGridApiRef()
  const [expandedRowIds, setExpandedRowIds] = useState<ReadonlySet<string>>(new Set())

  const setRowExpansionIfGroupNode = useCallback(
    (rowId: string, isExpanded: boolean) => {
      const rowNode = gridApiRef.current.getRowNode(rowId)
      if (rowNode?.type !== 'group') {
        return
      }
      gridApiRef.current.setRowChildrenExpansion(rowId, isExpanded)

      setExpandedRowIds((prev) => {
        const newSet = new Set(prev)
        if (isExpanded) {
          newSet.add(rowId)
        } else {
          newSet.delete(rowId)
        }
        return newSet
      })
    },
    [gridApiRef],
  )

  const getChangeDisplayValue = useCallback(
    (value: number, type: RateType, numberType: 'count' | 'rate') =>
      match([type, numberType])
        .with(['timeBasedRate', 'count'], () => formatTimeInMin(value))
        .with(['timeBasedRate', 'rate'], () => `${value.toFixed(2)}%`)
        .with(['frequencyBasedRate', P._], ([_, nType]) =>
          ctIntl.formatMessage(
            { id: 'scorecards.numberOfEvents' },
            {
              values: {
                size: nType === 'rate' ? value.toFixed(2) : value,
              },
            },
          ),
        )
        .exhaustive(),
    [],
  )

  const renderGroupCell = useCallback(
    (group: GroupMetric, numberType: 'count' | 'rate') => {
      if (!group.metric.change) {
        return ''
      }

      const displayValue = getChangeDisplayValue(
        group.metric.change.value,
        group.type,
        numberType,
      )

      return (
        <>
          <Typography component="span">{group.metric.display}</Typography>
          <ScoreChangeIndicator
            {...group.metric.change}
            value={displayValue}
          />
        </>
      )
    },
    [getChangeDisplayValue],
  )

  const columnHelper = useDataGridColumnHelper<RiskEventRow>({ filterMode: 'client' })

  const columns = useMemo(
    (): Array<GridColDef<RiskEventRow>> => [
      columnHelper.string((_, row) => row.eventName, {
        field: 'eventName',
        headerName: isSingleGroup
          ? ctIntl.formatMessage({ id: 'Event' })
          : ctIntl.formatMessage({ id: 'Group' }),
        flex: 2,
        minWidth: 200,
        aggregable: true,
        renderCell: (params) => {
          if ('isAutoGenerated' in params.rowNode && params.rowNode.isAutoGenerated)
            return (
              <IntlTypography
                msgProps={{ id: 'scoreCards.driverScores.events.totalImpact' }}
                sx={(theme) => ({ fontWeight: theme.typography.fontWeightMedium })}
              />
            )

          return (
            <CustomGridTreeDataGroupingCell
              {...params}
              expandedRowIds={expandedRowIds}
              setRowExpansion={setRowExpansionIfGroupNode}
              color={params.row.type === 'group' ? params.row.color : undefined}
            />
          )
        },
      }),
      columnHelper.string(
        (_, row) => {
          if (row.type === 'group') {
            return 'group-count' // Dummy value for sorting
          }
          return row.count?.toString() || '0'
        },
        {
          field: 'count',
          headerName: ctIntl.formatMessage({ id: 'Count' }),
          flex: 1.5,
          minWidth: 150,
          aggregable: false,
          renderCell: ({ rowNode, row }) => {
            if ('isAutoGenerated' in rowNode && rowNode.isAutoGenerated) return null

            if (row.type === 'group') {
              return (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {renderGroupCell(row.count, 'count')}
                </Box>
              )
            }

            const displayValue = getChangeDisplayValue(
              row.count.change.value,
              row.rate.rateType,
              'count',
            )

            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography component="span">
                  {row.type === 'event' ? (row.count.display ?? '') : ''}
                </Typography>
                {row.count.change && (
                  <ScoreChangeIndicator
                    {...row.count.change}
                    value={displayValue}
                  />
                )}
              </Box>
            )
          },
        },
      ),
      columnHelper.string(
        (_, row) => (row.type === 'event' ? row.rate?.display : 'group-rate'),
        {
          field: 'rate',
          headerName: `%/${ctIntl.formatMessage({
            id: 'scoreCards.driverScores.events.columns.rate',
          })}`,
          flex: 2,
          minWidth: 250,
          renderCell: ({ rowNode, row }) => {
            if ('isAutoGenerated' in rowNode && rowNode.isAutoGenerated) return null

            if (row.type === 'group') {
              return (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {renderGroupCell(row.rate, 'rate')}
                </Box>
              )
            }

            const displayValue = getChangeDisplayValue(
              row.rate.change.value,
              row.rate.rateType,
              'rate',
            )

            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography component="span">{row.rate.display}</Typography>
                {row.rate.change && (
                  <ScoreChangeIndicator
                    {...row.rate.change}
                    value={displayValue}
                  />
                )}
              </Box>
            )
          },
        },
      ),
      columnHelper.number((_, row) => row.scoreImpact || 0, {
        field: 'score_impact',
        align: 'left',
        headerAlign: 'left',
        headerName: ctIntl.formatMessage({
          id: 'scoreCards.driverScores.events.columns.impact',
        }),
        flex: 1,
        minWidth: 120,
        aggregable: true,
        renderCell: ({ value }) => {
          if (value === null || value === undefined) return null

          return (
            <Typography
              sx={{
                color: typeof value === 'number' ? 'error.main' : 'success.main',
                fontWeight: 'medium',
              }}
            >
              {value > 0 ? `-${value}` : value}
            </Typography>
          )
        },
      }),
    ],
    [
      columnHelper,
      isSingleGroup,
      expandedRowIds,
      setRowExpansionIfGroupNode,
      getChangeDisplayValue,
      renderGroupCell,
    ],
  )

  const groupingColDef = useMemoBranded(
    (): GridGroupingColDefOverride<RiskEventRow> => ({
      headerName: isSingleGroup
        ? ctIntl.formatMessage({ id: 'Event' })
        : ctIntl.formatMessage({ id: 'Group' }),
      width: 350,
    }),
    [isSingleGroup],
  )
  return (
    <SectionWithTitle
      titleMsgId={
        isSingleGroup
          ? 'scoreCards.driverScores.events.title'
          : 'scoreCards.driverScores.events.titleWithGroup'
      }
      dateRange={dateRange}
    >
      <UserDataGridWithSavedSettingsOnIDB
        apiRef={gridApiRef}
        dataGridId="driver-scores-risk-events"
        Component={DataGrid}
        RootPaperProps={{ sx: { backgroundColor: 'transparent' }, elevation: 0 }}
        sx={{
          '.MuiDataGrid-main': { minHeight: 250, maxHeight: 250 },
          border: 'none',
        }}
        disableColumnMenu
        disableColumnFilter
        // disableRowSelectionOnClick // NOTE: disable here to avoid the bug of showing row when hover on the aggregation row
        columns={columns}
        rows={rowData}
        treeData
        getTreeDataPath={useCallbackBranded((row: RiskEventRow) => row.path, [])}
        groupingColDef={groupingColDef}
        initialState={{
          columns: {
            columnVisibilityModel: {
              [GRID_TREE_DATA_GROUPING_FIELD]: false,
            },
          },
          pagination: { paginationModel: { pageSize: 10, page: 0 } },
          aggregation: { model: { score_impact: 'sum' } },
        }}
        pagination
        pageSizeOptions={[10, 25, 50]}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              filterButton: { show: true },
              settingsButton: { show: true },
            },
            extraContent: {
              left: !isSingleGroup ? (
                <FormSelection
                  testId="scoreCard-riskEvent-toolbar-select"
                  options={eventCategoryOptions}
                  value={eventCategoryFilter}
                  onChange={setEventCategoryFilter}
                  label="Event category"
                />
              ) : null,
            },
          }),
          pagination: { showFirstButton: true, showLastButton: true },
        }}
      />
    </SectionWithTitle>
  )
}
