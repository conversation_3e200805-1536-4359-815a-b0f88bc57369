import { Typography } from '@karoo-ui/core'
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward'
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward'
import { match } from 'ts-pattern'

import type { ChangeDirection } from '../Overview/types'

export const ScoreChangeIndicator = ({
  reverse = false,
  renderScore,
  ...change
}: {
  direction: ChangeDirection
  value: string | number
  reverse?: boolean
  renderScore?: (value: string | number) => React.ReactNode
}) => {
  if (change.direction === 'unchanged' || change.value === 0) return null

  const { color, prefix, Icon } = match(change.direction)
    .with('increase', () => ({
      color: reverse ? ('success.main' as const) : ('error.main' as const),
      prefix: '+',
      Icon: ArrowUpwardIcon,
    }))
    .with('decrease', () => ({
      color: reverse ? ('error.main' as const) : ('success.main' as const),
      prefix: '-',
      Icon: ArrowDownwardIcon,
    }))
    .otherwise(() => ({
      color: 'text.primary' as const,
      prefix: '',
      Icon: () => null,
    }))

  return (
    <Typography
      component="span"
      sx={{
        color,
        fontWeight: 'medium',
        ml: 0.5,
        display: 'inline-flex',
        alignItems: 'center',
      }}
    >
      <Icon sx={{ fontSize: '1rem', mr: 0.25 }} />
      {prefix}
      {renderScore ? renderScore(change.value) : change.value}
    </Typography>
  )
}
