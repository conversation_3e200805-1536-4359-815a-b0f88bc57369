import { useMemo } from 'react'
import {
  Box,
  DataGrid,
  MenuItem,
  Select,
  useDataGridColumnHelper,
  useGridApiRef,
} from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { FormattedMessage } from 'react-intl'
import { useHistory } from 'react-router'
import { match, P } from 'ts-pattern'

import OverflowableTextTooltip from 'src/components/_popups/Tooltip/OverflowableText'
import type { UseDriversQueryReturnWithData } from 'src/modules/api/useDriversQuery'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'

import { getColorForScore } from '../../utils'
import type { FetchPeriodGroupScoresData } from '../api/queries'
import { ScoreChangeIndicator } from '../components/ScoreChangeIndicator'
import { SectionWithTitle } from '../components/SectionWithTitle'
import type { ChangeDirection } from '../Overview/types'
import { getSingleDriverPagePath } from '../utils'

type ScoreRankingDataGridProps = {
  queryData: FetchPeriodGroupScoresData['drivers']
  dateRange: [DateTime, DateTime]
  scoreRange: Array<number>
  setScoreRange: React.Dispatch<React.SetStateAction<Array<number>>>
  driversData: UseDriversQueryReturnWithData['data']
  weightageRange: Array<number>
}

// Define the row type for the data grid
type DataGridRow = FetchPeriodGroupScoresData['drivers'][number] & {
  name: string
  groupNames: Array<string>
  selectionRanking: number
}

const columnsGetters = {
  selectionRanking: (row: DataGridRow) => row.selectionRanking,
  fleetRanking: (row: DataGridRow) => row.globalRanking,
  driver: (row: DataGridRow) => row.name,
  groups: (row: DataGridRow) => row.groupNames,
  distanceInKm: (row: DataGridRow) => row.distanceInKm,
  score: (row: DataGridRow) => row.score,
  scoreChange: (row: DataGridRow) => row.prevScore ?? 0,
}

export default function ScoreRankingDataGrid({
  queryData,
  dateRange,
  scoreRange,
  setScoreRange,
  driversData,
  weightageRange,
}: ScoreRankingDataGridProps) {
  const history = useHistory()
  const gridApiRef = useGridApiRef()
  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'client' })

  const filteredAndRankedRows = useMemo(
    () =>
      queryData
        ? queryData
            .filter(
              (driver) =>
                driver.score !== null &&
                driver.score >= scoreRange[0] &&
                driver.score <= scoreRange[1],
            )
            .sort((a, b) => (b.score ?? 0) - (a.score ?? 0))
            .map((driver, index) => ({
              ...driver,
              name: driversData.allDriversById.get(driver.id)?.name ?? '',
              selectionRanking: index + 1,
              fleetRanking: driver.globalRanking, // Mock fleet ranking
              scoreChange: driver.prevScore, // Mock score change
            }))
        : [],
    [driversData.allDriversById, queryData, scoreRange],
  )

  const columns = useMemo(
    () => [
      columnHelper.number((_, row) => columnsGetters.selectionRanking(row), {
        field: 'selectionRanking',
        headerName: ctIntl.formatMessage({
          id: 'scoreCards.driverScore.ranking.grid.selectionRanking',
        }),
        flex: 0.5,
      }),
      columnHelper.number((_, row) => columnsGetters.fleetRanking(row), {
        field: 'fleetRanking',
        headerName: ctIntl.formatMessage({
          id: 'scoreCards.driverScore.ranking.grid.fleetRanking',
        }),
        flex: 0.5,
      }),
      columnHelper.string((_, row) => columnsGetters.driver(row), {
        field: 'driver',
        headerName: ctIntl.formatMessage({ id: 'Driver' }),
        flex: 1,
      }),
      columnHelper.string((_, row) => columnsGetters.groups(row).join(','), {
        field: 'groups',
        headerName: ctIntl.formatMessage({ id: 'Group' }),
        flex: 1,
        renderCell: ({ value }) => (
          <OverflowableTextTooltip placement="top">{value}</OverflowableTextTooltip>
        ),
      }),
      columnHelper.number((_, row) => columnsGetters.score(row), {
        field: 'score',
        headerName: ctIntl.formatMessage({ id: 'Score' }),
        flex: 0.6,
        align: 'left',
        headerAlign: 'center',
        renderCell: ({ row, value }) => {
          if (typeof value !== 'number') return null

          const prevScore = columnsGetters.scoreChange(row)
          const scoreChange = value - prevScore
          const direction = match(scoreChange)
            .with(
              P.when((num) => num > 0),
              () => 'increase',
            )
            .with(
              P.when((num) => num < 0),
              () => 'decrease',
            )
            .otherwise(() => 'unchanged') as ChangeDirection

          return (
            <Box
              display="flex"
              alignItems="center"
              gap={0.5}
            >
              <Box
                sx={(theme) => ({
                  bgcolor: getColorForScore(value, theme, weightageRange).light,
                  color: getColorForScore(value, theme, weightageRange).textColor,
                  borderRadius: 1,
                  px: 1,
                  py: 0.5,
                  fontWeight: 'bold',
                  textAlign: 'center',
                  minWidth: 40,
                })}
              >
                {value}
              </Box>
              <ScoreChangeIndicator
                direction={direction}
                value={Math.abs(scoreChange)}
                reverse
                renderScore={(value) =>
                  ctIntl.formatMessage(
                    { id: 'scoreCards.driverScores.changeValue' },
                    { values: { value } },
                  )
                }
              />
            </Box>
          )
        },
      }),
      columnHelper.number((_, row) => columnsGetters.distanceInKm(row), {
        field: 'distance',
        headerName: ctIntl.formatMessage({
          id: 'scoreCards.driverScore.ranking.grid.distance',
        }),
        flex: 0.5,
      }),
    ],
    [columnHelper, weightageRange],
  )

  const fromScoreOptions = useMemo(
    () =>
      Array.from({ length: scoreRange[1] }, (_, i) => i).map((value) => (
        <MenuItem
          key={value}
          value={value}
        >
          {value}
        </MenuItem>
      )),
    [scoreRange],
  )

  const toScoreOptions = useMemo(
    () =>
      Array.from({ length: 100 - scoreRange[0] }, (_, i) => i + 1 + scoreRange[0]).map(
        (value) => (
          <MenuItem
            key={value}
            value={value}
          >
            {value}
          </MenuItem>
        ),
      ),
    [scoreRange],
  )

  return (
    <SectionWithTitle
      titleMsgId="scoreCards.driverScore.ranking.grid.title"
      dateRange={dateRange}
    >
      <UserDataGridWithSavedSettingsOnIDB
        dataGridId="driver-score-ranking-grid"
        Component={DataGrid}
        apiRef={gridApiRef}
        sx={{
          '.MuiDataGrid-main': { minHeight: 300, maxHeight: 300 },
          border: 'none',
          '& .MuiDataGrid-row': { cursor: 'pointer' },
        }}
        RootPaperProps={{ sx: { backgroundColor: 'transparent' }, elevation: 0 }}
        rows={filteredAndRankedRows}
        columns={columns}
        disableRowSelectionOnClick
        onRowClick={({ row }) => {
          history.push(
            getSingleDriverPagePath(history.location, {
              type: 'driver',
              id: row.id,
              start: dateRange[0].startOf('day').toISO(),
              end: dateRange[1].endOf('day').toISO(),
            }),
          )
        }}
        pagination
        initialState={{
          pagination: { paginationModel: { pageSize: 10 } },
          sorting: {
            sortModel: [{ field: 'selectionRanking', sort: 'asc' }], // Default sort by selection ranking
          },
        }}
        pageSizeOptions={[10, 25, 50]}
        slots={{ toolbar: KarooToolbar }} // Basic toolbar for now
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              settingsButton: { show: true },
              filterButton: { show: true },
            },

            extraContent: {
              left: (
                <FormattedMessage
                  id="scoreCards.driverScore.ranking.grid.customScoreRange"
                  values={{
                    from: () => (
                      <Select
                        sx={{ minWidth: 68 }}
                        size="small"
                        value={scoreRange[0]}
                        onChange={(event) =>
                          setScoreRange((prev) => [Number(event.target.value), prev[1]])
                        }
                      >
                        {fromScoreOptions}
                      </Select>
                    ),
                    to: () => (
                      <Select
                        size="small"
                        sx={{ minWidth: 68 }}
                        value={scoreRange[1]}
                        onChange={(event) =>
                          setScoreRange((prev) => [prev[0], Number(event.target.value)])
                        }
                      >
                        {toScoreOptions}
                      </Select>
                    ),
                  }}
                />
              ),
            },
          }),
        }}
      />
    </SectionWithTitle>
  )
}
