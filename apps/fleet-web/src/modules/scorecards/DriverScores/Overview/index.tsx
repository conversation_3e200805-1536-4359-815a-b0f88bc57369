import { Box, CircularProgressDelayedCentered, useTheme } from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import {
  useScorecardDefaultConfigurationWeightageQuery,
  type FetchScorecardConfigurationWeightageData,
  type FetchScorecardDefaultConfigurationWeightageResolved,
} from '../../Settings/api/queries'
import type { FetchPeriodGroupScoresData } from '../api/queries'
import { ScoreOverviewChart } from '../components/ScoreOverviewChart'
import type { SelectedComparisonGroup } from '../types'
import { transformApiDataToOverviewChartData } from '../utils'
import DriverPerformance from './DriverPerformance'
import RiskEvents from './RiskEvents'

type DriverScoreOverviewProps = {
  dateRange: [DateTime, DateTime]
  groups: ReadonlyArray<SelectedComparisonGroup>
  periodGroupScoresData: FetchPeriodGroupScoresData
  configData: FetchScorecardConfigurationWeightageData
}

const DriverScoreOverview = ({
  dateRange,
  groups,
  configData,
  periodGroupScoresData,
}: DriverScoreOverviewProps) => {
  const defaultConfigQuery = useScorecardDefaultConfigurationWeightageQuery()

  return match(defaultConfigQuery)
    .with({ status: 'success' }, ({ data: defaultConfigData }) => (
      <DriverScoreOverviewContent
        dateRange={dateRange}
        groups={groups}
        configData={configData}
        defaultConfigData={defaultConfigData}
        periodGroupScoresData={periodGroupScoresData}
      />
    ))
    .with({ status: 'pending' }, () => <CircularProgressDelayedCentered />)
    .with({ status: 'error' }, () => null)
    .exhaustive()
}

const DriverScoreOverviewContent = ({
  dateRange,
  groups,
  configData,
  defaultConfigData,
  periodGroupScoresData,
}: DriverScoreOverviewProps & {
  configData: FetchScorecardConfigurationWeightageData
  defaultConfigData: FetchScorecardDefaultConfigurationWeightageResolved
}) => {
  const theme = useTheme()
  const chartData = transformApiDataToOverviewChartData({
    apiData: periodGroupScoresData.periodScores,
    startDate: dateRange[0].toJSDate(),
    endDate: dateRange[1].toJSDate(),
    comparisonGroups: groups,
    safetyScore: {
      color: theme.palette.info.main,
      value: periodGroupScoresData.safetyScore,
    },
  })

  return (
    <Box
      id="Scorecard-driverScores-overview-page"
      sx={{
        gap: 2,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <ScoreOverviewChart data={chartData} />
      <RiskEvents
        dateRange={dateRange}
        comparisonGroups={groups}
        apiData={periodGroupScoresData.riskEvents}
      />
      <DriverPerformance
        apiData={periodGroupScoresData.driversPerformances}
        dateRange={dateRange}
        comparisonGroups={groups}
        weightageRange={
          configData.configurations?.weightageCustomize.on
            ? configData.configurations?.weightageCustomize.range
            : (defaultConfigData.configurationRules.weightageCustomize.range ?? [])
        }
      />
    </Box>
  )
}

export default DriverScoreOverview
