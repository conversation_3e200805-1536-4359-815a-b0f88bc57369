import { use<PERSON>allback, useMemo, useState } from 'react'
import {
  DataGrid,
  GRID_TREE_DATA_GROUPING_FIELD,
  LinearProgress,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  useMemoBranded,
  useTheme,
  type GridColDef,
  type GridGroupingColDefOverride,
} from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { useHistory } from 'react-router'

import type { DriverId } from 'api/types'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { useDriversQuery } from 'src/modules/api/useDriversQuery'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { ColorScore, getColorForScore } from 'src/modules/scorecards/utils'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'

import type { FetchPeriodGroupScoresData } from '../../api/queries'
import { CustomGridTreeDataGroupingCell } from '../../components/CustomGridTreeDataGroupingCell'
import { ScoreChangeIndicator } from '../../components/ScoreChangeIndicator'
import { SectionWithTitle } from '../../components/SectionWithTitle'
import type { SelectedComparisonGroup } from '../../types'
import { getSingleDriverPagePath } from '../../utils'
import type { DriverPerformanceRow } from './types'
import { transformDriverPerformanceData } from './utils'

export default function DriverPerformance({
  dateRange,
  comparisonGroups,
  weightageRange,
  apiData,
}: {
  dateRange: [DateTime, DateTime]
  comparisonGroups: ReadonlyArray<SelectedComparisonGroup>
  weightageRange: Array<number>
  apiData: FetchPeriodGroupScoresData['driversPerformances']
}) {
  const theme = useTheme()
  const history = useHistory()
  const gridApiRef = useGridApiRef()
  const [expandedRowIds, setExpandedRowIds] = useState<ReadonlySet<string>>(new Set())

  const driverQuery = useDriversQuery()
  const columnHelper = useDataGridColumnHelper<DriverPerformanceRow>({
    filterMode: 'client',
  })

  const isSingleGroup = comparisonGroups.length === 1

  const setRowExpansionIfGroupNode = useCallback(
    (rowId: string, isExpanded: boolean) => {
      const rowNode = gridApiRef.current.getRowNode(rowId)
      if (rowNode?.type !== 'group') {
        return
      }
      gridApiRef.current.setRowChildrenExpansion(rowId, isExpanded)

      setExpandedRowIds((prev) => {
        const newSet = new Set(prev)
        if (isExpanded) {
          newSet.add(rowId)
        } else {
          newSet.delete(rowId)
        }
        return newSet
      })
    },
    [gridApiRef],
  )

  const driverNames = useMemo(() => {
    const map = new Map<DriverId, string>()

    if (apiData && driverQuery?.data) {
      for (const group of apiData) {
        for (const driver of group.drivers) {
          if (!map.has(driver.id)) {
            map.set(
              driver.id,
              driverQuery?.data?.allDriversById.get(driver.id)?.name || '',
            )
          }
        }
      }
    }
    return map
  }, [apiData, driverQuery?.data])

  const transformedData = useMemo(
    () =>
      transformDriverPerformanceData({
        apiData,
        comparisonGroups,
        isSingleGroup,
        driverNames,
      }),
    [apiData, comparisonGroups, isSingleGroup, driverNames],
  )

  const columns = useMemo(
    (): Array<GridColDef<DriverPerformanceRow>> => [
      columnHelper.string((_, row) => row.driverName, {
        field: 'driverName',
        headerName: isSingleGroup
          ? ctIntl.formatMessage({ id: 'Driver' })
          : ctIntl.formatMessage({ id: 'Group' }),
        flex: 2,
        minWidth: 200,
        renderCell: (params) => (
          <CustomGridTreeDataGroupingCell
            {...params}
            expandedRowIds={expandedRowIds}
            setRowExpansion={setRowExpansionIfGroupNode}
            color={params.row.type === 'group' ? params.row.color : undefined}
          />
        ),
      }),
      ...(isSingleGroup
        ? []
        : [
            columnHelper.number(
              (_, row) => (row.type === 'group' ? row.peopleCount : 0),
              {
                field: 'peopleCount',
                headerName: ctIntl.formatMessage({
                  id: 'scoreCards.driverScores.performance.noOfPeople',
                }),
                flex: 1,
                minWidth: 150,
                renderCell: ({ row }) => {
                  if (row.type !== 'group') return null
                  return <Typography>{row.peopleCount}</Typography>
                },
              },
            ),
          ]),
      columnHelper.number((_, row) => row.scoreChange?.value ?? 0, {
        field: 'scoreChange',
        headerName: ctIntl.formatMessage({
          id: 'scoreCards.driverScores.performance.changeSinceLastPeriod',
        }),
        flex: 1,
        minWidth: 180,
        headerAlign: 'left',
        align: 'left',
        renderCell: ({ row }) =>
          row.scoreChange ? (
            <ScoreChangeIndicator
              {...row.scoreChange}
              reverse
              renderScore={(value) =>
                ctIntl.formatMessage(
                  { id: 'scoreCards.driverScores.changeValue' },
                  { values: { value } },
                )
              }
            />
          ) : (
            '-'
          ),
      }),
      columnHelper.number((_, row) => row.score, {
        field: 'score',
        headerName: ctIntl.formatMessage({ id: 'Score' }),
        flex: 1,
        headerAlign: 'left',
        align: 'left',
        minWidth: 120,
        renderCell: ({ row }) => {
          if (row.score === null) {
            return null
          }
          const scoreRange = getColorForScore(row.score, theme, weightageRange)
          return (
            <ColorScore
              sx={{ color: scoreRange.textColor, backgroundColor: scoreRange.light }}
            >
              {row.score}
            </ColorScore>
          )
        },
      }),
    ],
    [
      columnHelper,
      isSingleGroup,
      expandedRowIds,
      setRowExpansionIfGroupNode,
      theme,
      weightageRange,
    ],
  )

  const groupingColDef = useMemoBranded(
    (): GridGroupingColDefOverride<DriverPerformanceRow> => ({
      headerName: ctIntl.formatMessage({ id: isSingleGroup ? 'Driver' : 'Group' }),
      width: 350,
    }),
    [isSingleGroup],
  )
  return (
    <SectionWithTitle
      titleMsgId="scoreCards.driverScores.performance.title"
      dateRange={dateRange}
    >
      <UserDataGridWithSavedSettingsOnIDB
        apiRef={gridApiRef}
        dataGridId="driver-performance"
        Component={DataGrid}
        RootPaperProps={{ sx: { backgroundColor: 'transparent' }, elevation: 0 }}
        sx={{
          '.MuiDataGrid-main': { minHeight: 250, maxHeight: 250 },
          border: 'none',
          '& .MuiDataGrid-row': { cursor: 'pointer' },
        }}
        disableColumnMenu
        disableColumnFilter
        disableRowSelectionOnClick
        onRowClick={({ row }) => {
          if (row.type === 'driver') {
            history.push(
              getSingleDriverPagePath(history.location, {
                type: 'driver',
                id: row.driverId,
                start: dateRange[0].startOf('day').toISO(),
                end: dateRange[1].endOf('day').toISO(),
              }),
            )
          }
        }}
        columns={columns}
        rows={transformedData}
        treeData
        getTreeDataPath={useCallbackBranded(
          (row: DriverPerformanceRow) => row.path,
          [],
        )}
        groupingColDef={groupingColDef}
        initialState={{
          columns: {
            columnVisibilityModel: {
              [GRID_TREE_DATA_GROUPING_FIELD]: false,
            },
          },
          pagination: { paginationModel: { pageSize: 10, page: 0 } },
        }}
        pagination
        pageSizeOptions={[10, 25, 50]}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              filterButton: { show: true },
              settingsButton: { show: true },
            },
          }),
          pagination: { showFirstButton: true, showLastButton: true },
        }}
      />
    </SectionWithTitle>
  )
}
