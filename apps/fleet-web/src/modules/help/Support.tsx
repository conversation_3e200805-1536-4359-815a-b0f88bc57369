import { useMemo } from 'react'
import { fill, isNil } from 'lodash'
import { Box, Button, Divider, MenuItem, Typography } from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import { useForm } from 'react-hook-form'
import { isPossiblePhoneNumber } from 'react-phone-number-input'
import { useDispatch } from 'react-redux'
import { z } from 'zod/v4'

import {
  getAuthenticatedUserAsAccountUser,
  getSettings_UNSAFE,
  shouldBeVisible,
  submitHelpRequest,
} from 'duxs/user'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { useEnhancedReducer } from 'src/hooks'
import { ADMIN } from 'src/modules/app/components/routes/admin'
import { ALERT_CENTER } from 'src/modules/app/components/routes/alert-center'
import { ALERTS } from 'src/modules/app/components/routes/alerts'
import { CARPOOL } from 'src/modules/app/components/routes/carpool'
import { COACHING } from 'src/modules/app/components/routes/coaching'
import { COSTS } from 'src/modules/app/components/routes/costs'
import { DASHBOARD } from 'src/modules/app/components/routes/dashboard'
import { DELIVERY } from 'src/modules/app/components/routes/delivery'
import { ENGINE_ALERTS } from 'src/modules/app/components/routes/engineAlerts'
import { FIELD_SERVICE } from 'src/modules/app/components/routes/field-service'
import { HELP } from 'src/modules/app/components/routes/help'
import { KNOW_THE_DRIVER } from 'src/modules/app/components/routes/knowthedriver'
import { LIST } from 'src/modules/app/components/routes/list'
import { MAINTENANCE } from 'src/modules/app/components/routes/maintenance'
import { MAP } from 'src/modules/app/components/routes/map'
import { PRIVACY } from 'src/modules/app/components/routes/privacy'
import { REPORTS } from 'src/modules/app/components/routes/reports'
import { RUC } from 'src/modules/app/components/routes/ruc'
import { SETTINGS } from 'src/modules/app/components/routes/settings'
import { TACHOGRAPH } from 'src/modules/app/components/routes/tachograph'
import { VISION } from 'src/modules/app/components/routes/vision'
import { useTypedSelector } from 'src/redux-hooks'
import type { AppState } from 'src/root-reducer'
import { messages } from 'src/shared/forms/messages'
import type { FixMeAny } from 'src/types'
import { ctToast } from 'src/util-components'
import { ctIntl } from 'src/util-components/ctIntl'

import type { SubTab, Tab } from '../app/components/routes/types'
import { SidePanel } from './components/SidePanel'
import { UploadFileContent } from './components/UploadFileContent'
import type { FileBeingCreated, FilesState } from './types/file'

const ACCEPTED_FILE_TYPES = 'image/png, image/jpeg, application/pdf'
const MAXIMUM_UPLOADED_FILES = 3
const MAXIMUM_FILE_SIZE = 1000000 // in bytes

const fileInitialState = (): FilesState => ({
  files: [],
  filePercentages: fill(new Array(MAXIMUM_UPLOADED_FILES), 0),
  fileSizesLoaded: fill(new Array(MAXIMUM_UPLOADED_FILES), 0),
})

const reducers = {
  setFile: (
    draft: FilesState,
    {
      payload: { name, size, data, index },
    }: { payload: FileBeingCreated & { index: number } },
  ) => {
    draft.files[index] = {
      name,
      size,
      data,
    }
    if (draft.filePercentages[index] < 100) {
      draft.filePercentages[index] = 100
    }

    if (draft.fileSizesLoaded[index] < size) {
      draft.fileSizesLoaded[index] = size
    }
  },

  setFilePercentage: (
    draft: FilesState,
    {
      payload: { filePercentage, fileSizeLoaded, index },
    }: { payload: { filePercentage: number; fileSizeLoaded: number; index: number } },
  ) => {
    draft.filePercentages[index] = filePercentage
    draft.fileSizesLoaded[index] = fileSizeLoaded
  },

  deleteFile: (draft: FilesState, { payload: index }: { payload: number }) => {
    const newFiles = [...draft.files].filter((_item, idx) => idx !== index)

    draft.files = newFiles
  },

  clearFiles: (draft: FilesState) => {
    draft.files = []
  },
}

// Needs to be a function to enable translations with ctIntl
const getValidationSchema = () =>
  z.object({
    issueType: z.string().min(1, messages.required),
    // subType: z.string().nullable(),
    name: z.string().min(1, messages.required),
    description: z.string().min(1, messages.required),
    email: z.string().email(),
    phoneNumber: z.string().refine((val) => isPossiblePhoneNumber(val), {
      message: 'formValidation.phoneNumber.includeCountryCode',
    }),
    jobTitle: z
      .string()
      .min(1, messages.required)
      .max(
        60,
        ctIntl.formatMessage(
          {
            id: messages.validStringMax,
          },
          {
            values: { number: 60 },
          },
        ),
      ),
  })

type Schema = z.infer<ReturnType<typeof getValidationSchema>>

const featuresToCheckPermission = [
  ADMIN,
  ALERT_CENTER,
  ALERTS,
  CARPOOL,
  COACHING,
  COSTS,
  DASHBOARD,
  DELIVERY,
  ENGINE_ALERTS,
  FIELD_SERVICE,
  HELP,
  KNOW_THE_DRIVER,
  LIST,
  MAINTENANCE,
  MAP,
  PRIVACY,
  REPORTS,
  RUC,
  SETTINGS,
  TACHOGRAPH,
  VISION,
]

function Support() {
  const settings = useTypedSelector(getSettings_UNSAFE)
  const user = useTypedSelector(getAuthenticatedUserAsAccountUser)
  const dispatch = useDispatch()
  const storeState = useTypedSelector((state) => state)

  const {
    facebookSocialLink,
    linkedinSocialLink,
    instagramSocialLink,
    lineContactLink,
    whatsappContactLink,
    helpSupportMinioEnabled,
  } = settings

  const { handleSubmit, control, formState, reset } = useForm<Schema>({
    resolver: zodResolverV4(getValidationSchema()),
    mode: 'all',
    defaultValues: {
      name: `${user.firstName ?? ''} ${user.lastName ?? ''}`.trim(),
      issueType: '',
      // subType: '',
      email: settings.email,
      phoneNumber: settings.phone || '',
      description: '',
      jobTitle: '',
    },
  })

  const availableFeaturesTabs = useMemo(
    () =>
      featuresToCheckPermission
        .filter((item) =>
          shouldBeVisible(storeState, {
            selector: item.tab.selector,
            // setting: item.tab.,
          }),
        )
        .map((item) => item.tab),
    [storeState],
  )

  const availableFeaturesSelectionOptions = useMemo(
    () => normalizeSelectOptions(storeState, availableFeaturesTabs),
    [availableFeaturesTabs, storeState],
  )

  // const getSubTabs = ({ subTabs }: Tab) => {
  //   if (subTabs === undefined) return []

  //   const returnedSubTabs = isFunction(subTabs)
  //     ? subTabs(storeState)
  //     : subTabs

  //   const filteredSubTabs = returnedSubTabs.filter((item) =>
  //     Flag.shouldBeVisible(storeState, {
  //       selector: item.selector,
  //       setting: item.flag,
  //     })
  //   )

  //   return normalizeSelectOptions(filteredSubTabs)
  // }

  // const availableSubTabs = (featureToCheck: string) => {
  //   const selectedTab = availableFeaturesTabs.find(
  //     (feature) => feature.id === featureToCheck
  //   )

  //   return selectedTab ? getSubTabs(selectedTab) : []
  // }

  const [state, actions] = useEnhancedReducer({
    initialState: fileInitialState,
    reducers,
  })

  const onFileChange = (event: {
    target: { files: Array<File> | FileList | null }
  }) => {
    const newFileCollection = event.target.files
    const currentFileCollectionLength = state.files.length

    if (!isNil(newFileCollection) && newFileCollection.length > 0) {
      if (newFileCollection.length + state.files.length > MAXIMUM_UPLOADED_FILES) {
        ctToast.fire('warn', 'help.support.uploadFileCountWarning', {
          formatMessageOptions: {
            values: {
              fileCount: MAXIMUM_UPLOADED_FILES,
            },
          },
        })

        return
      }

      for (const { size } of newFileCollection) {
        if (size > MAXIMUM_FILE_SIZE) {
          ctToast.fire('warn', 'help.support.uploadFileSizeWarning', {
            formatMessageOptions: {
              values: {
                fileSize: `${MAXIMUM_FILE_SIZE / 1000000} MB`,
              },
            },
          })

          return
        }
      }

      // eslint-disable-next-line
      // eslint-disable-next-line unicorn/no-for-loop
      for (let i = 0; i < newFileCollection.length; i += 1) {
        const reader = new FileReader()
        const { name, size } = newFileCollection[i]

        reader.readAsDataURL(newFileCollection[i])

        const progressEventListener = (progressEvent: ProgressEvent<FileReader>) => {
          const percentage =
            progressEvent.total > 0
              ? (progressEvent.loaded / progressEvent.total) * 100
              : 100
          actions.setFilePercentage({
            filePercentage: percentage,
            fileSizeLoaded: progressEvent.loaded,
            index: i + currentFileCollectionLength,
          })
        }

        reader.addEventListener('progress', progressEventListener)

        const loadEventListener = (loadEvent: FixMeAny) => {
          actions.setFile({
            name,
            size,
            data: loadEvent.target.result,
            index: i + currentFileCollectionLength,
          })
          reader.removeEventListener('progress', progressEventListener)
          reader.removeEventListener('load', loadEventListener)
        }

        reader.addEventListener('load', loadEventListener)
      }
    }
  }

  const submitForm = handleSubmit((values) => {
    dispatch(
      submitHelpRequest({
        type: values.issueType,
        message: values.description,
        email: values.email,
        phoneNumber: values.phoneNumber,
        name: `${values.name}${user.account ? ` - ${user.account}` : ''}`,
        uploadedFiles: state.files,
        jobTitle: values.jobTitle,
      }),
    )
    reset()
    actions.clearFiles()
  })

  const onFileDelete = (index: number) => actions.deleteFile(index)
  const shouldDisplaySidePanel = Boolean(
    facebookSocialLink ||
      linkedinSocialLink ||
      instagramSocialLink ||
      lineContactLink ||
      whatsappContactLink,
  )

  return (
    <Box
      sx={{
        px: 2,
        py: 9,
        display: 'flex',
        justifyContent: 'center',
        gap: 2,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography
          variant="h4"
          align="center"
          sx={{ color: 'primary.main', width: '640px', pb: 6 }}
        >
          {ctIntl.formatMessage({ id: 'help.support.mainTitle' })}
        </Typography>

        <Typography
          align="center"
          sx={{ color: 'secondary.dark' }}
        >
          {ctIntl.formatMessage({
            id: 'help.support.subTitle.first',
          })}
        </Typography>
        <Typography
          align="center"
          sx={{ color: 'secondary.dark', pb: 4 }}
        >
          {ctIntl.formatMessage({
            id: 'help.support.subTitle.secondary',
          })}
        </Typography>
        <Box
          sx={{
            gap: 3,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Box
            sx={{
              gap: 2,
              display: 'flex',
              flexDirection: 'column',
              width: '400px',
            }}
          >
            <TextFieldControlled
              select
              ControllerProps={{
                name: 'issueType',
                control,
              }}
              id="issue-type"
              fullWidth
              label={ctIntl.formatMessage({ id: 'help.support.formInput.moduleLabel' })}
              variant="outlined"
              data-testid="IssueTypeInput"
            >
              {availableFeaturesSelectionOptions.map((option) => (
                <MenuItem
                  key={option.value}
                  value={option.value}
                >
                  {option.label}
                </MenuItem>
              ))}
            </TextFieldControlled>

            {/*
                  // option to select a sub tab won't be available for now
                    {availableSubTabs(form.values.type).length > 0 && (
                      <SelectInput
                        field="subType"
                        form={form}
                        labelMsgProps={{
                          id: 'SubType',
                        }}
                        options={availableSubTabs(form.values.type)}
                      />
                  )} */}
            <TextFieldControlled
              ControllerProps={{
                name: 'description',
                control,
              }}
              label={ctIntl.formatMessage({
                id: 'help.support.formInput.descriptionLabel',
              })}
              required
              multiline
              rows={4}
              data-testid="SupportDescription"
            />
            {helpSupportMinioEnabled && (
              <>
                <Typography>
                  {ctIntl.formatMessage({ id: 'help.support.fileUpload.title' })}
                </Typography>
                <UploadFileContent
                  files={state.files}
                  filePercentages={state.filePercentages}
                  fileSizesLoaded={state.fileSizesLoaded}
                  onFileDelete={onFileDelete}
                  onFileChange={onFileChange}
                  acceptedFileTypes={ACCEPTED_FILE_TYPES}
                />
              </>
            )}
          </Box>

          <Divider />

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Typography sx={{ color: 'secondary.dark' }}>
              {ctIntl.formatMessage({
                id: 'help.support.formInput.contactData',
              })}
            </Typography>

            <TextFieldControlled
              ControllerProps={{
                name: 'name',
                control,
              }}
              label={ctIntl.formatMessage({
                id: 'Name',
              })}
              required
              data-testid="SupportContactName"
            />
            <TextFieldControlled
              ControllerProps={{
                name: 'jobTitle',
                control,
              }}
              label={ctIntl.formatMessage({
                id: 'global.jobTitle',
              })}
              required
              data-testid="SupportJobDescription"
            />
            <TextFieldControlled
              ControllerProps={{
                name: 'email',
                control,
              }}
              label={ctIntl.formatMessage({
                id: 'E-mail',
              })}
              required
              data-testid="SupportEmailAddress"
            />
            <TextFieldControlled
              ControllerProps={{
                name: 'phoneNumber',
                control,
              }}
              label={ctIntl.formatMessage({
                id: 'Phone Number',
              })}
              required
              data-testid="SupportPhoneNumber"
            />
          </Box>

          <Button
            onClick={submitForm}
            variant="contained"
            sx={{ alignSelf: 'center' }}
            disabled={!formState.isValid}
            data-testid="SubmitButton"
          >
            {ctIntl.formatMessage({
              id: 'Submit',
            })}
          </Button>
        </Box>
      </Box>
      {shouldDisplaySidePanel && <SidePanel />}
    </Box>
  )
}

export default Support

const normalizeSelectOptions = (state: AppState, options: Array<Tab> | Array<SubTab>) =>
  options
    .map((option) => {
      const optionText =
        typeof option.text === 'function' ? option.text(state) : (option.text ?? '')
      return {
        label: ctIntl.formatMessage({ id: optionText }),
        value: option.id || optionText,
      }
    })
    .sort((a, b) => a.label.localeCompare(b.label))
