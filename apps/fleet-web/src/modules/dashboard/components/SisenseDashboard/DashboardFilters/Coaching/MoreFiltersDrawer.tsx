import { useCallback, useMemo } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Divider,
  Drawer,
  Stack,
  TextField,
} from '@karoo-ui/core'
import type {
  QueryObserverPlaceholderResult,
  QueryObserverSuccessResult,
} from '@tanstack/react-query'
import * as R from 'remeda'

import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { DrawerCloseButton } from 'src/components/DrawerCloseButton'
import type { FetchCoachingDashboardFilters } from 'src/modules/coaching/CoachingDashboard/api/types'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import {
  Array_every,
  Array_filter,
  Array_filterMap,
  Array_map,
  Array_sort,
} from 'src/util-functions/performance-critical-utils'

import type { FetchCoachingDashboardFiltersResolved } from '../../api/query'
import type { CoachingDashboardDispatch, MoreFiltersDrawerFilterObject } from './types'

type Props = {
  dispatch: CoachingDashboardDispatch
  successCoachingDashboardFiltersQuery:
    | QueryObserverSuccessResult<FetchCoachingDashboardFiltersResolved>
    | QueryObserverPlaceholderResult<FetchCoachingDashboardFiltersResolved, unknown>
  filterObject: MoreFiltersDrawerFilterObject
  isFetchingStatsResults: boolean
}

/** THIS IS CREATED BY INCREDIBLE RODRIGO, I JUST COPY THEM HERE */

export default function MoreFiltersDrawer({
  successCoachingDashboardFiltersQuery,
  filterObject,
  dispatch,
  isFetchingStatsResults,
}: Props) {
  const {
    vehicleGroupsSortedByLabel,
    driverGroups,
    drivers: allDrivers,
    userGroups,
    usersAndDriversJoinByDriverId,
    usersAndDriverGroupsJoinByDriverGroupId,
  } = successCoachingDashboardFiltersQuery.data

  const isDriverGroupRelatedToSelectedUserGroups = useCallback(
    (driverGroup: FetchCoachingDashboardFilters.DriverGroup) => {
      const userGroupsByHierarchyLevelValues = R.values(
        filterObject.userGroupsByHierarchyLevel.asObject(),
      )

      return Array_every(userGroupsByHierarchyLevelValues, (userGroup) =>
        userGroup
          ? (usersAndDriverGroupsJoinByDriverGroupId
              .get(driverGroup.id)
              ?.has(userGroup.id) ?? false)
          : true,
      )
    },
    [filterObject.userGroupsByHierarchyLevel, usersAndDriverGroupsJoinByDriverGroupId],
  )

  const isUserGroupRelatedToSelectedDriverGroups = useCallback(
    (userGroup: FetchCoachingDashboardFilters.UserGroupInHierarchy) => {
      const driverGroupsByHierarchyLevelValues = R.values(
        filterObject.driverGroupsByHierarchyLevel.asObject(),
      )

      return Array_every(driverGroupsByHierarchyLevelValues, (driverGroup) =>
        driverGroup
          ? (usersAndDriverGroupsJoinByDriverGroupId
              .get(driverGroup.id)
              ?.has(userGroup.id) ?? false)
          : true,
      )
    },
    [
      filterObject.driverGroupsByHierarchyLevel,
      usersAndDriverGroupsJoinByDriverGroupId,
    ],
  )

  const driversOptions = useMemo(() => {
    const driverGroupsByHierarchyLevelValues = R.values(
      filterObject.driverGroupsByHierarchyLevel.asObject(),
    )
    const isEveryDriverGroupValueNil = driverGroupsByHierarchyLevelValues.every((v) =>
      R.isNullish(v),
    )

    const userGroupsByHierarchyLevelValues = R.values(
      filterObject.userGroupsByHierarchyLevel.asObject(),
    )
    const isEveryUserGroupValueNil = userGroupsByHierarchyLevelValues.every((v) =>
      R.isNullish(v),
    )

    const array =
      isEveryDriverGroupValueNil && isEveryUserGroupValueNil
        ? allDrivers.array
        : Array_filter(allDrivers.array, (driver) => {
            const everyUserGroupPasses = Array_every(
              userGroupsByHierarchyLevelValues,
              (levelGroup) =>
                levelGroup === null
                  ? true
                  : (usersAndDriversJoinByDriverId.get(driver.id)?.has(levelGroup.id) ??
                    false),
            )

            const everyDriverGroupPasses = Array_every(
              driverGroupsByHierarchyLevelValues,
              (levelGroup) =>
                levelGroup === null
                  ? true
                  : driver.ascendentGroupIds.has(levelGroup.id),
            )

            return everyUserGroupPasses && everyDriverGroupPasses
          })

    return Array_sort(array, (a, b) => a.label.localeCompare(b.label))
  }, [
    allDrivers.array,
    filterObject.driverGroupsByHierarchyLevel,
    filterObject.userGroupsByHierarchyLevel,
    usersAndDriversJoinByDriverId,
  ])

  function renderDriverGroupsRecursively(
    groupIds: ReadonlyArray<FetchCoachingDashboardFilters.DriverGroup['id']>,
  ): JSX.Element | null {
    if (driverGroups === 'NO_ACCESS') {
      return null
    }

    if (groupIds.length === 0) {
      return null
    }

    const firstGroup = driverGroups.byId.get(groupIds[0])
    if (!firstGroup) {
      return null
    }

    const { hierarchyLevelsById } = driverGroups

    const groupHierarchyLevelId = firstGroup.hierarchyLevelId

    const valueForThisLevel =
      filterObject.driverGroupsByHierarchyLevel.get(groupHierarchyLevelId)

    return (
      <>
        <Autocomplete
          sx={{ mb: 2 }}
          {...getAutocompleteVirtualizedProps({
            options: Array_sort(
              Array_filterMap(groupIds, (id, { RemoveSymbol }) => {
                const group = driverGroups.byId.get(id)
                return group && isDriverGroupRelatedToSelectedUserGroups(group)
                  ? group
                  : RemoveSymbol
              }),
              (a, b) => a.label.localeCompare(b.label),
            ),
          })}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={valueForThisLevel ?? null}
          onChange={(_, newValue) => {
            dispatch({
              type: 'drawer_on_driver_groups_autocomplete_change',
              newValue,
              hierarchyLevelId: groupHierarchyLevelId,
            })
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              label={hierarchyLevelsById.get(groupHierarchyLevelId)?.label ?? ''}
            />
          )}
        />

        {valueForThisLevel
          ? renderDriverGroupsRecursively(valueForThisLevel.childrenGroupsIds)
          : null}
      </>
    )
  }

  function renderUserGroupsRecursively(
    groupObjectArray: ReadonlyArray<{
      id: FetchCoachingDashboardFilters.UserGroupInHierarchy['id']
      hierarchyLevelId: FetchCoachingDashboardFilters.UserGroupInHierarchy['hierarchyLevelId']
    }>,
  ): JSX.Element | null {
    if (userGroups === 'NO_ACCESS') {
      return null
    }

    if (groupObjectArray.length === 0) {
      return null
    }

    const firstGroupObject = groupObjectArray[0]
    if (!firstGroupObject) {
      return null
    }

    const { hierarchyLevelsById } = userGroups

    const groupHierarchyLevelId = firstGroupObject.hierarchyLevelId

    const valueForThisLevel =
      filterObject.userGroupsByHierarchyLevel.get(groupHierarchyLevelId)

    return (
      <>
        <Autocomplete
          sx={{ mb: 2 }}
          {...getAutocompleteVirtualizedProps({
            options: Array_sort(
              Array_filterMap(
                groupObjectArray,
                ({ id, hierarchyLevelId }, { RemoveSymbol }) => {
                  const group = userGroups.byHierarchyLevelIdById
                    .get(hierarchyLevelId)
                    ?.get(id)
                  return group && isUserGroupRelatedToSelectedDriverGroups(group)
                    ? group
                    : RemoveSymbol
                },
              ),
              (a, b) => a.label.localeCompare(b.label),
            ),
          })}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={valueForThisLevel ?? null}
          onChange={(_, newValue) => {
            dispatch({
              type: 'drawer_on_user_groups_autocomplete_change',
              newValue,
              hierarchyLevelId: groupHierarchyLevelId,
            })
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              label={hierarchyLevelsById.get(groupHierarchyLevelId)?.label ?? ''}
            />
          )}
        />

        {valueForThisLevel
          ? renderUserGroupsRecursively(valueForThisLevel.childrenGroupsIdsWithLevel)
          : null}
      </>
    )
  }

  return (
    <Drawer
      anchor="right"
      open
      PaperProps={{
        sx: {
          maxWidth: '45vw',
          minWidth: 500,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between', // important for the position sticky buttons to work
          pt: 3,
          pb: 0, // important for the position sticky buttons to work
          px: 3,
          overflow: 'auto',
        },
      }}
      onClose={() => dispatch({ type: 'on_drawer_close' })}
      variant="persistent"
      data-testid="DashboardCoachingFilter-Drawer"
    >
      <Box>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
            pb: 5,
          }}
        >
          <IntlTypography
            variant="h5"
            msgProps={{
              id: 'More Filters',
            }}
          />
          <DrawerCloseButton onClick={() => dispatch({ type: 'on_drawer_close' })} />
        </Box>

        <Stack spacing={3}>
          {vehicleGroupsSortedByLabel === 'NO_ACCESS' ? null : (
            <Stack spacing={3}>
              <Autocomplete
                data-testid="DashboardCoachingFilter-VehicleGroup"
                {...getAutocompleteVirtualizedProps({
                  options: vehicleGroupsSortedByLabel,
                })}
                value={filterObject.vehicleGroup ?? null}
                onChange={(_, value) => {
                  dispatch({
                    type: 'drawer_on_vehicle_groups_autocomplete_change',
                    value,
                  })
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={ctIntl.formatMessage({ id: 'Vehicle Group' })}
                  />
                )}
              />

              <Divider />
            </Stack>
          )}

          {driverGroups === 'NO_ACCESS' ? null : (
            <Stack spacing={2}>
              <IntlTypography msgProps={{ id: 'Driver Groups' }} />

              <Stack>
                {renderDriverGroupsRecursively(
                  Array_map(driverGroups.rootArraySortedByLabel, (g) => g.id),
                )}
              </Stack>
            </Stack>
          )}

          {userGroups === 'NO_ACCESS' ? null : (
            <Stack spacing={2}>
              <IntlTypography msgProps={{ id: 'User Groups' }} />

              <Stack>
                {renderUserGroupsRecursively(
                  Array_map(userGroups.rootArraySortedByLabel, (g) => ({
                    id: g.id,
                    hierarchyLevelId: g.hierarchyLevelId,
                  })),
                )}
              </Stack>
            </Stack>
          )}

          <Autocomplete
            data-testid="DashboardCoachingFilter-Driver"
            {...getAutocompleteVirtualizedProps({
              options: driversOptions,
            })}
            value={filterObject.driver ?? null}
            renderInput={(params) => (
              <TextField
                {...params}
                label={ctIntl.formatMessage({ id: 'Drivers' })}
              />
            )}
            onChange={(_, value) => {
              dispatch({
                type: 'drawer_on_drivers_autocomplete_change',
                value,
              })
            }}
          />
        </Stack>
      </Box>

      <Box
        sx={(theme) => ({
          display: 'flex',
          justifyContent: 'space-between',
          position: 'sticky',
          zIndex: 1,
          background: theme.palette.background.paper,
          bottom: 0,
          py: 3,
        })}
      >
        <Button
          variant="outlined"
          color="secondary"
          onClick={() => dispatch({ type: 'on_drawer_close' })}
        >
          {ctIntl.formatMessage({ id: 'Cancel' })}
        </Button>

        <Button
          data-testid="DashboardCoachingFilter-Drawer-SaveButton"
          variant="contained"
          onClick={() => dispatch({ type: 'drawer_on_save_button_click' })}
          disabled={isFetchingStatsResults}
          startIcon={
            isFetchingStatsResults ? (
              <CircularProgress
                color="inherit"
                size={16}
              />
            ) : null
          }
        >
          {ctIntl.formatMessage({ id: 'Save' })}
        </Button>
      </Box>
    </Drawer>
  )
}
