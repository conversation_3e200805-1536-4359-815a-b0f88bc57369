import { Fragment, useCallback, useMemo } from 'react'
import {
  Autocomplete,
  CircularProgress,
  DataGrid,
  DEFAULT_GRID_COL_TYPE_KEY,
  getGridDefaultColumnTypes,
  Grid,
  GRID_AGGREGATION_FUNCTIONS,
  GRID_AGGREGATION_ROOT_FOOTER_ROW_ID,
  GridActionsCellItem,
  LinearProgress,
  Stack,
  TextField,
  Tooltip,
  useCallbackBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type GetApplyFilterFn,
  type GridAggregationFunction,
  type GridColDef,
  type GridFilterOperator,
} from '@karoo-ui/core'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { DateTime } from 'luxon'
import { first } from 'remeda'

import type { DriverId } from 'api/types'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import theme from 'src/components/_themes/tachograph'
import ServiceEntryMarkerWithToolTip from 'src/components/_timeline/Activity/Marker/Service/Entry/WithTooltip'
import ServiceExitMarkerWithToolTip from 'src/components/_timeline/Activity/Marker/Service/Exit/WithTooltip'
import TimelineBar from 'src/components/_timeline/Bar'
import ETachoDriverIcon from 'src/components/Driver/TachographDetails/ETachoDriver/ETachoIcon'
import useIntl from 'src/modules/components/connected/useIntl'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { getTimePeriodPct } from 'src/modules/map-view/DriversMapView/Tachograph/utils'
import ActivityDateRangePicker from 'src/modules/tachograph/components/ActivityDateRangePicker'
import { ActivityGridToolbar } from 'src/modules/tachograph/components/ActivityGridToolbar'
import { getFragmentTooltipMessage } from 'src/modules/tachograph/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import {
  useTachographActivityQuery,
  type FetchTachographActivity,
} from '../../api/useTachographActivityQuery'
import { useTachographWorkJourneysActivityQuery } from '../../api/useTachographWorkJourneysActivityQuery'
import type { CommonTabComponentProps } from '../types'
import {
  activityColumnsIds,
  getActivityTimeAndDistanceColumns,
  type ActivityColumnId,
  type ColumnId,
} from './columns'
import type { DriverActivityDrawerData, TachographActivityData } from './types'

type WorkJourneysStartAndEndPeriodsByDate = Record<
  string,
  {
    isStartDate?: boolean
    isEndDate?: boolean
  }
>

const defaultColumnTypes = getGridDefaultColumnTypes()

const getVehiclesDisplayValue = (vehicles: TachographActivityData['vehicles']) =>
  vehicles.length > 0 ? vehicles.map((v) => v.name).join(', ') : '-'

type Props = {
  drawer: {
    isDrawerOpen: boolean
    open: () => void
    close: () => void
    data: DriverActivityDrawerData | undefined
  }
}

type DataGridRow = FetchTachographActivity.Return[number]

const FullDayActivityTab = ({
  isLoadingDrivers,
  selectedDriverOption,
  driverOptions,
  onDriverChange,
  selectedDateRange,
  onDateRangeChange,
  onActivityDetailsButtonClick,
}: CommonTabComponentProps & Props) => {
  const { formatNumber } = useIntl()
  const apiRef = useGridApiRef()
  const eTacoDriverIds = useMemo(
    () => new Set(driverOptions.filter((d) => d.isETachoDriver).map((d) => d.value)),
    [driverOptions],
  )

  const today = DateTime.now().startOf('day')
  const from = selectedDateRange.value[0]?.startOf('day')
  const to = selectedDateRange.value[1]?.endOf('day')

  // Check if today is within the selected range (inclusive)
  const isTodaySelected = from && to && today >= from && today <= to

  // Only refetch if today is selected because it is the only case where it makes sense to update the data
  const refetchOptions = isTodaySelected
    ? {
        refetchInterval: 60_000,
        refetchIntervalInBackground: true,
      }
    : {
        refetchInterval: undefined,
        refetchIntervalInBackground: false,
      }

  // Call activity after having a driver selected
  const tachographActivityQuery = useTachographActivityQuery(
    {
      driverId: (selectedDriverOption as NonNullable<typeof selectedDriverOption>)
        ?.value,
      timestampFrom: selectedDateRange.formatted.from,
      timestampTo: selectedDateRange.formatted.to,
    },
    {
      enabled: selectedDriverOption !== null,
      ...refetchOptions,
    },
  )

  const tachographWorkJourneysActivityQuery =
    useTachographWorkJourneysActivityQuery<WorkJourneysStartAndEndPeriodsByDate>(
      {
        driverId:
          (selectedDriverOption as NonNullable<typeof selectedDriverOption>)?.value ??
          null,
        timestampFrom: selectedDateRange.formatted.from,
        timestampTo: selectedDateRange.formatted.to,
      },
      {
        ...refetchOptions,
        select: (data) =>
          data.workJourneys.reduce<WorkJourneysStartAndEndPeriodsByDate>(
            (acc, entry) => {
              const startDate = DateTime.fromJSDate(entry.startDate.raw).toFormat(
                'yyyy-MM-dd HH:mm:ss',
              )
              const endDate = DateTime.fromJSDate(entry.endDate.raw).toFormat(
                'yyyy-MM-dd HH:mm:ss',
              )
              acc[startDate] = {
                isStartDate: true,
              }
              acc[endDate] = {
                isEndDate: true,
              }
              return acc
            },
            {},
          ),
      },
    )

  const hasWorkJourneyTimestamp = useCallback(
    (date: string | null, state: 'start' | 'end') => {
      if (tachographWorkJourneysActivityQuery.data && date !== null) {
        const workJourney = tachographWorkJourneysActivityQuery.data[date]

        return (
          Boolean(workJourney) &&
          (state === 'start' ? workJourney.isStartDate : workJourney.isEndDate)
        )
      }
      return false
    },
    [tachographWorkJourneysActivityQuery.data],
  )

  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'client' })

  // The column widths are important for the print style
  const columns = useMemo((): Array<GridColDef<DataGridRow>> => {
    const isETachoDriver = (() => {
      if (tachographActivityQuery.data === undefined) {
        return false
      }

      return first(tachographActivityQuery.data)?.driver.isETachoDriver ?? false
    })()

    const columns: Array<GridColDef<DataGridRow>> = [
      columnHelper.date({
        field: 'date',
        headerName: ctIntl.formatMessage({ id: 'Date' }),
        valueGetter: (_, row) => {
          if (!row.date) {
            return null
          }

          return row.date.raw
        },
        valueFormatter: (value) => {
          if (value !== null) {
            return DateTime.fromJSDate(value).toFormat('D ccc')
          }

          return ''
        },
        minWidth: 160,
        resizable: true,
      }),
      columnHelper.string(
        (_, row) => {
          if (!row.vehicles) {
            return null
          }

          return getVehiclesDisplayValue(row.vehicles)
        },
        {
          field: 'vehicles',
          headerName: ctIntl.formatMessage({ id: 'Vehicles' }),
          minWidth: 110,
          sortable: false,
        },
      ),
      ...getActivityTimeAndDistanceColumns(
        formatNumber,
        getVehiclesDisplayValue,
        isETachoDriver,
      ),
      {
        field: 'timeline',
        headerName: ctIntl.formatMessage({
          id: 'Timeline',
        }),
        sortable: false,
        filterable: false,
        valueGetter: undefined,
        minWidth: 200,
        flex: 1,
        disableExport: true,
        renderCell: ({ row }) => {
          if (!row.timeline) {
            return null
          }

          const tableDensity = apiRef.current.exportState().density

          return (
            <Grid
              sx={{
                height: '100%',
                width: '100%',
                paddingY: 1,
                paddingX: 0,
              }}
            >
              <TimelineBar>
                {row.timeline.map((period) => (
                  <Fragment
                    key={`${row.driver.id}_${period.startPct}_${period.endPct}_${period.status}`}
                  >
                    {hasWorkJourneyTimestamp(period.endTs, 'start') && (
                      <ServiceEntryMarkerWithToolTip
                        pctLeft={period.endPct}
                        {...(tableDensity === 'compact' && {
                          height: '6',
                          spacingIndex: 2,
                        })}
                      />
                    )}
                    <TimelineBar.FragmentWithTooltip
                      color={theme.colors.tachographDriverActivity[period.status]}
                      pctLeft={period.startPct}
                      pctWidth={getTimePeriodPct(period)}
                      tooltipProps={{
                        title: getFragmentTooltipMessage(period),
                        placement: 'bottom',
                      }}
                    />
                    {hasWorkJourneyTimestamp(period.endTs, 'end') && (
                      <ServiceExitMarkerWithToolTip
                        pctLeft={period.endPct}
                        {...(tableDensity === 'compact' && {
                          height: '6',
                          spacingIndex: 2,
                        })}
                      />
                    )}
                  </Fragment>
                ))}
              </TimelineBar>
            </Grid>
          )
        },
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({
          id: 'Actions',
        }),
        getActions: ({ id, row }) => {
          if (id === GRID_AGGREGATION_ROOT_FOOTER_ROW_ID) {
            return []
          }

          return [
            <Tooltip
              key="info"
              title={ctIntl.formatMessage({
                id: 'tachograph.activity.viewDetailsButton.tooltip',
              })}
            >
              <GridActionsCellItem
                icon={<InfoOutlinedIcon />}
                label={ctIntl.formatMessage({
                  id: 'tachograph.activity.viewDetailsButton.tooltip',
                })}
                onClick={() => {
                  onActivityDetailsButtonClick(row.date.raw)
                }}
              />
            </Tooltip>,
          ]
        },
      },
    ]

    const wrapOperator = (
      operator: GridFilterOperator<TachographActivityData, unknown, unknown>,
    ) => {
      const getApplyFilterFn: GetApplyFilterFn<
        TachographActivityData,
        unknown,
        unknown
      > = (filterItem, column) => {
        const innerFilterFn = operator.getApplyFilterFn(filterItem, column)
        if (!innerFilterFn) {
          return innerFilterFn
        }

        return (value, row, colDef, apiRef) => {
          if (colDef.type === 'string') {
            const formattedValue = apiRef.current.getRowFormattedValue<unknown>(
              row,
              colDef,
            )
            return innerFilterFn(formattedValue, row, colDef, apiRef)
          }

          return innerFilterFn(value, row, colDef, apiRef)
        }
      }

      return {
        ...operator,
        getApplyFilterFn,
      }
    }

    return columns.map((col) => {
      const filterOperators =
        col.filterOperators ??
        defaultColumnTypes[col.type ?? DEFAULT_GRID_COL_TYPE_KEY].filterOperators

      if (filterOperators === undefined) {
        return col
      } else {
        return {
          ...col,
          filterOperators: filterOperators.map((operator) => wrapOperator(operator)),
        }
      }
    })
  }, [
    columnHelper,
    formatNumber,
    tachographActivityQuery.data,
    apiRef,
    hasWorkJourneyTimestamp,
    onActivityDetailsButtonClick,
  ])

  const dataGridDefaults = useMemo(() => {
    const visibility: Record<ColumnId, boolean> = {
      Date: true,
      Vehicles: true,
      Timeline: true,
      'map.tachographDrivers.driverActivityStatus.time.driving': true,
      'map.tachographDrivers.driverActivityStatus.time.otherWork': true,
      'map.tachographDrivers.driverActivityStatus.time.effectiveWorking': true,
      'map.tachographDrivers.driverActivityStatus.time.available': true,
      'map.tachographDrivers.driverActivityStatus.time.active': true,
      'map.tachographDrivers.driverActivityStatus.time.workForOtherEntities': true,
      'map.tachographDrivers.driverActivityStatus.time.rest': true,
      'map.tachographDrivers.driverActivityStatus.time.unknown': true,
      'map.tachographDrivers.driverActivityStatus.time.restAndUnknown': true,
      'map.tachographDrivers.driverActivityStatus.time.availableNational': false,
      'map.tachographDrivers.driverActivityStatus.time.availableInternational': false,
      'map.tachographDrivers.driverActivityStatus.time.drivingNational': false,
      'map.tachographDrivers.driverActivityStatus.time.drivingInternational': false,
      'map.tachographDrivers.driverActivityStatus.time.drivingNight': false,
      'map.tachographDrivers.driverActivityStatus.time.otherWorkNational': false,
      'map.tachographDrivers.driverActivityStatus.time.otherWorkInternational': false,
      'map.tachographDrivers.driverActivityStatus.time.otherWorkNight': false,
      'map.tachographDrivers.driverActivityStatus.time.activeNational': false,
      'map.tachographDrivers.driverActivityStatus.time.activeInternational': false,
      'map.tachographDrivers.driverActivityStatus.time.restNational': false,
      'map.tachographDrivers.driverActivityStatus.time.restInternational': false,
      'map.tachographDrivers.driverActivityStatus.time.unknownNational': false,
      'map.tachographDrivers.driverActivityStatus.time.unknownInternational': false,
      'map.tachographDrivers.driverActivityStatus.time.effectiveWorkingNational': false,
      'map.tachographDrivers.driverActivityStatus.time.effectiveWorkingInternational': false,
      'map.tachographDrivers.driverActivityStatus.time.effectiveWorkingNight': false,
      'map.tachographDrivers.driverActivityStatus.distanceAsDriver': false,
      'map.tachographDrivers.driverActivityStatus.distanceAsCoDriver': false,
      'map.tachographDrivers.driverActivityStatus.totalDistance': true,
      'map.tachographDrivers.driverActivityStatus.distanceFromFile': true,
    }

    return {
      visibility,
      aggregationModel: activityColumnsIds.reduce(
        (acc, curr) => {
          acc[curr] = 'activityTotalAggregation'
          return acc
        },
        {} as Record<ActivityColumnId, string>,
      ),
    }
  }, [])

  const isLoading =
    (tachographActivityQuery.status === 'pending' &&
      tachographActivityQuery.fetchStatus === 'fetching') ||
    isLoadingDrivers

  return (
    <UserDataGridWithSavedSettingsOnIDB
      Component={DataGrid}
      apiRef={apiRef}
      getRowId={useCallbackBranded((row: DataGridRow) => row.date.formatted, [])}
      dataGridId="tachographActivity"
      columns={columns}
      rows={tachographActivityQuery.data ?? []}
      aggregationFunctions={{
        ...GRID_AGGREGATION_FUNCTIONS,
        activityTotalAggregation,
      }}
      initialState={{
        sorting: {
          sortModel: [{ field: 'date', sort: 'desc' }],
        },
        columns: {
          columnVisibilityModel: dataGridDefaults.visibility,
        },
        pinnedColumns: {
          left: ['date', 'vehicles'],
          right: ['timeline', 'actions'],
        },
        aggregation: {
          model: dataGridDefaults.aggregationModel,
        },
        density: 'compact',
      }}
      aggregationRowsScope="all"
      hideFooter
      autoPageSize
      loading={isLoading}
      slots={{
        toolbar: ActivityGridToolbar,
        loadingOverlay: LinearProgress,
      }}
      disableRowSelectionOnClick
      slotProps={{
        toolbar: {
          leftContent: (
            <>
              <Autocomplete
                value={selectedDriverOption}
                disabled={selectedDriverOption === null}
                sx={{ width: 350 }}
                size="medium"
                {...getAutocompleteVirtualizedProps({
                  options: driverOptions,
                  renderRowSingleItemContent: (option) => {
                    const isETachoDriver =
                      option.value !== undefined &&
                      eTacoDriverIds.has(option.value as DriverId)

                    return (
                      <Stack
                        direction="row"
                        alignItems="center"
                        gap={1}
                      >
                        {isETachoDriver && (
                          <ETachoDriverIcon
                            sx={{ height: 'fit-content' }}
                            fontSize="small"
                          />
                        )}
                        {option.label}
                      </Stack>
                    )
                  },
                })}
                onChange={(_, value) => {
                  if (value !== null) {
                    onDriverChange(value.value)
                  }
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={(() => {
                      if (isLoadingDrivers) {
                        return (
                          <Stack
                            direction="row"
                            alignItems="center"
                            gap={1}
                          >
                            <CircularProgress size={13} />

                            {ctIntl.formatMessage({
                              id: 'global.loadingDrivers',
                            })}
                          </Stack>
                        )
                      }

                      return ctIntl.formatMessage({ id: 'Driver' })
                    })()}
                  />
                )}
              />
              <ActivityDateRangePicker
                selectedDateRange={selectedDateRange.value}
                onChange={onDateRangeChange}
              />
            </>
          ),
        },
      }}
      sx={{
        '& .MuiDataGrid-columnHeaderTitleContainerContent': {
          height: '100%',
        },
      }}
    />
  )
}

export default FullDayActivityTab

const activityTotalAggregation: GridAggregationFunction<number, number | null> = {
  apply: (params) => {
    if (params.values.length === 0) {
      return null
    }

    return params.values.reduce((acc = 0, curr = 0) => acc + curr, 0)
  },
  label: '',
  columnTypes: ['string'],
}
