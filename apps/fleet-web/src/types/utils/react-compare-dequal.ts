type ComparableObject =
  | { [Key in string]?: ReactDequalComparable }
  | { toJSON: () => ReactDequalComparable }
type ComparableArray = ReadonlyArray<ReactDequalComparable>

export type ReactDequalComparable =
  | string
  | number
  | boolean
  | null
  | undefined
  | ComparableObject
  | ComparableArray
  | Map<unknown, unknown> // values within Sets and Maps use value equality
  | Set<unknown> // keys within Maps use value equality

export type NotDeepComparableMsg =
  'The value must be deep comparable using dequal library.'

export type IfDeepComparableReactDeps<T, IfYes, IfNot = NotDeepComparableMsg> =
  T extends ReadonlyArray<ReactDequalComparable> ? IfYes : IfNot
