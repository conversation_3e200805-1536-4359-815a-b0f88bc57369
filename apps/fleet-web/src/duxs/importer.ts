import { createSelector } from '@reduxjs/toolkit'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny } from 'src/types'

// Actions
export const FETCH_IMPORTERS = 'FETCH_IMPORTERS'
export const UPLOAD_IMPORT_DATA = 'FETCH_IMPORT_FIELDS'
export const UPLOAD_MIFLEET_IMPORT_DATA = 'FETCH_MIFLEET_IMPORT_FIELDS'
export const RECEIVE_IMPORTERS = 'RECEIVE_IMPORTERS'
export const RECEIVE_IMPORT_RESULT = 'RECEIVE_IMPORT_RESULT'
export const RECEIVE_MIFLEET_IMPORT_RESULT = 'RECEIVE_MIFLEET_IMPORT_RESULT'
export const CLEAR_IMPORT_RESULT = 'CLEAR_IMPORT_RESULT'
export const FILE_FIELDS_VALID = 'FILE_FIELDS_VALID'
export const FETCH_SAVE_MAPPING = 'FETCH_SAVE_MAPPING'
export const RECEIVE_SAVED_MAPPING = 'RECEIVE_SAVED_MAPPING'
export const NEW_MAPPING_TEMPLATE = 'NEW_MAPPING_TEMPLATE'
export const RESPONSE_NEW_MAPPING_TEMPLATE = 'RESPONSE_NEW_MAPPING_TEMPLATE'
export const DELETE_SAVE_MAPPING = 'DELETE_SAVE_MAPPING'
export const RESPONSE_DELETE_SAVE_MAPPING = 'RESPONSE_DELETE_SAVE_MAPPING'
export const EDIT_SAVE_MAPPING = 'EDIT_SAVE_MAPPING'
export const RESPONSE_EDIT_SAVE_MAPPING = 'RESPONSE_EDIT_SAVE_MAPPING'

type State = {
  importers: Array<any>
  categories: Array<any> | null
  isImporting: boolean
  hasFetchedImporters: boolean
  importResult: any
  importError: any
  fileFieldsValid: boolean
  errorMessage: string | null
  savedTemplate: Array<Record<string, any>>
}

// Reducer
const initialState: State = {
  importers: [],
  categories: null,
  isImporting: false,
  hasFetchedImporters: false,
  importResult: null,
  importError: null,
  fileFieldsValid: true,
  errorMessage: null,
  savedTemplate: [],
}

export default function reducer(state = initialState, action: FixMeAny): State {
  switch (action.type) {
    case RECEIVE_IMPORTERS: {
      return {
        ...state,
        hasFetchedImporters: true,
        importers: action.payload.importers,
        categories: action.payload.categories,
      }
    }
    case UPLOAD_IMPORT_DATA: {
      return {
        ...state,
        isImporting: true,
      }
    }
    case UPLOAD_MIFLEET_IMPORT_DATA: {
      return {
        ...state,
        isImporting: true,
      }
    }
    case RECEIVE_IMPORT_RESULT: {
      return {
        ...state,
        isImporting: false,
        importResult: action.payload.result,
        importError: action.payload.importError,
      }
    }
    case RECEIVE_MIFLEET_IMPORT_RESULT: {
      return {
        ...state,
        isImporting: false,
        importResult: action.payload.result,
        importError: action.payload.importError,
      }
    }
    case CLEAR_IMPORT_RESULT: {
      return {
        ...state,
        importResult: null,
        importError: null,
      }
    }
    case FILE_FIELDS_VALID: {
      return {
        ...state,
        fileFieldsValid: action.payload.fileFieldsValid,
        errorMessage: action.payload.errorMessage,
      }
    }
    case RECEIVE_SAVED_MAPPING: {
      return {
        ...state,
        savedTemplate: action.payload,
      }
    }
    case RESPONSE_NEW_MAPPING_TEMPLATE: {
      return {
        ...state,
        savedTemplate: [...state.savedTemplate, action.payload.objectList],
      }
    }
    case RESPONSE_DELETE_SAVE_MAPPING: {
      return {
        ...state,
        savedTemplate: state.savedTemplate.filter(
          (row) => row.settings_name !== action.payload.objectList.settings_name,
        ),
      }
    }

    case RESPONSE_EDIT_SAVE_MAPPING: {
      const updatedList = state.savedTemplate.map((row) => {
        if (row.settings_name === action.payload.objectList.settings_name) {
          return {
            ...row,
            settings_value: action.payload.objectList.settings_value,
          }
        }
        return row
      })

      return {
        ...state,
        savedTemplate: updatedList,
      }
    }

    default: {
      return state
    }
  }
}

// Actions
export function fetchImporters() {
  return {
    type: FETCH_IMPORTERS,
  }
}

export function uploadImportData({
  data,
  id,
  name,
}: {
  data: Array<FixMeAny>
  id: string
  name: string
}) {
  return {
    type: UPLOAD_IMPORT_DATA,
    payload: { data, id, name },
  }
}

export function uploadMiFleetImportData({
  data,
  prompts,
  mifleet_import_type,
}: {
  data: Array<FixMeAny>
  prompts: FixMeAny
  mifleet_import_type: string
}) {
  return {
    type: UPLOAD_MIFLEET_IMPORT_DATA,
    payload: { data, prompts, mifleet_import_type },
  }
}

export function clearImportResult() {
  return {
    type: CLEAR_IMPORT_RESULT,
  }
}

export function setFieldsValidationState({
  fileFieldsValid,
  errorMessage = '',
}: {
  fileFieldsValid: boolean
  errorMessage?: string
}) {
  return {
    type: FILE_FIELDS_VALID,
    payload: { fileFieldsValid, errorMessage },
  }
}
export function fetchSavedMapping(groupName?: string) {
  return {
    type: FETCH_SAVE_MAPPING,
    payload: groupName,
  }
}
export function createNewMapping(payload: FixMeAny) {
  return {
    type: NEW_MAPPING_TEMPLATE,
    payload: payload,
  }
}
export function deleteSaveMapping(payload: FixMeAny) {
  return {
    type: DELETE_SAVE_MAPPING,
    payload: payload,
  }
}
export function updateSaveMapping(payload: FixMeAny) {
  return {
    type: EDIT_SAVE_MAPPING,
    payload: payload,
  }
}
// Selectors

export const getImporters = (state: AppState) => state.importer.importers
export const getSavedMapping = (state: AppState) => state.importer.savedTemplate

export const getFileFieldsValid = (state: AppState) => ({
  fileFieldsValid: state.importer.fileFieldsValid,
  errorMessage: state.importer.errorMessage,
})

export const getCategories = createSelector(
  (state: AppState) => state.importer.categories,
  getImporters,
  (categories, importers) =>
    importers.length > 0
      ? (categories?.filter((c) => importers.some((i) => c.id === i.category)) ?? [])
      : [],
)
export const getImportResult = (state: AppState) => state.importer.importResult
export const getImportError = (state: AppState) => state.importer.importError
