import { isEmpty, round, isNil, compact, pull, isArray } from 'lodash'
import { createAction } from '@reduxjs/toolkit'
import { filterOutInvalidCoordinates } from 'cartrack-utils'

import { unmountedLiveVisionRightPanel } from 'duxs/live-vision'
import { getMapType, getTimelineTablesActiveTab } from 'duxs/map-timeline'
import { createStatefulAction } from 'duxs/utils'
import { CLOSE_DETAILS_PANEL, SELECT_VEHICLE_TRIP_SUMMARY } from 'duxs/shared'
import { formatDistance } from 'src/util-components/formatted-distance'
import { ctIntl } from 'src/util-components/ctIntl'
import { isValidGPSData } from 'src/util-functions/map-utils'
import { id as tableId } from 'src/modules/tables/vehicle-events'
import {
  changedActivityDateRange,
  getActivityActiveTab,
  unmountedDetailsPanel,
} from 'src/modules/map-view/FleetMapView/DetailsPanel/slice'

import type {
  SharedTimelineEventsTypes,
  BasicEvent,
  BasicEventUI,
  FetchMultipleDaysTimeline,
  FetchTimelineEventsUI,
  TimelineActivityEvent,
} from 'api/timeline/types'
import type { FetchTimelineEventsRawResolved } from 'src/api/timeline'
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'
import type { TripMarkerData } from 'src/modules/map-view/map/types'
import type { TimelineTableTripToSelect } from 'src/modules/map-view/timeline/types'
import {
  DriverNameVisibilityStatus,
  type DriverName,
  type VehicleId,
  type VehicleTripId,
} from 'api/types'

import {
  chartNormalizer,
  generateSensorsColumns,
  reGenerateSensorsColumns,
} from './utils'
import { getSettings_UNSAFE, type UserPreferences } from '../user'
import { getFocusedVehicle, getIsLoadingSummaryTrips } from '../vehicles'
import { getSensorTypeIdEnumType } from 'api/utils'
import { createSelectorWithStrictMode } from 'src/redux-utils'
import type { GridColDef } from '@karoo-ui/core'
import type { ExcludeStrict, ExtractStrict } from 'src/types/utils'
import { batchReduxStateUpdatesFromSaga } from 'src/sagas/actions'
import { DateTime } from 'luxon'
import type {
  GeofencesMapLayers,
  LivePositionMapLayers,
  MapsMapLayers,
  RootMapLayers,
  SensorsMapLayers,
} from 'duxs/map-types'

export type TimelineEvent = FetchTimelineEventsRawResolved['events'][number]
export type TimelineEventWithRoadSpeed = TimelineEvent & {
  allowRoadSpeed: boolean | undefined
}

export type TimelineSensorWithColor = State['sensorsByNumber'][number] & {
  color: string
}
export type DigitalTimelineSensor = ExtractStrict<
  TimelineSensorWithColor,
  { signalType: 'DIGITAL' }
>
export type AnalogTimelineSensor = ExcludeStrict<
  TimelineSensorWithColor,
  DigitalTimelineSensor
>

export type SelectedTimelineSensors = Record<
  TimelineSensorWithColor['sensorNumber'],
  true
>

const prefix = 'timeline/'

export const receiveTimelineEventsRaw = createAction<{
  timelineEventsRaw: FetchTimelineEventsRawResolved['events']
  timelineTrips: Array<Array<TimelineEventWithRoadSpeed>>
  timelineTripsReset: Array<Array<TimelineEventWithRoadSpeed>>
  sensorsByNumber: FetchTimelineEventsRawResolved['sensorsByNumber']
  isPollingEvents: boolean
  isTodayDateSelected: boolean
}>(prefix + 'receiveTimelineEventsRaw')

export const onChartXVariantTabGroupClick = createAction<State['xAxisChartType']>(
  prefix + 'onChartXVariantTabGroupClick',
)
export const fetchMultipleDaysTimeline =
  createStatefulAction<FetchMultipleDaysTimeline.Return>(
    prefix + 'fetchMultipleDaysTimeline',
  )

export const onTimelineChartsPointerUp = createAction<{
  nearestEventId: string
}>(prefix + 'onTimelineChartsPointerUp')

export const selectTimelineTrip = createAction<{
  trip: TimelineTableTripToSelect
}>(prefix + 'selectTimelineTrip')

export const receiveTimelineEventsUi = createAction<
  | (FetchTimelineEventsUI.ReturnWithTrips & {
      reset: FetchTimelineEventsUI.ReturnWithTrips['trips']
    })
  | {
      trips: undefined
      lastPosition: FetchTimelineEventsUI.ParsedLastPosition
    }
>(prefix + 'receiveTimelineEventsUi')

export const setSelectedCompactTrip = createAction<State['selectedCompactTrip']>(
  prefix + 'setSelectedCompactTrip',
)

export const CANCEL_FETCH_TIMELINE_DATA = 'CANCEL_FETCH_TIMELINE_DATA'
export const UPDATE_TIMELINE_EVENTS_LOADING = 'UPDATE_TIMELINE_EVENTS_LOADING'
export const UPDATE_TIMELINE_BAR_UI_LOADING = 'UPDATE_TIMELINE_BAR_UI_LOADING'
export const DELETE_VEHICLE_TRIP_DETAILS = 'DELETE_VEHICLE_TRIP_DETAILS'
export const CLEAR_TIMELINE = 'CLEAR_TIMELINE'
export const REQUEST_SVR_POLL = 'REQUEST_SVR_POLL'
export const SHOW_TRIP_STATISTICS = 'timeline: SHOW_TRIP_STATISTICS'
export const FETCH_TRIP_STATISTICS_START = 'timeline: FETCH_TRIP_STATISTICS_START'
export const FETCH_TRIP_STATISTICS_SUCCESS = 'timeline: FETCH_TRIP_STATISTICS_SUCCESS'
export const FETCH_TRIP_STATISTICS_FINISH = 'timeline: FETCH_TRIP_STATISTICS_FINISH'
export const SET_FILTERED_TIMELINE_TRIPS_EVENTS = 'SET_FILTERED_TIMELINE_TRIPS_EVENTS'
export const SET_TIMELINE_TRIPS_VISIBILITY_PREFERENCES =
  'SET_TIMELINE_TRIPS_VISIBILITY_PREFERENCES'
export const SET_TIMELINE_TRIPS_TABLE_ORDER_MODE = 'SET_TIMELINE_TRIPS_TABLE_ORDER_MODE'

type LayerLabelMeta = { msgId: string } | { translatedLabel: string }

export type MapLayerOptions = Partial<{
  [key in RootMapLayers]: {
    labelMeta: LayerLabelMeta
    children: ReadonlyArray<{ key: string; labelMeta: LayerLabelMeta }>
  }
}>

export const originalLayerGroups = {
  harshEvents: {
    labelMeta: { msgId: 'Harsh Events' },
    children: [
      { key: 'harshEventsSpeeding', labelMeta: { msgId: 'Harsh Events Speeding' } },
    ],
  },
  sensors: {
    labelMeta: { msgId: 'Sensors' },
    children: [
      { key: 'corner', labelMeta: { msgId: 'Corner' } },
      { key: 'diagnosticError', labelMeta: { msgId: 'Diagnostic Error' } },
      { key: 'doorOpen', labelMeta: { msgId: 'Door Open' } },
      { key: 'driverId', labelMeta: { msgId: 'Driver Id' } },
      { key: 'gearChange', labelMeta: { msgId: 'Gear Change' } },
      { key: 'handbrake', labelMeta: { msgId: 'Handbrake' } },
      { key: 'hazard', labelMeta: { msgId: 'Hazard' } },
      { key: 'horn', labelMeta: { msgId: 'Horn' } },
      { key: 'idle', labelMeta: { msgId: 'Idle' } },
      { key: 'indicators', labelMeta: { msgId: 'Indicators' } },
      { key: 'lights', labelMeta: { msgId: 'Lights' } },
      { key: 'motion', labelMeta: { msgId: 'Motion' } },
      { key: 'overRev', labelMeta: { msgId: 'Over Rev' } },
      { key: 'power', labelMeta: { msgId: 'Power' } },
      { key: 'privacy', labelMeta: { msgId: 'Privacy' } },
      { key: 'PTO', labelMeta: { msgId: 'PTO' } },
      { key: 'reversing', labelMeta: { msgId: 'Reversing' } },
      { key: 'roaming', labelMeta: { msgId: 'Roaming' } },
      { key: 'seatBelt', labelMeta: { msgId: 'Seat Belt' } },
      { key: 'speeding', labelMeta: { msgId: 'Speeding' } },
    ] satisfies Array<{ key: SensorsMapLayers; labelMeta: LayerLabelMeta }>,
  },
  geofences: {
    labelMeta: { msgId: 'Geofences' },
    children: [
      { key: 'geofenceLabels', labelMeta: { msgId: 'Labels' } },
      { key: 'systemGeofences', labelMeta: { msgId: 'System Geofences' } },
      { key: 'userGeofences', labelMeta: { msgId: 'User Geofences' } },
    ] satisfies ReadonlyArray<{ key: GeofencesMapLayers; labelMeta: LayerLabelMeta }>,
  },
  landmarks: {
    labelMeta: { msgId: 'Landmarks' },
    children: [
      {
        key: 'pointsOfInterestLabels',
        labelMeta: { msgId: 'Labels' },
      },
    ],
  },
  livePositions: {
    labelMeta: { msgId: 'Live Positions' },
    children: [
      { key: 'livePositionLabels', labelMeta: { msgId: 'Live Position Labels' } },
      { key: 'livePositionClusters', labelMeta: { msgId: 'Live Position Clusters' } },
      {
        key: 'livePositionShowWhileAVehicleIsSelected',
        labelMeta: { msgId: 'Live Position Show While A Vehicle Is Selected' },
      },
    ] satisfies ReadonlyArray<{
      key: LivePositionMapLayers
      labelMeta: LayerLabelMeta
    }>,
  },
  maps: {
    labelMeta: { msgId: 'Maps' },
    children: [
      { key: 'traffic', labelMeta: { msgId: 'Traffic' } },
      { key: 'transit', labelMeta: { msgId: 'Transit' } },
      { key: 'bicycle', labelMeta: { msgId: 'Bicycle' } },
      { key: 'alerts', labelMeta: { msgId: 'Alerts' } },
    ] satisfies ReadonlyArray<{ key: MapsMapLayers; labelMeta: LayerLabelMeta }>,
  },
  showTripLines: {
    labelMeta: { msgId: 'Show Trip Lines' },
    children: [],
  },
} as const satisfies MapLayerOptions

export type TimelineResetState = {
  multipleDaysTimeline: FetchMultipleDaysTimeline.Return | null
  selectedTrip: {
    id: VehicleTripId
    events: Array<TimelineEventWithRoadSpeed>
    isTripAlreadySelected?: boolean
  } | null
  selectedCompactTrip: {
    id: Array<VehicleTripId>
    events: Array<Array<TimelineEventWithRoadSpeed>>
  } | null
  timelineEventsUI: (
    | FetchTimelineEventsUI.ReturnWithTrips
    | {
        // Empty state
        timeline: FetchTimelineEventsUI.ReturnWithTrips['timeline']
        trips: FetchTimelineEventsUI.ReturnWithTrips['trips']
        dayStart: undefined
        dayEnd: undefined
        totals: undefined
        alerts: undefined
      }
  ) & {
    reset: FetchTimelineEventsUI.ReturnWithTrips['trips']
  }
  sensorsByNumber: FetchTimelineEventsRawResolved['sensorsByNumber']
  timelineEventsRaw: FetchTimelineEventsRawResolved['events']
  timelineTrips: Array<Array<TimelineEventWithRoadSpeed>>
  timelineTripsReset: Array<Array<TimelineEventWithRoadSpeed>>
}

type State = TimelineResetState & {
  isTimelineBarUILoading: boolean
  isTimelineEventsLoading: boolean
  trips: Record<string, any>
  isFetchingStatistics: boolean
  xAxisChartType: 'time' | 'distance'
  tableOrderMode: {
    modes: ['recent', 'older']
    current: 'recent' | 'older'
  }
  // NOTE: save the static part of layer groups as initial values
  layerGroups: typeof originalLayerGroups
}

export type TimelineReduxState = State

export const TRIP_STATISTICS = {
  Speed: {
    buttonName: 'Speed',
    chartName: 'speed',
    XAxisName: 'Speed (Km/h)',
    YAxisName: 'Speed Statistics %',
  },
  'Throttle Position': {
    buttonName: 'Throttle Position',
    chartName: 'throttle',
    XAxisName: 'Throttle Position (%)',
    YAxisName: 'Throttle Statistics %',
  },
  RPM: {
    buttonName: 'RPM',
    chartName: 'RPM',
    XAxisName: 'RPM',
    YAxisName: 'RPM Statistics %',
  },
} as const

/**
 * TODO: Add fix for FABF-776
 * Refer to commit 58090da
 */
export const tableColumnsVisibility = {
  Date: true,
  Time: true,
  Actions: false,
  Vision: false,
  Status: true,
  Events: true,
  Speed: true,
  'Road Speed': true,
  RPM: true,
  // Fuel: false,
  'Fuel Used': false,
  Location: true,
  Driver: true,
  'GPS Signal': true,
  Odometer: true,
  Geofence: true,
  Latitude: false,
  Longitude: false,
  Altitude: false,
  'X Accel.': false,
  'Y Accel.': false,
  'Z Accel.': false,
  'Temp 1': false,
  'Temp 2': false,
  'Temp 3': false,
  'Temp 4': false,
  Clock: false,
  'Unit Temp': true,
  'Water Temp': true,
  'Oil Temp': true,
  'Oil Pressure': true,
  'Manifold Pressure': true,
  'Clock (Minutes)': true,
  'Clock (Raw)': true,
  'Battery Levels': true,
}

const timelineResetState: TimelineResetState = {
  multipleDaysTimeline: null,
  selectedTrip: null,
  selectedCompactTrip: null,
  timelineEventsUI: {
    totals: undefined,
    alerts: undefined,
    timeline: [],
    trips: [], // Contains trip UI data for timeline tables including notes
    reset: [], // Reset for show/hide 0km
    dayStart: undefined,
    dayEnd: undefined,
  },
  sensorsByNumber: [],
  timelineEventsRaw: [],
  timelineTrips: [],
  timelineTripsReset: [],
}

// Reducers
export const initialState: State = {
  ...timelineResetState,
  isTimelineBarUILoading: false,
  isTimelineEventsLoading: false,
  trips: {},
  isFetchingStatistics: true,
  xAxisChartType: 'time',
  tableOrderMode: {
    modes: ['recent', 'older'],
    current: 'recent',
  },
  layerGroups: originalLayerGroups,
}

const timeline = (state = initialState, action: FixMeAny): State => {
  if (receiveTimelineEventsRaw.match(action)) {
    const { payload } = action
    return {
      ...state,
      ...payload,
    }
  } else if (onChartXVariantTabGroupClick.match(action)) {
    return { ...state, xAxisChartType: action.payload }
  } else if (changedActivityDateRange.match(action)) {
    // NOTE: In the booking tab, we're using react-query, unlike the other tabs that use redux
    if (action.payload.tabId === 'booking') {
      return {
        ...state,
        isTimelineBarUILoading: false,
      }
    } else {
      return {
        ...state,
        isTimelineBarUILoading: true,
      }
    }
  } else if (fetchMultipleDaysTimeline.processing.match(action)) {
    return {
      ...state,
      isTimelineBarUILoading: true,
    }
  } else if (fetchMultipleDaysTimeline.succeeded.match(action)) {
    return {
      ...state,
      multipleDaysTimeline: action.payload,
      isTimelineEventsLoading: false,
      isTimelineBarUILoading: false,
    }
  } else if (fetchMultipleDaysTimeline.failed.match(action)) {
    return {
      ...state,
      multipleDaysTimeline: null,
      isTimelineEventsLoading: false,
      isTimelineBarUILoading: false,
    }
  } else if (
    unmountedDetailsPanel.match(action) ||
    unmountedLiveVisionRightPanel.match(action)
  ) {
    return {
      ...state,
      isTimelineEventsLoading: false,
      isTimelineBarUILoading: false,
    }
  } else if (selectTimelineTrip.match(action)) {
    const { payload } = action
    if (payload.trip && payload.trip.selectionType === 'multiple') {
      const isTripAlreadySelect = payload.trip.isTripAlreadySelected

      const calculatedIds = isTripAlreadySelect
        ? compact(
            pull(
              [
                state.selectedTrip?.id,
                ...(
                  state.selectedCompactTrip as ExcludeStrict<
                    TimelineResetState['selectedCompactTrip'],
                    null
                  >
                ).id,
              ],
              payload.trip.id,
            ),
          )
        : compact([
            state.selectedTrip?.id,
            ...(state.selectedCompactTrip?.id || []),
            payload.trip.id,
          ])

      const calculatedEvents = payload.trip.events

      return {
        ...state,
        selectedTrip: null,
        selectedCompactTrip: {
          id: calculatedIds,
          events: calculatedEvents,
        },
      }
    }

    return {
      ...state,
      selectedTrip: payload.trip && {
        id: payload.trip.id,
        events: payload.trip.events || [],
      },
      selectedCompactTrip: null,
    }
  } else if (receiveTimelineEventsUi.match(action)) {
    if (action.payload.trips === undefined) {
      return {
        ...state,
        isTimelineBarUILoading: false,
      }
    }

    return {
      ...state,
      timelineEventsUI: { ...action.payload },
      isTimelineBarUILoading: false,
    }
  } else if (setSelectedCompactTrip.match(action)) {
    return {
      ...state,
      selectedCompactTrip: action.payload,
    }
  } else if (batchReduxStateUpdatesFromSaga.match(action)) {
    return action.payload.timelineState
      ? { ...state, ...action.payload.timelineState }
      : state
  }

  switch (action.type) {
    case UPDATE_TIMELINE_EVENTS_LOADING: {
      return {
        ...state,
        isTimelineEventsLoading: action.payload,
      }
    }
    case UPDATE_TIMELINE_BAR_UI_LOADING: {
      return {
        ...state,
        isTimelineBarUILoading: action.payload,
      }
    }
    case SELECT_VEHICLE_TRIP_SUMMARY: {
      return {
        ...state,
        isTimelineBarUILoading: true,
      }
    }
    case CANCEL_FETCH_TIMELINE_DATA: {
      return {
        ...state,
        isTimelineEventsLoading: false,
        isTimelineBarUILoading: false,
        isFetchingStatistics: false,
        ...timelineResetState,
      }
    }
    case SET_FILTERED_TIMELINE_TRIPS_EVENTS: {
      return {
        ...state,
        timelineEventsUI: {
          ...state.timelineEventsUI,
          trips: action.payload.trips,
        },
        timelineTrips: action.payload.timelineTrips,
      }
    }
    case CLOSE_DETAILS_PANEL:
    case CLEAR_TIMELINE: {
      return {
        ...state,
        ...timelineResetState,
      }
    }
    case FETCH_TRIP_STATISTICS_START: {
      return {
        ...state,
        isFetchingStatistics: true,
      }
    }
    case FETCH_TRIP_STATISTICS_SUCCESS: {
      return {
        ...state,
        trips: {
          ...state.trips,
          [action.payload.tripId]: {
            speed: action.payload.speedData,
            throttle: action.payload.throttleData,
            RPM: action.payload.RPMData,
          },
        },
      }
    }
    case FETCH_TRIP_STATISTICS_FINISH: {
      return {
        ...state,
        isFetchingStatistics: false,
      }
    }
    case SET_TIMELINE_TRIPS_TABLE_ORDER_MODE: {
      return {
        ...state,
        tableOrderMode: {
          ...state.tableOrderMode,
          current: action.payload,
        },
      }
    }
    default: {
      return state
    }
  }
}

/**
 * ***********
 * Actions
 * ***********
 */

export const updateTripDetails = createAction<{
  vehicleId: string
  trip: FetchTimelineEventsUI.ParsedApiTrip
}>('UPDATE_VEHICLE_TRIPS')

export const setTimelineTripsTablesViewModePreference = createAction<
  UserPreferences['tripsTablesPreferences']['selectedViewMode']
>('SET_TIMELINE_TRIPS_TABLES_VIEW_MODE_PREFERENCE')

export const setTimelineTripsTablesVisibilityPreferences = (
  tripName: keyof UserPreferences['tripsTablesPreferences']['visibility'],
  checked: boolean,
) => {
  const payload: Partial<UserPreferences['tripsTablesPreferences']['visibility']> = {
    [tripName]: checked,
  }

  return {
    type: SET_TIMELINE_TRIPS_VISIBILITY_PREFERENCES,
    payload,
  }
}

export const setTimelineTripsTableOrderMode = (
  orderMode: State['tableOrderMode']['current'],
) => ({
  type: SET_TIMELINE_TRIPS_TABLE_ORDER_MODE,
  payload: orderMode,
})

export const deleteVehicleTripDetailsAction = createAction<{
  vehicleId: VehicleId
  trip: FetchTimelineEventsUI.ParsedApiTrip
}>(DELETE_VEHICLE_TRIP_DETAILS)

export const requestSVRPoll = (vehicleId: VehicleId, tripStartTime: Date) => ({
  type: REQUEST_SVR_POLL,
  payload: { vehicleId, tripStartTime },
})

// For fetching timeline data (new APIs)
export const fetchApiTimelineData = createAction<{
  vehicleId: VehicleId
  startTime: Date
  endTime: Date
  isSingleTrip?: boolean
}>('FETCH_TIMELINE_DATA')

// This will clear the timeline data on unmount
export const clearTimeline = () => ({
  type: CLEAR_TIMELINE,
})

export const showTripStatistics = (tripId: FixMeAny) => ({
  type: SHOW_TRIP_STATISTICS,
  payload: {
    tripId,
  },
})

/**
 * ***********
 * Selectors
 * ***********
 */
export const getIsFetchingStatistics = (state: AppState) =>
  state.timeline.isFetchingStatistics

export const getIsTimelineBarUILoading = (state: AppState) =>
  state.timeline.isTimelineBarUILoading

export const getIsTimelineEventsLoading = (state: AppState) =>
  state.timeline.isTimelineEventsLoading

export const getTimelineXAxisChartType = (state: AppState) =>
  state.timeline.xAxisChartType

export const getIsTimelineChartDataLoadingState = createSelectorWithStrictMode(
  getIsLoadingSummaryTrips,
  getIsTimelineBarUILoading,
  getIsTimelineEventsLoading,
  (isSummaryTripsLoading, isTimelineBarUILoading, isTimelineEventsLoading) =>
    isSummaryTripsLoading || isTimelineBarUILoading || isTimelineEventsLoading,
)

export const getTimelineEventsRaw = (state: AppState) =>
  state.timeline.timelineEventsRaw

export const getSelectedTrip = createSelectorWithStrictMode(
  (state: AppState) => state.timeline.selectedTrip,
  (state: AppState) => state.timeline.selectedCompactTrip,
  (selectedTrip, selectedCompactTrip) => {
    if (selectedCompactTrip) {
      return {
        id: selectedCompactTrip.id,
        events: selectedCompactTrip.events.map((tripEvents) =>
          tripEvents.filter((event) => event.lat !== null && event.lng !== null),
        ),
      }
    }

    if (selectedTrip === null) {
      return selectedTrip
    }

    return {
      events: selectedTrip.events.filter(
        (event) => event.lat !== null && event.lng !== null,
      ),
      id: selectedTrip.id,
    }
  },
)

export const getSelectedTripId = createSelectorWithStrictMode(
  getSelectedTrip,
  (selectedTrip) => selectedTrip?.id ?? null,
)

export const getLayerGroups = (state: AppState) => state.timeline.layerGroups

// Stable reference to not break memoization of selectors dependent on this
const emptyArray = [] as const
export const getTimelineSensorsByNumber = (
  state: AppState,
): ReadonlyArray<SharedTimelineEventsTypes.ApiSensor> =>
  getActivityActiveTab(state) === 'daily'
    ? state.timeline.sensorsByNumber
    : (state.timeline.multipleDaysTimeline?.sensorsByNumber ?? emptyArray)

export const generateSortedSensorList = (sensorsByNumber: State['sensorsByNumber']) =>
  Object.values(sensorsByNumber).sort((a, b) => a.name.localeCompare(b.name))

export const getTimelineSortedSensorList = createSelectorWithStrictMode(
  getTimelineSensorsByNumber,
  (sensorsByNumber) => generateSortedSensorList(sensorsByNumber),
)
// Order is important -> Colors closer to index 0 are meant to be used in the majority of the time
export const chartSeriesColors = [
  '#000000',
  '#F47735',
  '#5CAE60',
  '#FFBD4D',
  '#CE5239',
  '#B5D55E',
  '#53B8C6',
  '#D66087',
  '#5099CE',
  '#A870BD',
  '#999999',
  '#963825',
  '#85C288',
  '#D4B8DE',
  '#F79865',
  '#CD8100',
  '#266A73',
  '#E089A6',
  '#C8E086',
  '#A8CCE7',
]

export const getTimelineSensorWithUniqueColorData = createSelectorWithStrictMode(
  getTimelineSortedSensorList,
  (sortedSensorList) =>
    sortedSensorList.map((sensor, i) => {
      const color =
        i < chartSeriesColors.length ? chartSeriesColors[i] : chartSeriesColors[0] // default,

      return { ...sensor, color }
    }),
)

export const getTimelineTrips = (state: AppState) => state.timeline.timelineTrips

export const getTimelineTripsReset = (state: AppState) => ({
  timelineTrips: state.timeline.timelineTripsReset,
  timelineEventUI: state.timeline.timelineEventsUI.reset,
})

export const getTimelineTripsMarkerData = createSelectorWithStrictMode(
  (state: AppState) => getTimelineEventsUI(state),
  (state: AppState) => getTimelineTrips(state),
  (state: AppState) => getSelectedTripId(state),
  (state: AppState) => getSettings_UNSAFE(state),
  ({ trips }, tripEvents, selectedTripId, { distanceInMiles }) =>
    trips.reduce<TripMarkerData>((acc, trip, index) => {
      const [formattedDistanceValue, distanceUnit] = formatDistance(
        trip.totalDistance ?? 0,
        {
          round: true,
          units: true,
          isMiles: distanceInMiles,
        },
      ).split(' ')

      if (
        selectedTripId === null ||
        selectedTripId === trip.tripId ||
        (isArray(selectedTripId) && selectedTripId.includes(trip.tripId))
      ) {
        acc.push({
          ...trip,
          formattedTime: {
            start: DateTime.fromJSDate(trip.startTime).toFormat('t'),
            end: DateTime.fromJSDate(trip.endTime).toFormat('t'),
          },
          events: tripEvents[index]
            ? filterOutInvalidCoordinates(tripEvents[index])
            : [],
          totals: [
            {
              label: ctIntl.formatMessage({ id: 'Minutes' }),
              value: Math.round((trip.endTimeMS - trip.startTimeMS) / 1000 / 60),
            },
            {
              label: distanceUnit,
              value: formattedDistanceValue,
            },
          ],
        })
      }

      return acc
    }, []),
)

export const getTimelineEventsUI = (state: AppState) => state.timeline.timelineEventsUI

export const getTimelineEventsUIDateRange = createSelectorWithStrictMode(
  getTimelineEventsUI,
  (timelineEventsUI) => ({
    timelineStartTime: new Date(timelineEventsUI.dayStart as FixMeAny).getTime(),
    timelineEndTime: new Date(timelineEventsUI.dayEnd as FixMeAny).getTime(),
  }),
)

export const getSelectedTimeline = () =>
  JSON.parse(localStorage.getItem('selectedTimeline') ?? '') || {}

export const parsedTripsDataSelector = (state: AppState, tripId: FixMeAny) => {
  if (isEmpty(state.timeline.trips) && isEmpty(state.timeline.trips[tripId])) {
    // No data inside
    return {}
  }

  const tripData = state.timeline.trips[tripId]
  const resultObject: Record<string, any> = {}

  const speedData = tripData.speed as Record<string, any>
  // Handle speed data
  if (isEmpty(speedData)) {
    resultObject.speed = []
  } else {
    const sum = Object.values(speedData).reduce((result, value) => {
      if (isEmpty(value)) return result

      return result + Number.parseFloat(value)
    }, 0)

    let i = 0
    const parsedData = Object.values(speedData).reduce((result, value) => {
      if (isEmpty(value)) return result

      return [
        ...result,
        {
          speed: (10 * i++).toString(),
          percentage: round((Number.parseFloat(value) / sum) * 100, 1),
        },
      ]
    }, [])

    resultObject.speed = parsedData
  }

  const throttleData = tripData.throttle as Record<string, any>
  // Handle throttle data
  if (isEmpty(throttleData)) {
    resultObject.throttle = []
  } else {
    const sum = Object.values(throttleData).reduce((result, value) => {
      if (isEmpty(value)) return result

      return result + Number.parseFloat(value)
    }, 0)

    let i = 0
    const parsedData = Object.values(throttleData).reduce((result, value) => {
      if (isEmpty(value)) return result

      return [
        ...result,
        {
          throttle: (5 * i++).toString(),
          percentage: round((Number.parseFloat(value) / sum) * 100, 1),
        },
      ]
    }, [])

    resultObject.throttle = parsedData
  }

  const rpmData = tripData.RPM as Record<string, any>
  // Handle RPM data
  if (isEmpty(rpmData)) {
    resultObject.RPM = []
  } else {
    const sum = Object.values(rpmData).reduce((result, value) => {
      if (isEmpty(value)) return result

      return result + Number.parseFloat(value)
    }, 0)

    let i = 0
    const parsedData = Object.values(rpmData).reduce((result, value) => {
      if (isEmpty(value) || Number.parseInt(value, 10) === 0) return result

      return [
        ...result,
        {
          RPM: (100 * i++).toString(),
          percentage: round((Number.parseFloat(value) / sum) * 100, 1),
        },
      ]
    }, [])

    resultObject.RPM = parsedData
  }

  return resultObject
}

export const getTimelineTableOrderMode = (state: AppState) =>
  state.timeline.tableOrderMode

export const getIsTimelineTableLoading = createSelectorWithStrictMode(
  getIsLoadingSummaryTrips,
  getIsTimelineBarUILoading,
  getIsTimelineEventsLoading,
  (isSummaryTripsLoading, isTimelineBarUILoading, isTimelineEventsLoading) =>
    isSummaryTripsLoading || isTimelineBarUILoading || isTimelineEventsLoading,
)

export const getMultipleDaysTimeline = (state: AppState) =>
  state.timeline.multipleDaysTimeline

export const getMultipleDaysTimelineWithMappedEvents = createSelectorWithStrictMode(
  getMultipleDaysTimeline,
  (multipleDaysTimeline) => {
    if (multipleDaysTimeline === null) {
      return null
    }

    const { events, ...rest } = multipleDaysTimeline

    return { ...rest, events: events.allIds.map((id) => events.byId[id]) }
  },
)

export const getTimelineTripsSensors = createSelectorWithStrictMode(
  (state: AppState) => state.timeline.timelineTrips,
  getTimelineSortedSensorList,
  (state: AppState) => state.user.preferences,
  (state: AppState) => state.map.type,
  (timelineTrips, sortedSensorsList, preferences, mapType) => {
    if (mapType !== 'fleet' && mapType !== 'vehicles') {
      return {
        allSensors: [],
        visibleSensors: [],
      }
    }

    const sortedSensorColumns = generateSensorsColumns(
      timelineTrips.flatMap((x) => x),
      sortedSensorsList,
    )

    const hasEVSensors = sortedSensorsList.some(
      (sensor: SharedTimelineEventsTypes.ApiSensor) => sensor.sensorTypeId === '87',
    )

    const hasFuelSensors = sortedSensorsList.some(
      (sensor: SharedTimelineEventsTypes.ApiSensor) =>
        getSensorTypeIdEnumType(sensor.sensorTypeId) === 'fuel',
    )

    const defaultSensorFields: Record<string, any> = {}
    const storedPreferences = !isNil(preferences[tableId.default])
      ? preferences[tableId.default]
      : preferences[tableId.basic]

    for (const col of sortedSensorColumns) {
      const storedVisibleState = !isNil(storedPreferences)
        ? storedPreferences[col.Header as string]
        : undefined
      defaultSensorFields[col.Header as string] = !isNil(storedVisibleState)
        ? storedVisibleState
        : true
    }

    return {
      allSensors: sortedSensorColumns,
      defaultSensorFields,
      hasEVSensors,
      hasFuelSensors,
    }
  },
)

export const getReTimelineTripsSensors = createSelectorWithStrictMode(
  (state: AppState) => state.timeline.timelineTrips,
  getTimelineSortedSensorList,
  (state: AppState) => state.user.preferences,
  (state: AppState) => state.map.type,
  (
    timelineTrips,
    sortedSensorsList,
    preferences,
    mapType,
  ): {
    allSensors: ReadonlyArray<GridColDef<TimelineEventWithRoadSpeed>>
    defaultSensorFields: Record<string, any>
    hasEVSensors: boolean
    hasFuelSensors: boolean
  } => {
    if (mapType !== 'fleet' && mapType !== 'vehicles') {
      return {
        allSensors: [],
        defaultSensorFields: {},
        hasEVSensors: false,
        hasFuelSensors: false,
      }
    }

    const sortedSensorColumns = reGenerateSensorsColumns(
      timelineTrips.flatMap((x) => x),
      sortedSensorsList,
    )

    const hasEVSensors = sortedSensorsList.some(
      (sensor: SharedTimelineEventsTypes.ApiSensor) => sensor.sensorTypeId === '87',
    )

    const hasFuelSensors = sortedSensorsList.some(
      (sensor: SharedTimelineEventsTypes.ApiSensor) =>
        getSensorTypeIdEnumType(sensor.sensorTypeId) === 'fuel',
    )

    const defaultSensorFields: Record<string, any> = {}
    const storedPreferences = !isNil(preferences[tableId.default])
      ? preferences[tableId.default]
      : preferences[tableId.basic]

    for (const col of sortedSensorColumns) {
      const storedVisibleState = !isNil(storedPreferences)
        ? storedPreferences[col.field as string]
        : undefined
      defaultSensorFields[col.field as string] = !isNil(storedVisibleState)
        ? storedVisibleState
        : true
    }

    return {
      allSensors: sortedSensorColumns,
      defaultSensorFields,
      hasEVSensors,
      hasFuelSensors,
    }
  },
)

export const getMultipleDaysChartData = createSelectorWithStrictMode(
  getMultipleDaysTimelineWithMappedEvents,
  getTimelineXAxisChartType,
  (multipleDaysTimelineWithMappedEvents, xAxisChartType) =>
    multipleDaysTimelineWithMappedEvents === null
      ? []
      : chartNormalizer({
          type: 'multipleDays',
          events: multipleDaysTimelineWithMappedEvents.events,
          xAxisChartType,
        }),
)

export const getSingleDayChartData = createSelectorWithStrictMode(
  getTimelineEventsRaw,
  getTimelineXAxisChartType,
  (state: AppState) => state.timeline.sensorsByNumber,
  (eventsRaw, xAxisChartType, sensorsByNumber) => {
    if (isEmpty(sensorsByNumber)) {
      return []
    }

    return chartNormalizer({
      type: 'singleDay',
      events: eventsRaw,
      xAxisChartType,
    })
  },
)

export const getActivityActiveTabTimelineChartData = createSelectorWithStrictMode(
  getActivityActiveTab,
  getSingleDayChartData,
  getMultipleDaysChartData,
  (activeTab, singleDayChartData, multipleDaysChartData) => {
    switch (activeTab) {
      case 'daily': {
        return { type: 'daily', data: singleDayChartData } as const
      }
      case 'multipleDays': {
        return { type: 'multipleDays', data: multipleDaysChartData } as const
      }
      case 'booking': {
        return { type: 'booking', data: undefined } as const
      }
    }
  },
)

type TimelineEventRaw = ReturnType<typeof getTimelineEventsRaw>[number]
type AllTripsTimelineTableDataElement = TimelineEventRaw & {
  driverName: DriverName | null
}
/* IMPORTANT: using explicit returns to __prevent bugs__ */
export const getTimelineTableDataFilteredByCategory = createSelectorWithStrictMode(
  (state: AppState) => getTimelineTablesActiveTab(state), // Inline to avoid circular dependency
  getTimelineEventsRaw,
  (state: AppState) => state.timeline.timelineEventsUI,
  (
    timelineTablesActiveTab,
    events,
    timelineEventsUI,
  ): Array<TimelineEventRaw> | Array<AllTripsTimelineTableDataElement> =>
    timelineTablesActiveTab !== 'all-trips'
      ? events
          .filter((event) => event.eventCategory === timelineTablesActiveTab)
          .map((event): AllTripsTimelineTableDataElement => {
            const tripWithDateInterval = timelineEventsUI.trips.find(
              (trip) => event.time >= trip.startTimeMS && event.time <= trip.endTimeMS,
            )
            return {
              ...event,
              driverName: tripWithDateInterval?.driverName ?? null,
            }
          })
      : events,
)
export const reGetTimelineTableDataFilteredByCategory = createSelectorWithStrictMode(
  (state: AppState) => getTimelineTablesActiveTab(state), // Inline to avoid circular dependency
  getTimelineEventsRaw,
  (state: AppState) => state.timeline.timelineEventsUI,
  (
    timelineTablesActiveTab,
    events,
    timelineEventsUI,
    //This needs to be checked for, the columns regenerator must be specific and not generic
  ): Array<
    | (TimelineEventRaw & { allowRoadSpeed: boolean | undefined })
    | (AllTripsTimelineTableDataElement & { allowRoadSpeed: boolean | undefined })
  > =>
    timelineTablesActiveTab !== 'all-trips'
      ? events
          .filter((event) =>
            timelineTablesActiveTab === 'Road Speed'
              ? // some events can have isRoadSpeeding=true and evenCategory != 'Road Speed'
                event.isRoadSpeeding
              : event.eventCategory === timelineTablesActiveTab,
          )
          .map(
            (
              event,
            ): AllTripsTimelineTableDataElement & {
              allowRoadSpeed: boolean | undefined
            } => {
              const tripWithDateInterval = timelineEventsUI.trips.find(
                (trip) =>
                  event.time >= trip.startTimeMS && event.time <= trip.endTimeMS,
              )
              return {
                ...event,
                driverName: tripWithDateInterval?.driverName ?? {
                  status: DriverNameVisibilityStatus.PUBLIC,
                  name: null,
                },
                allowRoadSpeed: undefined,
              }
            },
          )
      : events.map((event) => ({
          ...event,
          allowRoadSpeed: undefined,
        })),
)

export const getTimelineEventsByActivityAndMapType = createSelectorWithStrictMode(
  getActivityActiveTab,
  getTimelineEventsRaw,
  getMultipleDaysTimelineWithMappedEvents,
  (state: AppState) => getMapType(state), // Inline to avoid circular dependency
  (activeTab, singleDayEvents, multipleDaysDataWithMappedEvents, mapType) => {
    if (activeTab === 'daily' || mapType !== 'fleet') {
      return {
        type: 'daily',
        events: singleDayEvents,
      } as const
    } else {
      return {
        type: 'multipleDays',
        events: multipleDaysDataWithMappedEvents?.events ?? [],
      } as const
    }
  },
)

export const getNormalizedTimelineSingleDayEvents = createSelectorWithStrictMode(
  getTimelineEventsRaw,
  (rawEvents) =>
    rawEvents.map((event) => ({
      ...event,
      coords: isValidGPSData(event.lat, event.lng)
        ? {
            lat: event.lat,
            lng: event.lng,
          }
        : null,
    })),
)

export const getTimelineSliderDataFilteredByActivity = createSelectorWithStrictMode(
  getActivityActiveTab,
  getNormalizedTimelineSingleDayEvents,
  getMultipleDaysTimelineWithMappedEvents,
  getTimelineEventsUI,
  (
    activityActiveTab,
    singleDayEvents,
    multipleDaysDataWithMappedEvents,
    timelineEventsUI,
  ): {
    events: Array<TimelineActivityEvent>
    timelineBarUI: Array<BasicEventUI>
    timeRange: {
      startDateTime: DateTime
      endDateTime: DateTime
    }
  } => {
    if (activityActiveTab === 'daily') {
      return {
        events: singleDayEvents,
        timelineBarUI: timelineEventsUI.timeline,
        timeRange: {
          startDateTime: DateTime.fromJSDate(timelineEventsUI.dayStart ?? new Date()),
          endDateTime: DateTime.fromJSDate(timelineEventsUI.dayEnd ?? new Date()),
        },
      }
    } else {
      return {
        events: multipleDaysDataWithMappedEvents?.events ?? [],
        timelineBarUI: multipleDaysDataWithMappedEvents?.timeline ?? [],
        timeRange: {
          startDateTime: DateTime.fromJSDate(
            multipleDaysDataWithMappedEvents?.dayStart ?? new Date(),
          ),
          endDateTime: DateTime.fromJSDate(
            multipleDaysDataWithMappedEvents?.dayEnd ?? new Date(),
          ),
        },
      }
    }
  },
)

export const getTimelineTotals = createSelectorWithStrictMode(
  getActivityActiveTab,
  getMultipleDaysTimeline,
  getTimelineEventsUI,
  (activityActiveTab, multipleDaysData, timelineEventsUI) => {
    if (activityActiveTab === 'daily') {
      return timelineEventsUI.totals
    } else {
      return multipleDaysData?.totals
    }
  },
)

export const getMultipleDaysPathComponentsFilteredEventsCoords =
  createSelectorWithStrictMode(
    getFocusedVehicle,
    getTimelineEventsByActivityAndMapType,
    (focusedVehicle, timelineEventsByActivity) => {
      if (timelineEventsByActivity.type === 'multipleDays' && !isNil(focusedVehicle)) {
        return timelineEventsByActivity.events.reduce<
          Array<ExcludeStrict<BasicEvent['coords'], null>>
        >((acc, event) => {
          // Downsample further based on zoom, but always show harsh events
          if (
            event.coords !== null &&
            isValidGPSData(event.coords.lat, event.coords.lng)
          ) {
            acc.push(event.coords)
          }

          return acc
        }, [])
      }

      return []
    },
  )

export const getActiveTimelineEventByActivity = createSelectorWithStrictMode(
  getTimelineEventsByActivityAndMapType,
  (state: AppState) => state.map.activeEventIndex,
  (eventsByActivityData, activeEventIndex) => {
    let activeEvent: (typeof eventsByActivityData.events)[number] | null = null

    if (eventsByActivityData.type === 'daily') {
      const singleDayEvents = eventsByActivityData.events

      if (singleDayEvents.length > 0) {
        if (!isNil(activeEventIndex) && activeEventIndex < singleDayEvents.length) {
          activeEvent = singleDayEvents[activeEventIndex]
        } else {
          activeEvent = singleDayEvents[singleDayEvents.length - 1]
        }
      }

      return {
        type: 'daily',
        data: activeEvent,
      } as const
    } else {
      const multipleDaysEvents = eventsByActivityData.events

      if (multipleDaysEvents.length > 0) {
        if (!isNil(activeEventIndex) && activeEventIndex < multipleDaysEvents.length) {
          activeEvent = multipleDaysEvents[activeEventIndex]
        } else {
          activeEvent = multipleDaysEvents[multipleDaysEvents.length - 1]
        }
      }

      return {
        type: 'multipleDays',
        data: activeEvent,
      } as const
    }
  },
)

export default timeline

export const getTimelineStateFromSaga = (state: AppState) => state.timeline
