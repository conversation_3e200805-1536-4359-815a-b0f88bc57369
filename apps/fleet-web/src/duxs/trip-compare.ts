import { createAction, createSelector } from '@reduxjs/toolkit'

import { transformAt, removeAt } from 'src/util-functions/functional-utils'
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'
import {
  clickedLeftPanelCompareTripsButton,
  clickedLeftPanelCompareTripsBackButton,
} from 'src/modules/map-view/actions'
import type { FetchVehicleCompareTripsResolved } from 'api/vehicles'
import type { getActiveVehicleOptions } from './shared'
import type { ReadonlyDeep } from 'type-fest'
import { createSelectorWithStrictMode } from 'src/redux-utils'

export const FETCH_COMPARED_TRIPS = 'FETCH_COMPARED_TRIPS'
export const ADD_COMPARED_TRIP = 'ADD_COMPARED_TRIP'
export const UPDATE_COMPARED_TRIP = 'UPDATE_COMPARED_TRIP'
export const REMOVE_COMPARED_TRIP = 'REMOVE_COMPARED_TRIP'
export const REMOVE_ALL_COMPARED_TRIPS = 'REMOVE_ALL_COMPARED_TRIPS'
export const SET_HOVERED_COMPARED_TRIP_INDEX = 'SET_HOVERED_COMPARE_TRIP_INDEX'
export const SET_SELECTED_COMPARED_TRIP_INDEX = 'SET_SELECTED_COMPARED_TRIP_INDEX'

export type Trip = {
  key: string
  events: FetchVehicleCompareTripsResolved[number]['events']
  color: string
  dateRange: { to: Date; from: Date }
  vehicle: ReturnType<typeof getActiveVehicleOptions>[number]
  serverDateRange?: { from: string; to: string }
  tripData?: {
    events: FetchVehicleCompareTripsResolved[number]['timeline']
    stats: FetchVehicleCompareTripsResolved[number]['stats']
    startTime: number
    endTime: number
  }
}

export const receiveComparedTrips = createAction<{
  trips: Array<Trip>
}>('RECEIVE_COMPARED_TRIPS')

type State = ReadonlyDeep<{
  selectedIndex: number | null
  loading: boolean
  trips: Array<Record<string, FixMeAny> & { events: Array<Record<string, FixMeAny>> }>
  hoveredIndex: number | null | undefined
}>

const initialState: State = {
  selectedIndex: null,
  loading: false,
  trips: [],
  hoveredIndex: undefined,
}

export default function reducer(state = initialState, action: FixMeAny): State {
  if (clickedLeftPanelCompareTripsButton.match(action)) {
    return {
      ...state,
      selectedIndex: null,
      trips: [],
    }
  } else if (clickedLeftPanelCompareTripsBackButton.match(action)) {
    return initialState
  } else if (receiveComparedTrips.match(action)) {
    return {
      ...state,
      selectedIndex: 0,
      trips: action.payload.trips,
      loading: false,
    }
  }

  switch (action.type) {
    case FETCH_COMPARED_TRIPS: {
      return {
        ...state,
        loading: true,
      }
    }

    case ADD_COMPARED_TRIP: {
      return {
        ...state,
        trips: [...state.trips, action.payload.trip],
      }
    }
    case UPDATE_COMPARED_TRIP: {
      return {
        ...state,
        trips: transformAt(state.trips, action.payload.index, action.payload.updater),
      }
    }
    case REMOVE_COMPARED_TRIP: {
      return {
        ...state,
        trips: removeAt(state.trips, action.payload.index),
      }
    }
    case REMOVE_ALL_COMPARED_TRIPS: {
      return {
        ...state,
        trips: [],
      }
    }
    case SET_SELECTED_COMPARED_TRIP_INDEX: {
      return {
        ...state,
        selectedIndex: action.payload.index,
      }
    }
    case SET_HOVERED_COMPARED_TRIP_INDEX: {
      return {
        ...state,
        hoveredIndex: action.payload.index,
      }
    }
    default: {
      return state
    }
  }
}

export function fetchComparedTrips(trips: Array<Trip>) {
  return {
    type: FETCH_COMPARED_TRIPS,
    payload: { trips },
  }
}

export function addComparedTrip(trip: Trip) {
  return {
    type: ADD_COMPARED_TRIP,
    payload: { trip },
  }
}

export function updateComparedTrip(index: FixMeAny, updater: FixMeAny) {
  return {
    type: UPDATE_COMPARED_TRIP,
    payload: { index, updater },
  }
}

export function removeComparedTrip(index: FixMeAny) {
  return {
    type: REMOVE_COMPARED_TRIP,
    payload: { index },
  }
}

export function removeAllComparedTrips() {
  return {
    type: REMOVE_ALL_COMPARED_TRIPS,
  }
}

export function setHoveredComparedTripIndex(index: number | null) {
  return {
    type: SET_HOVERED_COMPARED_TRIP_INDEX,
    payload: { index },
  }
}

export function setSelectedComparedTripIndex(index: FixMeAny) {
  return {
    type: SET_SELECTED_COMPARED_TRIP_INDEX,
    payload: { index },
  }
}

// Selectors
export const getIsLoading = (state: AppState) => state.tripCompare.loading
export const getComparedTrips = (state: AppState) =>
  state.tripCompare.trips as Array<Trip>
export const getHoveredComparedTripIndex = (state: AppState) =>
  state.tripCompare.hoveredIndex
export const getSelectedComparedTripIndex = (state: AppState) =>
  state.tripCompare.selectedIndex

export const getSelectedComparedTrip = createSelectorWithStrictMode(
  getComparedTrips,
  getSelectedComparedTripIndex,
  (trips, selectedIndex) => {
    if (selectedIndex === null) {
      return null
    }

    const trip = trips[selectedIndex]
    if (trip) {
      const eventsWithCoords = trip.events.map((event) => {
        if ('coords' in event) {
          return event as typeof event & {
            coords: {
              lat: number
              lng: number
            }
          }
        }

        return {
          ...event,
          coords: {
            lat: event.lat,
            lng: event.lng,
          },
        }
      })

      return {
        ...trip,
        events: eventsWithCoords,
      }
    }

    return trip
  },
)

export const getSelectedCompareTripActiveEvent = createSelector(
  getSelectedComparedTrip,
  (state: AppState) => state.map.activeEventIndex, // Avoid circular dependency
  (trip, index) => (trip && index !== null ? (trip.events[index] ?? null) : null),
)

export type SelectedCompareTripActiveEvent = ReturnType<
  typeof getSelectedCompareTripActiveEvent
>
