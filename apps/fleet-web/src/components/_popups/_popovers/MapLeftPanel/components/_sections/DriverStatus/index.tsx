import { Stack, styled, Tooltip, Typography } from '@karoo-ui/core'
import BadgeOutlinedIcon from '@mui/icons-material/BadgeOutlined'
import GroupIcon from '@mui/icons-material/Group'
import LocationOnIcon from '@mui/icons-material/LocationOn'
import PersonIcon from '@mui/icons-material/Person'
import PhoneAndroidOutlinedIcon from '@mui/icons-material/PhoneAndroidOutlined'

import type { FetchVehicles } from 'api/vehicles/types'
import TachographDriverActivityIcon from 'src/components/Icon/TachographDriverActivity'
import type {
  Driver,
  TachographDriverActivity,
} from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import { ctIntl } from 'src/util-components/ctIntl'

type Props = {
  status: TachographDriverActivity
  driverName: Pick<FetchVehicles.Vehicle['driver_name'], 'name' | 'status'>
  driverRole?: Driver['role']
  driverCardId?: Driver['cardId']
  isETachoDriver?: boolean
  streetAddress: string | React.ReactNode
  children: React.ReactNode
}

const DriverStatus = ({
  status,
  driverName,
  driverRole,
  driverCardId,
  isETachoDriver,
  streetAddress,
  children,
}: Props) => (
  <Stack
    gap={1}
    flex={1}
  >
    <Stack
      direction="row"
      alignItems="center"
      flex={1}
      gap={1}
      justifyContent="space-between"
    >
      <Tooltip
        title={ctIntl.formatMessage({
          id: 'Current Status',
        })}
      >
        <Stack
          direction="row"
          alignItems="center"
          gap={1}
        >
          <TachographDriverActivityIcon
            status={status}
            size="max"
          />
          <Typography variant="subtitle2">
            {ctIntl.formatMessage({
              id: `map.tachographDrivers.driverActivityStatus.${status}`,
            })}
          </Typography>
        </Stack>
      </Tooltip>

      {children}
    </Stack>
    <InfoWrap>
      <PersonIcon sx={{ fontSize: '20px', color: 'action.active' }} />
      <InfoText>
        {driverName.status === 'UNDISCLOSED'
          ? ctIntl.formatMessage({
              id: 'vehicle.driverName.undisclosed',
            })
          : (driverName.name ?? `(${ctIntl.formatMessage({ id: 'No driver' })})`)}
      </InfoText>
    </InfoWrap>
    {isETachoDriver ? (
      <InfoWrap>
        <PhoneAndroidOutlinedIcon sx={{ fontSize: '20px', color: 'action.active' }} />
        <InfoText>{ctIntl.formatMessage({ id: 'eTachoDriver' })}</InfoText>
      </InfoWrap>
    ) : (
      driverCardId !== undefined && (
        <InfoWrap>
          <BadgeOutlinedIcon sx={{ fontSize: '20px', color: 'action.active' }} />
          <InfoText>{driverCardId}</InfoText>
        </InfoWrap>
      )
    )}

    {driverRole === 'CODRIVER' && (
      <InfoWrap>
        <GroupIcon sx={{ fontSize: '20px', color: 'action.active' }} />
        <InfoText>
          {ctIntl.formatMessage({ id: 'map.tachographDrivers.coDriver' })}
        </InfoText>
      </InfoWrap>
    )}
    {streetAddress && (
      <InfoWrap>
        <LocationOnIcon sx={{ fontSize: '20px', color: 'action.active' }} />
        <InfoText>{streetAddress}</InfoText>
      </InfoWrap>
    )}
  </Stack>
)

export default DriverStatus

const InfoText = styled(Typography)({
  color: 'rgba(0, 0, 0, 0.87)',
  fontSize: '12px',
})

const InfoWrap = styled(Stack)(({ theme }) =>
  theme.unstable_sx({
    gap: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
  }),
)
