import { DateTime } from 'luxon'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import { PermissionLookup, type PermissionLookupKeyType } from 'api/admin'
import {
  companyDepartmentIdSchema,
  vehicleIdSchema,
  type DriverGroupId,
  type DriverId,
  type DriverLicenseTypeId,
  type DriverSpecialLicenseTypeId,
} from 'api/types'
import { parseBEBoolean } from 'api/utils'
import type { PromiseResolvedType } from 'src/types'
import { groupByAndMapValues, toImmutable } from 'src/util-functions/functional-utils'
import { isNilOrTrimmedEmptyString } from 'src/util-functions/string-utils'
import { utcTimestampToMs } from 'src/util-functions/time-utils'
import { safeParseFromZodSchema } from 'src/util-functions/zod-utils'

import { apiCallerNoX } from '../api-caller'
import type {
  Ct_fleet_get_drivers_v2,
  Ct_fleet_update_client_driver_details_chunk,
} from './types'

export function parseDriverListDriver(
  d: Ct_fleet_get_drivers_v2.ApiOutput['ct_fleet_get_drivers'][number],
) {
  // Here for legacy reasons. Could potentially be removed.
  const statusClassName = 'no-signal'

  return {
    id: d.out_driver_id as DriverId,
    name: d.out_driver_name + (d.out_driver_surname ? ' ' + d.out_driver_surname : ''),
    description: d.out_driver_description,
    gender:
      // eslint-disable-next-line no-nested-ternary
      Number(d.out_gender) === 1
        ? ('M' as const)
        : Number(d.out_gender) === 2
          ? ('F' as const)
          : ('' as const),
    rating: Number(d.out_driver_behaviour_score),
    vehicleRegistration: d.out_vehicle_registration || '',
    vehicleType: d.out_vehicle_type_id || '0',
    active: d.out_driver_statuses.includes('10'),
    statusClassName,
    departmentId: safeParseFromZodSchema(
      companyDepartmentIdSchema,
      d.out_department_id,
      {
        defaultValue: () => null,
      },
    ),
    cellPhone: d.out_cell_phone,
    email: d.out_driver_email,
    licenseNumber: d.out_license_number,
    licenseExpirationTimeUnixMs: d.out_license_expiration_timestamp
      ? utcTimestampToMs(d.out_license_expiration_timestamp)
      : null,
    licenseCode: d.out_license_code,
    idPassportNumber: d.out_id_passport_number,
    employeeNumber: d.out_employee_number,

    vehicleId: isNilOrTrimmedEmptyString(d.out_vehicle_id)
      ? null
      : (vehicleIdSchema.safeParse(d.out_vehicle_id).data ?? null),

    // Defects
    newDefectsCount: Number(d.out_new_defects) || 0,
    repairedDefectsCount: Number(d.out_repaired_defects) || 0,
    inspectedDefectsCount: Number(d.out_inspected_defects) || 0,
    userPermissions: d.out_permissions,
    ownerUsername: d.out_driver_owner,
    deliveryDriver: d.out_delivery_driver,
    carpoolMeta: d.carpool_meta
      ? {
          canAutoBook: parseBEBoolean(
            d.carpool_meta.drivers_can_select_vehicle_using_system_autobooking,
            {
              fallback: false,
            },
          ),
          canBookSpecificVehicles: parseBEBoolean(
            d.carpool_meta.drivers_can_select_vehicle_by_registration,
            { fallback: false },
          ),
          canBookCarpool: parseBEBoolean(d.carpool_meta.drivers_can_book_carpool, {
            fallback: false,
          }),
        }
      : null,
    driverLicenseTypes: d.out_driver_license_types.map((t) => ({
      id: t.id.toString() as DriverLicenseTypeId,
      expirationDate: t.expiration_date,
      isValid:
        !t.expiration_date ||
        DateTime.fromJSDate(new Date(t.expiration_date)).startOf('day') >=
          DateTime.now().startOf('day'),
    })),
    driverSpecialLicenseTypes: d.out_driver_special_license_types.map((t) => ({
      id: t.id.toString() as DriverSpecialLicenseTypeId,
      expirationDate: t.expiration_date,
      isValid:
        !t.expiration_date ||
        DateTime.fromJSDate(new Date(t.expiration_date)).startOf('day') >=
          DateTime.now().startOf('day'),
    })),
  }
}

function parseDrivers(
  rawDrivers: Ct_fleet_get_drivers_v2.ApiOutput['ct_fleet_get_drivers'],
  rawDriverGroups: Ct_fleet_get_drivers_v2.ApiOutput['ct_fleet_get_driverlist_dgroup'],
  rawGroupMap: Ct_fleet_get_drivers_v2.ApiOutput['ct_fleet_get_driverlist_dgroupmap'],
) {
  const drivers = rawDrivers.map((d) => parseDriverListDriver(d))

  const driverIdsByGroupId = groupByAndMapValues(
    rawGroupMap,
    'group_driver_id',
    'client_driver_id',
  )

  const groups = rawDriverGroups.map((raw) => {
    // convert the number to the readable permission
    const permissionId = match<
      typeof raw.permission_id,
      PermissionLookupKeyType | 'hide'
    >(raw.permission_id)
      .with(
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        (value) =>
          R.keys(PermissionLookup).find(
            (key) => PermissionLookup[key] === Number(value),
          ) ?? 'hide',
      )
      .otherwise(() => 'hide') // if the value is invalid, set it as 'hide' default

    const itemIds = (driverIdsByGroupId[raw.group_driver_id] ||
      []) as ReadonlyArray<DriverId>
    return {
      id: raw.group_driver_id as DriverGroupId,
      group_driver_id: raw.group_driver_id as DriverGroupId,
      name: raw.name,
      description: raw.description,
      clientUserId: raw.client_user_id,
      permissionId: permissionId,
      itemIds,
      driverIdsSet: new Set(itemIds),
    }
  })

  return toImmutable({
    drivers,
    groups,
  })
}

const driversAPI = {
  fetchDrivers() {
    return apiCallerNoX<Ct_fleet_get_drivers_v2.ApiOutput>(
      'ct_fleet_get_drivers_v2',
    ).then((res) =>
      parseDrivers(
        res.ct_fleet_get_drivers,
        res.ct_fleet_get_driverlist_dgroup,
        res.ct_fleet_get_driverlist_dgroupmap,
      ),
    )
  },
  updateClientDriverDetailsChunk(
    input: Ct_fleet_update_client_driver_details_chunk.ApiInput,
  ) {
    return apiCallerNoX<Ct_fleet_update_client_driver_details_chunk.ApiOutput>(
      'ct_fleet_update_client_driver_details_chunk',
      input,
    )
  },
}

export default driversAPI

export type FetchDriversResponse = PromiseResolvedType<typeof driversAPI.fetchDrivers>
