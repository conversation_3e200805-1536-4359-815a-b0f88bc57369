/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  findIndex,
  findKey,
  groupBy,
  isEmpty,
  isNil,
  pick,
  range,
  reduce,
  repeat,
  some,
  uniq,
} from 'lodash'
import * as R from 'remeda'
import { concat, isDefined, isTruthy, map, pipe } from 'remeda'
import { match } from 'ts-pattern'
import type { Tagged } from 'type-fest'

import type {
  GeofenceGroupId,
  GeofenceId,
  GeofenceSystemId,
  PositionDescription,
  VehicleGroupId,
  VehicleId,
} from 'api/types'
import { parseBEBoolean } from 'api/utils'
import {
  ContactType,
  findAlertByTrigger,
  SpecialContactType,
} from 'src/modules/alerts/utils'
import type { SetupEventAlertFormValues } from 'src/modules/forms/alerts/setup-event-alert'
import type { SensorStatus } from 'src/modules/forms/alerts/setup-event-alert/notification-types-form-section/types'
import type {
  LineContactFormValue,
  TelegramContactFormValue,
} from 'src/modules/forms/alerts/shared/validation-schema'
import type { FixMeAny, PromiseResolvedType } from 'src/types'
import type { ExcludeStrict } from 'src/types/utils'
import { connectedCtIntl } from 'src/util-components/connectedCtIntl'
import { ctIntl } from 'src/util-components/ctIntl'
import { namespacedUUIDv4 } from 'src/util-functions/string-utils'
import { isTrue } from 'src/util-functions/validation'

import {
  convertDateTimeToSeconds,
  timeStampToDate,
} from '../../util-functions/moment-helper'
import apiCaller, { apiCallerNoX } from '../api-caller'
import { AlertGroupType, AlertTrigger, VisionAlertFacilitiesTriggers } from './triggers'
import {
  staticBitIdName,
  type Alert,
  type Ct_fleet_get_alerts_details,
  type FetchAlertLog,
  type OLD_FetchActiveAlerts,
  type OLD_FetchAlertTypes,
} from './types'

/**
 * @deprecated
 * Parses distance to miles according to the app setting `distanceInMiles`.
 */
export const parseDistanceByAppSetting = (
  distance: number,
  { distanceInMiles }: { distanceInMiles: boolean },
) =>
  connectedCtIntl.formatDistanceIfNeeded(distance, {
    unitToFormatToIfNeeded: 'miles',
    distanceInMiles,
  })

/**
 * @deprecated
 * Normalizes distance to kms (accepted format by endpoints)
 */
export const normalizeDistanceByAppSetting = (
  distance: number,
  { distanceInMiles }: { distanceInMiles: boolean },
) =>
  connectedCtIntl.formatDistanceIfNeeded(distance, {
    unitToFormatToIfNeeded: 'kms',
    distanceInMiles,
  })

export const getAlertsTypesFromTriggers = (
  triggers: Array<AlertTrigger>,
  hasCustomSensors = false,
) => {
  const initialAlertsTypes = {
    enterGeofence: false,
    leaveGeofence: false,
    speedOver: false,
    ignitionOn: false,
    idInvalid: false,
    ignitionOff: false,
    idDetected: false,
    maxSpeedExceeded: false,
    harshBraking: false,
    harshCorneringTurning: false,
    harshAcceleration: false,
    idNotDetected: false,
    unscheduledUsage: false,
    excessiveIdling: false,
    enterSystemZone: false,
    leaveSystemZone: false,
    powerOn: false,
    powerOff: false,
    tripSummary: false,
    overRev: false,
    overRPM: false,
    excessiveDriveTime: false,
    stationaryVehicle: false,
    motion: false,
    sensors: hasCustomSensors,
    fuelLoss: false,
    fuelGain: false,
    geofenceArrivalTime: false,
    tollGate: false,
    overRoadSpeed: false,
    engineDiagnostics: false,
    temperatureDiagnostics: false,
    distraction: false,
    yawn: false,
    fatigue: false,
    pedestrianCollision: false,
    forwardCollision: false,
    smoke: false,
    phone: false,
    cameraCovered: false,
    headwayMonitoring: false,
    laneDeparture: false,
    eyesClosed: false,
    eyeDelta: false,
    urbForwardCollision: false,
    button: false,
    passenger: false,
    noSeatbelt: false,
    multiObject: false,
    lineCrossObject: false,
    facemask: false,
    socialDistance: false,
    actionDetection: false,
    facialRecognition: false,
    licensePlateRecognition: false,
    safetyHelmetDetection: false,
    cameraStatus: false,
    custom: false,
    highPriorityLineCross: false,
    absenceLoitering: false,
    suspiciousBehavior: false,
    customObjectDetection: false,
  }

  return triggers.reduce((acc, curTrigger) => {
    // eslint-disable-next-line sonarjs/max-switch-cases
    switch (curTrigger) {
      case AlertTrigger.ENTER_GEOFENCE: {
        acc.enterGeofence = true
        break
      }
      case AlertTrigger.LEAVE_GEOFENCE: {
        acc.leaveGeofence = true
        break
      }
      case AlertTrigger.SPEED_OVER: {
        acc.speedOver = true
        break
      }
      case AlertTrigger.IGNITION_ON: {
        acc.ignitionOn = true
        break
      }
      case AlertTrigger.INVALID_ID: {
        acc.idInvalid = true
        break
      }
      case AlertTrigger.IGNITION_OFF: {
        acc.ignitionOff = true
        break
      }
      case AlertTrigger.ID_DETECTED: {
        acc.idDetected = true
        break
      }
      case AlertTrigger.MAX_SPEED_EXCEEDED: {
        acc.maxSpeedExceeded = true
        break
      }
      case AlertTrigger.HARSH_BRAKING: {
        acc.harshBraking = true
        break
      }
      case AlertTrigger.HARSH_CORNERING_TURNING: {
        acc.harshCorneringTurning = true
        break
      }
      case AlertTrigger.HARSH_ACCELERATION: {
        acc.harshAcceleration = true
        break
      }
      case AlertTrigger.ID_NOT_DETECTED: {
        acc.idNotDetected = true
        break
      }
      case AlertTrigger.UNSCHEDULED_USAGE: {
        acc.unscheduledUsage = true
        break
      }
      case AlertTrigger.EXCESSIVE_IDLING: {
        acc.excessiveIdling = true
        break
      }
      case AlertTrigger.ENTER_SYSTEM_ZONE: {
        acc.enterSystemZone = true
        break
      }
      case AlertTrigger.LEAVE_SYSTEM_ZONE: {
        acc.leaveSystemZone = true
        break
      }
      case AlertTrigger.POWER_ON: {
        acc.powerOn = true
        break
      }
      case AlertTrigger.POWER_OFF: {
        acc.powerOff = true
        break
      }
      case AlertTrigger.TRIP_SUMMARY: {
        acc.tripSummary = true
        break
      }
      case AlertTrigger.OVER_REV: {
        acc.overRev = true
        break
      }
      case AlertTrigger.OVER_RPM: {
        acc.overRPM = true
        break
      }
      case AlertTrigger.EXCESSIVE_DRIVE_TIME: {
        acc.excessiveDriveTime = true
        break
      }
      case AlertTrigger.STATIONARY_VEHICLE_ON_OFF:
      case AlertTrigger.STATIONARY_VEHICLE_ON: {
        acc.stationaryVehicle = true
        break
      }
      case AlertTrigger.MOTION_DETECTED: {
        acc.motion = true
        break
      }
      case AlertTrigger.SENSOR_ENGINE_HIGH:
      case AlertTrigger.SENSOR_ENGINE_LOW:
      case AlertTrigger.SENSOR_COOLANT_HIGH:
      case AlertTrigger.SENSOR_COOLANT_LOW:
      case AlertTrigger.SENSOR_TEMP1_HIGH:
      case AlertTrigger.SENSOR_TEMP1_LOW:
      case AlertTrigger.SENSOR_TEMP2_HIGH:
      case AlertTrigger.SENSOR_TEMP2_LOW:
      case AlertTrigger.SENSOR_TEMP3_HIGH:
      case AlertTrigger.SENSOR_TEMP3_LOW:
      case AlertTrigger.SENSOR_TEMP4_HIGH:
      case AlertTrigger.SENSOR_TEMP4_LOW:
      case AlertTrigger.SENSOR_UP:
      case AlertTrigger.SENSOR_DOWN: {
        acc.sensors = true
        break
      }
      case AlertTrigger.FUEL_LOSS: {
        acc.fuelLoss = true
        break
      }
      case AlertTrigger.FUEL_GAIN: {
        acc.fuelGain = true
        break
      }
      case AlertTrigger.GEOFENCE_ARRIVAL_TIME: {
        acc.geofenceArrivalTime = true
        break
      }
      case AlertTrigger.TOLL_GATE: {
        acc.tollGate = true
        break
      }
      case AlertTrigger.OVER_ROAD_SPEED: {
        acc.overRoadSpeed = true
        break
      }
      case AlertTrigger.ENGINE_DIAGNOSTICS: {
        acc.engineDiagnostics = true
        break
      }

      //  Vision Vehicles Alerts
      case AlertTrigger.DISTRACTION: {
        acc.distraction = true
        break
      }
      case AlertTrigger.YAWN: {
        acc.yawn = true
        break
      }
      case AlertTrigger.FATIGUE: {
        acc.fatigue = true
        break
      }
      case AlertTrigger.PEDESTRIAN_COLLISION: {
        acc.pedestrianCollision = true
        break
      }
      case AlertTrigger.FORWARD_COLLISION: {
        acc.forwardCollision = true
        break
      }
      case AlertTrigger.SMOKE: {
        acc.smoke = true
        break
      }
      case AlertTrigger.PHONE: {
        acc.phone = true
        break
      }
      case AlertTrigger.CAMERA_COVERED: {
        acc.cameraCovered = true
        break
      }
      case AlertTrigger.HEADWAY_MONITORING: {
        acc.headwayMonitoring = true
        break
      }
      case AlertTrigger.LANE_DEPARTURE: {
        acc.laneDeparture = true
        break
      }
      case AlertTrigger.EYES_CLOSED: {
        acc.eyesClosed = true
        break
      }
      case AlertTrigger.EYE_DELTA: {
        acc.eyeDelta = true
        break
      }
      case AlertTrigger.URB_FORWARD_COLLISION: {
        acc.urbForwardCollision = true
        break
      }
      case AlertTrigger.BUTTON: {
        acc.button = true
        break
      }
      case AlertTrigger.PASSENGER: {
        acc.passenger = true
        break
      }
      case AlertTrigger.NO_SEATBELT: {
        acc.noSeatbelt = true
        break
      }
      //  Vision Facilities Alerts
      case AlertTrigger.MULTI_OBJECT: {
        acc.multiObject = true
        break
      }
      case AlertTrigger.LINE_CROSS_OBJECT: {
        acc.lineCrossObject = true
        break
      }
      case AlertTrigger.FACEMASK: {
        acc.facemask = true
        break
      }
      case AlertTrigger.SOCIAL_DISTANCE: {
        acc.socialDistance = true
        break
      }
      case AlertTrigger.ACTION_DETECTION: {
        acc.actionDetection = true
        break
      }
      case AlertTrigger.FACIAL_RECOGNITION: {
        acc.facialRecognition = true
        break
      }
      case AlertTrigger.LICENSE_PLATE_RECOGNITION: {
        acc.licensePlateRecognition = true
        break
      }
      case AlertTrigger.SAFETY_HELMET_DETECTION: {
        acc.safetyHelmetDetection = true
        break
      }
      case AlertTrigger.CAMERA_STATUS: {
        acc.cameraStatus = true
        break
      }
      case AlertTrigger.CUSTOM: {
        acc.custom = true
        break
      }
      case AlertTrigger.HIGH_PRIORITY_LINE_CROSS: {
        acc.highPriorityLineCross = true
        break
      }
      case AlertTrigger.ABSENCE_LOITERING: {
        acc.absenceLoitering = true
        break
      }
      case AlertTrigger.SUSPICIOUS_BEHAVIOR: {
        acc.suspiciousBehavior = true
        break
      }
      case AlertTrigger.CUSTOM_OBJECT_DETECTION: {
        acc.customObjectDetection = true
        break
      }
      default: {
        break
      }
    }

    return acc
  }, initialAlertsTypes)
}

export const getAlertGroupsFromTriggers = (triggers: ReadonlyArray<AlertTrigger>) => {
  const types = []
  for (const trigger of triggers) {
    const matchedAlert = findAlertByTrigger(trigger)
    if (matchedAlert && matchedAlert.groupType) {
      types.push(matchedAlert.groupType)
    }
  }
  return types
}

export const getAlertGroupFromTriggers = (triggers: ReadonlyArray<AlertTrigger>) => {
  for (const trigger of triggers) {
    const matchedAlert = findAlertByTrigger(trigger)
    if (matchedAlert && matchedAlert.groupType) {
      return matchedAlert.groupType
    }
  }

  return '' as const
}

// Parse
const parseAlertTypes = (
  result: OLD_FetchAlertTypes.ApiOutput,
): OLD_FetchAlertTypes.Return => {
  const systemZones = groupBy(
    (result.systemzones_light || []).map((g) => ({
      value: g.geofence_id.trim(),
      name: g.geofence_name.replace(
        '10km from border',
        ctIntl.formatMessage({ id: '10km from border' }),
      ),
      key: g.geofence_id.trim(),
      type: g.geofence_type_id,
      typeDescription: ctIntl.formatMessage({
        id: g.geofence_type_description,
      }),
    })),
    'type',
  )

  const systemZoneTypes = Object.keys(systemZones).map((id) => ({
    name: systemZones[id][0].typeDescription,
    value: id,
  }))

  const geofenceGroups = result.geofence_group_light.geofence_groups_id
    ? result.geofence_group_light.geofence_groups_id.map((group) => ({
        id: group.geofence_id,
        value: group.geofence_id,
        key: group.geofence_id,
        name: group.geofence_name,
        label: group.geofence_name,
        isGroup: true as const,
      }))
    : []

  const geofences = result.geofence_group_light.geofence_ids
    ? result.geofence_group_light.geofence_ids.map((geofence) => ({
        id: geofence.geofence_id,
        value: geofence.geofence_id,
        key: geofence.geofence_id,
        name: geofence.geofence_name,
        label: geofence.geofence_name,
        isGroup: false as const,
      }))
    : []

  return {
    geofences: [...geofences, ...geofenceGroups],
    systemZones,
    systemZoneTypes,
    staticBits: (result.static_bit_types || []).map((b) => ({
      id: b.id,
      name: b.bit_name,
      description: b.description,
    })),
    contactTypes: (result.contact_types || []).map((c) => ({
      id: c.notification_contact_type_id,
      description: c.contact_description,
      cost: c.message_cost ?? '0',
    })),
    triggers: (result.notification_trigger_types || []).map((t) => ({
      id: t.notification_trigger_type_id,
      description: t.trigger_description,
      descriptionTermTranslationId: t.trigger_description_term_translation_id,
      group: t.trigger_group_type_id,
      hasValueField: t.has_value_field === 't',
      index: t.display_order,
      isRouteAlert: t.route_alert === 't',
    })),
    customSensors: (result.sensors || []).map((s) => ({
      id: s.vehicle_sensors_id,
      name: `${s.registration ? s.registration + ' - ' : ''}${
        s.sensor_name ? s.sensor_name + ' - ' : ''
      }${s.sensor_type ?? ''}`,
    })),
  }
}

const parseStaticBits = (
  on: Array<string> | null | undefined,
  off: Array<string> | null | undefined,
) => {
  const onBits = (on ?? []).reduce(
    (acc, bit) => {
      acc[bit] = 'ON'

      return acc
    },
    {} as Record<string, 'ON'>,
  )

  const offBits = (off ?? []).reduce(
    (acc, bit) => {
      if (onBits[bit]) {
        acc[bit] = 'ON,OFF'
      } else {
        acc[bit] = 'OFF'
      }

      return acc
    },
    {} as Record<string, 'OFF' | 'ON,OFF'>,
  )

  return { ...onBits, ...offBits }
}

const parseSchedule = (schedule: FixMeAny) => {
  if (isEmpty(schedule)) {
    return null
  }

  if (typeof schedule !== 'string' || !schedule.includes('1')) {
    return null
  }

  const byDay = schedule.split('\n')
  const filteredDays = byDay.reduce((acc, day, index) => {
    if (day.includes('1')) {
      acc.push(index)
    }

    return acc
  }, [] as Array<number>)
  const firstActiveDay = byDay[filteredDays[0]]

  const isAdvancedSchedule =
    !byDay.every((d) => d === firstActiveDay || !d.includes('1')) ||
    firstActiveDay.indexOf('1') % 2 !== 0 ||
    (firstActiveDay.lastIndexOf('1') + 1) % 2 !== 0

  const advancedSchedule = schedule
    .split('')
    .filter((c) => c !== '\n')
    .map((s) => s === '1')

  const hours = {
    from: Math.floor(firstActiveDay.indexOf('1') / 2) / 2,
    to: Math.min(Math.floor((firstActiveDay.lastIndexOf('1') + 1) / 2) / 2, 23.5),
  }

  const days = range(7).map((i) => filteredDays.includes(i)) as SevenBoolArray

  return {
    hours,
    days,
    advancedSchedule,
    isAdvancedSchedule,
  }
}

// Sensor

const SENSORS_TRIGGERS = {
  [AlertTrigger.SENSOR_ENGINE_HIGH]: 'high_temp_engine_value',
  [AlertTrigger.SENSOR_ENGINE_LOW]: 'low_temp_engine_value',
  [AlertTrigger.SENSOR_COOLANT_HIGH]: 'high_temp_coolant_value',
  [AlertTrigger.SENSOR_COOLANT_LOW]: 'low_temp_coolant_value',
  [AlertTrigger.SENSOR_TEMP1_HIGH]: 'high_temp_1_value',
  [AlertTrigger.SENSOR_TEMP1_LOW]: 'low_temp_1_value',
  [AlertTrigger.SENSOR_TEMP2_HIGH]: 'high_temp_2_value',
  [AlertTrigger.SENSOR_TEMP2_LOW]: 'low_temp_2_value',
  [AlertTrigger.SENSOR_TEMP3_HIGH]: 'high_temp_3_value',
  [AlertTrigger.SENSOR_TEMP3_LOW]: 'low_temp_3_value',
  [AlertTrigger.SENSOR_TEMP4_HIGH]: 'high_temp_4_value',
  [AlertTrigger.SENSOR_TEMP4_LOW]: 'low_temp_4_value',
  [AlertTrigger.SENSOR_TEMP1_HIGH_WITH_IGNITION_ON]:
    'high_temp_1_with_ignition_on_value',
  [AlertTrigger.SENSOR_TEMP1_LOW_WITH_IGNITION_ON]: 'low_temp_1_with_ignition_on_value',
  [AlertTrigger.SENSOR_TEMP2_HIGH_WITH_IGNITION_ON]:
    'high_temp_2_with_ignition_on_value',
  [AlertTrigger.SENSOR_TEMP2_LOW_WITH_IGNITION_ON]: 'low_temp_2_with_ignition_on_value',
  [AlertTrigger.SENSOR_TEMP3_HIGH_WITH_IGNITION_ON]:
    'high_temp_3_with_ignition_on_value',
  [AlertTrigger.SENSOR_TEMP3_LOW_WITH_IGNITION_ON]: 'low_temp_3_with_ignition_on_value',
  [AlertTrigger.SENSOR_TEMP4_HIGH_WITH_IGNITION_ON]:
    'high_temp_4_with_ignition_on_value',
  [AlertTrigger.SENSOR_TEMP4_LOW_WITH_IGNITION_ON]: 'low_temp_4_with_ignition_on_value',
} as const

const SENSORS_TRIGGERS_BY_FIELD_NAME = R.invert(SENSORS_TRIGGERS)

const parseSensorData = (
  alert: Ct_fleet_get_alerts_details.ApiOutput['ct_fleet_get_alerts_details'],
  triggers: Array<AlertTrigger>,
) => {
  const triggerSensorValues = {} as Record<keyof typeof SENSORS_TRIGGERS, number | null>

  for (const trigger_any in SENSORS_TRIGGERS) {
    const trigger = trigger_any as keyof typeof SENSORS_TRIGGERS
    const fieldName = SENSORS_TRIGGERS[trigger]
    if (fieldName in alert) {
      triggerSensorValues[trigger] = alert[fieldName as keyof typeof alert] as
        | number
        | null
    }
  }

  for (const trigger_type_id_value of alert.trigger_type_id_values ?? []) {
    const trigger = trigger_type_id_value.id as keyof typeof SENSORS_TRIGGERS
    triggerSensorValues[trigger] = trigger_type_id_value.value
  }

  return {
    ...pick(triggerSensorValues, triggers),
    customSensors: parseCustomSensors(alert),
  }
}

const createCustomSensorStatusArray = (test: undefined | string | Array<string>) => {
  if (typeof test === 'string') {
    return [test]
  }
  return test ?? []
}

const parseCustomSensors = ({
  check_value44: sensorOff,
  check_value45: sensorOn,
}: {
  check_value44?: Array<string> | string
  check_value45?: Array<string> | string
}) => {
  const sensorsOffArray = createCustomSensorStatusArray(sensorOff)
  const sensorsOnArray = createCustomSensorStatusArray(sensorOn)
  const sensorsIds = uniq(concat(sensorsOffArray, sensorsOnArray))

  const customSensors = pipe(
    sensorsIds,
    map((id) => {
      const statusOff = isDefined(sensorsOffArray.find((sensor) => sensor === id))
      const statusOn = isDefined(sensorsOnArray.find((sensor) => sensor === id))
      const status = match({ statusOff, statusOn })
        .returnType<SensorStatus | null>()
        .with({ statusOff: false, statusOn: false }, () => null)
        .with({ statusOff: true, statusOn: false }, () => 'OFF')
        .with({ statusOff: false, statusOn: true }, () => 'ON')
        .with({ statusOff: true, statusOn: true }, () => 'ON,OFF')
        .exhaustive()
      if (!status) {
        return
      }
      return {
        id,
        status,
      }
    }),
  )

  return customSensors
}

const normalizeSensors = (
  sensorOptions: SetupEventAlertFormValues['sensorOptions'] | undefined,
) => {
  if (!sensorOptions) {
    return undefined
  }

  const checkValues: Record<
    `check_value${number}`,
    | number
    | string
    | Array<string>
    | Array<
        | {
            id: string | null
            status: SensorStatus | null
          }
        | undefined
      >
    | null
  > = {}

  const triggerTypeIdValues: Array<{
    id: string | number
    value: number | null
  }> = []

  R.forEachObj(sensorOptions, (sensor, trigger) => {
    if (sensor !== undefined && trigger !== 'hysteresis') {
      if (trigger !== 'customSensors') {
        const sensorValue: number | null = sensor as ExcludeStrict<
          typeof sensor,
          (typeof sensorOptions)['customSensors']
        >
        triggerTypeIdValues.push({
          id: SENSORS_TRIGGERS_BY_FIELD_NAME[trigger],
          value: sensorValue,
        })
      } else {
        const typedSensors = sensor as Array<{
          id: string
          status: SensorStatus
        }>
        const offCustomSensors: Array<string> = []
        const onCustomSensors: Array<string> = []
        for (const typedSensor of typedSensors) {
          match(typedSensor.status)
            .with('OFF', () => offCustomSensors.push(typedSensor.id))
            .with('ON', () => {
              onCustomSensors.push(typedSensor.id)
            })
            .with('ON,OFF', () => {
              offCustomSensors.push(typedSensor.id)
              onCustomSensors.push(typedSensor.id)
            })
            .exhaustive()
        }

        checkValues['check_value44'] = offCustomSensors
        checkValues['check_value45'] = onCustomSensors
      }
    }
  })

  return { checkValues, triggerTypeIdValues }
}

const parseSystemZone = (
  alertGeofence: Array<{
    id: GeofenceSystemId | GeofenceId | GeofenceGroupId
  }>,
  alertTypes: FixMeAny,
) => {
  if (alertTypes) {
    const { systemZones, systemZoneTypes } = parseAlertTypes(alertTypes)
    type SystemZone = {
      key: GeofenceSystemId
      value: GeofenceSystemId
      label: string
      name: string
      type: string
    }
    let selectedSystemZones: Array<SystemZone> = []

    if (R.isArray(alertGeofence) && alertGeofence.length > 0) {
      selectedSystemZones = alertGeofence.reduce<Array<SystemZone>>(
        (acc, maybeGeofence) => {
          for (const key in systemZones) {
            const systemZone = systemZones[key].find(
              // Enable triple equals when you have enough confidence "key" is always a string
              // eslint-disable-next-line eqeqeq
              (systemZone) => systemZone.key == maybeGeofence.id.trim(),
            )

            if (systemZone) {
              acc.push({
                key: systemZone.value as GeofenceSystemId,
                value: systemZone.value as GeofenceSystemId,
                label: systemZone.name,
                name: systemZone.name,
                type: systemZone.type,
              })
            }
          }

          return acc
        },
        [],
      )
    }

    return {
      systemZones: selectedSystemZones,
      availableSystemZones: systemZones,
      availableSystemZoneTypes: systemZoneTypes,
    }
  }

  return {
    systemZones: [],
    availableSystemZones: {},
    availableSystemZoneTypes: [],
  }
}

export const parseAlertDetailsFromApi = (
  alert: Ct_fleet_get_alerts_details.ApiOutput['ct_fleet_get_alerts_details'],
  alertTypes: Record<string, any>,
  { distanceInMiles }: { distanceInMiles: boolean },
) => {
  const triggers: Array<AlertTrigger> = alert.triggers.map(
    (trigger) => trigger.toString() as AlertTrigger,
  )

  const vehicles = parseVehicles(alert.vehicles)
  const vehiclesGroups = parseVehiclesGroups(alert.alert_selected_vh_grp || [])
  const hasGeofences =
    alert.geofences &&
    ((R.isArray(alert.geofences.geofence_ids) &&
      alert.geofences.geofence_ids.length > 0) ||
      (R.isArray(alert.geofences.geofence_groups_id) &&
        alert.geofences.geofence_groups_id.length > 0))

  const allFacilities = isTruthy(
    alert.alert_selected_facilities &&
      alert.alert_selected_facilities.length === 0 &&
      triggers.some((trigger) =>
        VisionAlertFacilitiesTriggers.includes(trigger as FixMeAny),
      ),
  )

  const allCameras = isTruthy(
    alert.alert_selected_facilities &&
      triggers.some((trigger) =>
        VisionAlertFacilitiesTriggers.includes(trigger as FixMeAny),
      ) &&
      vehicles.length === 0 &&
      (alert.any_vehicle === 't' ||
        alert.any_vehicle === true ||
        alert.any_vehicle === '0'),
  )

  type GeofenceGroup = {
    id: GeofenceGroupId
    value: GeofenceGroupId
    key: GeofenceGroupId
    label: string
    name: string
    isGroup: true
  }
  const geofenceGroups: Array<GeofenceGroup> =
    alert.geofences && alert.geofences.geofence_groups_id
      ? alert.geofences.geofence_groups_id.map(
          (group): GeofenceGroup => ({
            id: group.id,
            value: group.id,
            key: group.id,
            label: group.name,
            name: group.name,
            isGroup: true,
          }),
        )
      : []

  type GeofenceSystem = {
    id: GeofenceSystemId
    value: GeofenceSystemId
    key: GeofenceSystemId
    label: string
    name: string
    isGroup: false
  }
  const geofenceSystems: Array<GeofenceSystem> =
    alert.geofences && alert.geofences.geofence_systems_id
      ? alert.geofences.geofence_systems_id.map(
          (gSystem): GeofenceSystem => ({
            id: gSystem.id,
            value: gSystem.id,
            key: gSystem.id,
            label: gSystem.name,
            name: gSystem.name,
            isGroup: false,
          }),
        )
      : []

  const geofences: Array<Geofence> =
    alert.geofences && alert.geofences.geofence_ids
      ? alert.geofences.geofence_ids.map(
          (geofence): Geofence => ({
            id: geofence.id,
            value: geofence.id,
            key: geofence.id,
            label: geofence.name,
            name: geofence.name,
            isGroup: false,
            address: null,
          }),
        )
      : []

  const geofencesOptions: Array<Geofence | GeofenceGroup | GeofenceSystem> = [
    ...geofences,
    ...geofenceGroups,
    ...geofenceSystems,
  ]

  const systemZoneData = parseSystemZone(geofencesOptions, alertTypes)
  return {
    anyFacility: allFacilities,
    anyVehicle:
      (alert.any_vehicle === 't' ||
        alert.any_vehicle === true ||
        alert.any_vehicle === '0') &&
      !allFacilities,
    anyCameras: allCameras,
    contact: alert.notification_contact.split(';').filter((val) => val.length > 0),
    contactType: alert.notification_contact_type_id,
    severityLevel: alert.priority_id,
    assignedDriversOnly: alert.assigned_drivers_only ?? false,
    createdAt: connectedCtIntl.formatDateWithHourMinute(alert.create_ts),
    updatedAt: connectedCtIntl.formatDateWithHourMinute(alert.update_ts),
    systemZoneData,
    alertGeofence: hasGeofences
      ? // Since we check if geofences or groups have at least one element, we can safely assume the first option
        // will always be either a Geofence or a GeofenceGroup
        (geofencesOptions[0] as Geofence | GeofenceGroup)
      : null,
    geofences: geofencesOptions,
    geofenceArrivalTime: alert.one_time_geofence_arrival_time
      ? timeStampToDate(alert.one_time_geofence_arrival_time, 'YYYY-MM-DD HH:mm:ssZZ')
      : 0,
    id: alert.notification_configuration_group_id || alert.id,
    groupDescription: alert.group_description,
    rpmOver: Number(alert.high_rpm_value),
    sensorParams: parseSensorData(alert, triggers),
    maxCheckValue: alert.max_check_value,
    motionStartIgnition: alert.motion_value === '1' ? 'on' : 'off',
    repeatId: alert.notification_repeat_id,
    schedule: parseSchedule(alert.schedule),
    scheduleId: alert.schedule_id,
    roadSpeedOver: Math.round(
      parseDistanceByAppSetting(Number(alert.over_rd_spd_value), { distanceInMiles }),
    ),
    speedOver: Math.round(
      parseDistanceByAppSetting(Number(alert.spd_value), { distanceInMiles }),
    ),
    staticBits: parseStaticBits(alert.sbitonarr, alert.sbitoffarr),
    stationaryDuration: Number(
      alert.stationary_ignoff_value || alert.stationary_ignon_value,
    ),
    stationaryVehicleIgnition: triggers.includes(AlertTrigger.STATIONARY_VEHICLE_ON)
      ? 'on'
      : 'onOrOff',
    statusId: alert.statusId,
    excessiveDriveTime:
      Number(alert.time_value) >= 60 ? Number(alert.time_value) / 60 : 0,
    byGeofence: hasGeofences,
    outsideGeofence: isTrue(alert.track_outside_geofence),
    triggers,
    hysteresis: alert.hysteresis ? Number(alert.hysteresis) : 0,
    vehicles,
    vehiclesGroups,
    facilities: alert.alert_selected_facilities
      ? map(alert.alert_selected_facilities, (item) => item.facility_id)
      : [],
    facilityCameras: alert.alert_selected_facilities ? vehicles : [],
    coaching: alert.coaching,
  }
}

const parseActiveAlert = (
  alert: OLD_FetchActiveAlerts.ApiOutput[number],
): OLD_FetchActiveAlerts.Return[number] => {
  const triggers = (alert.triggers?.split(';') || []) as Array<AlertTrigger>

  const allFacilities = isTruthy(
    !alert.facilities_name &&
      triggers.some((trigger) =>
        VisionAlertFacilitiesTriggers.includes(trigger as FixMeAny),
      ),
  )

  let contactValue = null

  if (!isEmpty(alert.notification_contact)) {
    if (alert.notification_contact_type_id === ContactType.LINE) {
      contactValue = normalizedLineContactFormValueToLineContactFormValue(
        alert.notification_contact as NormalizedLineContactFormValue,
      )
    } else if (alert.notification_contact_type_id === ContactType.TELEGRAM) {
      contactValue = normalizedTelegramContactFormValueToTelegramContactFormValue(
        alert.notification_contact as NormalizedTelegramContactFormValue,
      )
    } else {
      contactValue = alert.notification_contact
    }
  }

  const anyVehicleValue = parseBEBoolean(alert.any_vehicle, { fallback: false })
  return {
    id: alert.id,
    type: getAlertGroupFromTriggers(triggers),
    anyVehicle: anyVehicleValue && !allFacilities,
    anyFacility: allFacilities,
    contacts: isTrue(alert.assigned_drivers_only)
      ? [ctIntl.formatMessage({ id: 'Assigned Drivers' })]
      : alert.notification_contact
          ?.split(';')
          .filter((val: string) => val.length > 0) || [],

    contactType: alert.notification_contact_type_id,
    contactValue,
    createdAt: connectedCtIntl.formatDateWithHourMinute(alert.create_ts) || '',
    updatedAt: connectedCtIntl.formatDateWithHourMinute(alert.update_ts) || '',
    groupDescription: alert.group_description || '',
    triggers,
    vehicles: alert.vehicles?.split(';') || [],
    vehicleGroups: alert.vehicle_group_name?.split(',') || [],
    facilitiesName: alert.facilities_name?.split(',') || [],
  }
}

// Normalize
const normalizeSchedule = (props: Alert['schedule']) => {
  // Schedule is shared between Unscheduled Usage and Notification Schedules
  if (props.sendAnytime) {
    return ''
  }

  let scheduleArr
  if (props.isAdvancedSchedule) {
    // Format: single array, index = 15 minute increment
    scheduleArr = range(7).map((i) =>
      props.advancedSchedule
        .slice(i * 96, i * 96 + 96)
        .map((e) => (e ? '1' : '0'))
        .join(''),
    )
  } else {
    // Format: hour range and array of days of week
    let scheduleDay = ''
    const emptyDay = repeat('0', 96)
    for (const h of range(96))
      scheduleDay +=
        h >= props.hours.from * 4 && h <= props.hours.to * 4 - 1 ? '1' : '0'

    scheduleArr = props.days.map((d) => (d ? scheduleDay : emptyDay))
  }

  return scheduleArr.join(String.raw`\n`) + String.raw`\n`
}

const normalizeStaticBits = (bits: Alert['staticBits']) => {
  if (!bits) {
    return []
  }

  return Object.keys(bits).reduce((acc, bitKey) => {
    const staticBitId = findKey(staticBitIdName, (staticBit) => staticBit === bitKey)

    if (staticBitId && !isNil(bits[bitKey as keyof typeof bits])) {
      bits[bitKey as keyof typeof bits].split(',').map((value) => {
        if (value) {
          acc.push(`${staticBitId}_${value}`)
        }
      })
    }

    return acc
  }, [] as Array<string>)
}

// eslint-disable-next-line complexity
const normalizeTriggers = (alert: Alert) => {
  const triggers = alert.triggers ? [...alert.triggers] : []

  const additionalTriggers = {
    // Geofence
    [AlertTrigger.ENTER_GEOFENCE]: alert.geofence && alert.geofence.enterGeofence,
    [AlertTrigger.LEAVE_GEOFENCE]: alert.geofence && alert.geofence.leaveGeofence,
    // System Zone
    [AlertTrigger.ENTER_SYSTEM_ZONE]:
      alert.systemZone && alert.systemZone.enterSystemZone,
    [AlertTrigger.LEAVE_SYSTEM_ZONE]:
      alert.systemZone && alert.systemZone.leaveSystemZone,
    // Driver Id
    [AlertTrigger.ID_DETECTED]: alert.driverId && alert.driverId.idDetected,
    [AlertTrigger.INVALID_ID]: alert.driverId && alert.driverId.idInvalid,
    [AlertTrigger.ID_NOT_DETECTED]: alert.driverId && alert.driverId.idNotDetected,
    // Event
    [AlertTrigger.SPEED_OVER]: alert.event && alert.event.speedOver,
    [AlertTrigger.IGNITION_ON]: alert.event && alert.event.ignitionOn,
    [AlertTrigger.IGNITION_OFF]: alert.event && alert.event.ignitionOff,
    [AlertTrigger.MAX_SPEED_EXCEEDED]: alert.event && alert.event.maxSpeedExceeded,
    [AlertTrigger.HARSH_BRAKING]: alert.event && alert.event.harshBraking,
    [AlertTrigger.HARSH_CORNERING_TURNING]:
      alert.event && alert.event.harshCorneringTurning,
    [AlertTrigger.HARSH_ACCELERATION]: alert.event && alert.event.harshAcceleration,
    [AlertTrigger.UNSCHEDULED_USAGE]: alert.event && alert.event.unscheduledUsage,
    [AlertTrigger.EXCESSIVE_IDLING]: alert.event && alert.event.excessiveIdling,
    [AlertTrigger.POWER_ON]: alert.event && alert.event.powerOn,
    [AlertTrigger.POWER_OFF]: alert.event && alert.event.powerOff,
    [AlertTrigger.TRIP_SUMMARY]: alert.event && alert.event.tripSummary,
    [AlertTrigger.OVER_REV]: alert.event && alert.event.overRev,
    [AlertTrigger.OVER_RPM]: alert.event && alert.event.overRPM,
    [AlertTrigger.EXCESSIVE_DRIVE_TIME]: alert.event && alert.event.excessiveDriveTime,
    [AlertTrigger.STATIONARY_VEHICLE_ON_OFF]:
      alert.event &&
      alert.event.stationaryVehicle &&
      alert.stationaryVehicleIgnition &&
      alert.stationaryVehicleIgnition !== 'on',
    [AlertTrigger.STATIONARY_VEHICLE_ON]:
      alert.event &&
      alert.event.stationaryVehicle &&
      alert.stationaryVehicleIgnition &&
      alert.stationaryVehicleIgnition === 'on',
    [AlertTrigger.MOTION_DETECTED]: alert.event && alert.event.motion,
    [AlertTrigger.SENSOR_UP]:
      alert.event &&
      alert.sensorOptions?.customSensors.some((sensor) =>
        sensor.status?.includes('OFF'),
      ),
    [AlertTrigger.SENSOR_DOWN]:
      alert.event &&
      alert.sensorOptions?.customSensors.some((sensor) =>
        sensor.status?.includes('ON'),
      ),
    [AlertTrigger.FUEL_LOSS]: alert.event && alert.event.fuelLoss,
    [AlertTrigger.FUEL_GAIN]: alert.event && alert.event.fuelGain,
    [AlertTrigger.GEOFENCE_ARRIVAL_TIME]:
      alert.event && alert.event.geofenceArrivalTime,
    [AlertTrigger.STATIC_BIT_ON]: alert.event && alert.event.staticBits,
    [AlertTrigger.STATIC_BIT_OFF]: alert.event && alert.event.staticBits,
    [AlertTrigger.TOLL_GATE]: alert.event && alert.event.tollGate,
    [AlertTrigger.OVER_ROAD_SPEED]: alert.event && alert.event.overRoadSpeed,
    [AlertTrigger.ENGINE_DIAGNOSTICS]: alert.event && alert.event.engineDiagnostics,
    [AlertTrigger.TEMPERATURE_DIAGNOSTICS]:
      alert.event && alert.event.temperatureDiagnostics,
  }

  Object.keys(additionalTriggers).map((triggerIdAny) => {
    const triggerId = triggerIdAny as keyof typeof additionalTriggers
    if (additionalTriggers[triggerId]) {
      triggers.push(triggerId)
    }
  })

  if (alert.vision) {
    alert.vision.map((triggerId) => triggers.push(triggerId))
  }

  return triggers
}

const checkSpecialGeofence = (field: Alert['systemZone']) => some(field, (a) => a)

const normalizeAlert = (
  alert: Alert,
  { distanceInMiles }: { distanceInMiles: boolean },
) => {
  let contact = null
  let schedule = ''
  let geofences: {
    geofence_groups_id: Array<string>
    geofence_ids: Array<string>
  } = { geofence_groups_id: [], geofence_ids: [] }

  if (!alert.contact.assignedDriversOnly) {
    contact = match(alert.contact.contactType)
      .with(ContactType.SMS, () => ({ [ContactType.SMS]: alert.contact.sms }))
      .with(ContactType.EMAIL, () => ({ [ContactType.EMAIL]: alert.contact.email }))
      .with(ContactType.WHATSAPP, () => ({
        [ContactType.WHATSAPP]: alert.contact.whatsapp,
      }))
      .with(ContactType.CAMS_CONTROL_ROOM, () => ({
        [ContactType.CAMS_CONTROL_ROOM]: '',
      }))
      .with(ContactType.ALERT_CENTER, () => ({
        [ContactType.ALERT_CENTER]: '',
      }))
      .with(ContactType.UNIT, () => ({ [ContactType.UNIT]: 'Unit' }))
      .with(ContactType.LINE, () => ({
        [ContactType.LINE]: alert.contact.line as LineContactFormValue,
      }))
      .with(ContactType.TELEGRAM, () => ({
        [ContactType.TELEGRAM]: alert.contact.telegram as TelegramContactFormValue,
      }))
      .with(ContactType.RSS, () => ({ [ContactType.RSS]: 'RSS' }))
      .exhaustive()
  }

  if (
    !alert.schedule.sendAnytime ||
    (alert.alertType && alert.alertType.hideSchedule) ||
    (alert.alertType && alert.alertType.name === 'Unscheduled Usage')
  ) {
    schedule = normalizeSchedule(alert.schedule)
  }

  if (
    isTruthy(alert.geofences) &&
    R.isArray(alert.geofences) &&
    alert.geofences.length > 0
  ) {
    geofences = reduce(
      alert.geofences,
      (acc, g) => {
        if (g.isGroup) {
          acc.geofence_groups_id.push(g.value)
        } else {
          acc.geofence_ids.push(g.value)
        }
        return acc
      },
      { geofence_groups_id: [], geofence_ids: [] } as {
        geofence_groups_id: Array<string>
        geofence_ids: Array<string>
      },
    )
  } else if (!isNil(alert.systemZone) && checkSpecialGeofence(alert.systemZone)) {
    geofences = reduce(
      alert.systemZones,
      (acc, g) => {
        acc.geofence_ids.push(g.value)

        return acc
      },
      { geofence_groups_id: [], geofence_ids: [] } as {
        geofence_groups_id: Array<string>
        geofence_ids: Array<string>
      },
    )
  }

  const triggers = normalizeTriggers(alert)

  const normalizedSensors = normalizeSensors(alert.sensorOptions)

  return {
    notifications: [
      {
        notification_configuration_group_id: alert.id || '',
        track_outside_geofence: alert.outsideGeofence || false,
        notification_repeat_id: '1',
        after_hours_action_id: '0',
        notification_contact: contact,
        notification_contact_type_id:
          alert.coaching.length === 0
            ? [alert.contact.contactType]
            : [alert.contact.contactType, SpecialContactType.COACHING],
        priority_id:
          alert.contact.contactType === ContactType.ALERT_CENTER ||
          alert.contact.contactType === ContactType.CAMS_CONTROL_ROOM
            ? alert.contact.severity
            : undefined,
        group_description: alert.name,
        sbitArr: normalizeStaticBits(alert.staticBits),
        vehicles: alert.vehicles || undefined,
        any_vehicle: alert.allVehicles,
        assigned_drivers_only: alert.contact.assignedDriversOnly,
        geofences,
        wkScheduleID: '',
        wkSchedule: schedule,
        triggers,
        vehicle_groups: alert.vehiclesGroups,
        facilityIDs: alert.facilities || [],
        schedule_id: alert.schedule_id || '',
        hysteresis: alert.sensorOptions && alert.sensorOptions.hysteresis,
        coaching: alert.coaching,

        // See note below for check_value descriptions
        // Check if it needs to be formatted back to kilometers
        check_value:
          Math.round(
            normalizeDistanceByAppSetting(Number(alert.speedOver), { distanceInMiles }),
          ) || 25,
        check_value2: Number(alert.excessiveDriveTime) * 60 || 0,
        check_value3: Number(alert.stationaryDuration) || 1, // For when stationaryVehicleIgnition = 'onOrOff'
        check_value4: Number(alert.stationaryDuration) || 1, // For when stationaryVehicleIgnition = 'on'
        check_value5: Number(alert.rpmOver) || 0,
        check_value6: alert.motionStartIgnition === 'on' ? 1 : 0,
        ...normalizedSensors?.checkValues,
        trigger_type_id_values: normalizedSensors?.triggerTypeIdValues,
        // Check if it needs to be formatted back to kilometers
        check_value21:
          Math.round(
            normalizeDistanceByAppSetting(Number(alert.roadSpeedOver), {
              distanceInMiles,
            }),
          ) || 0,
        check_value22: alert.geofenceArrivalTime
          ? convertDateTimeToSeconds(alert.geofenceArrivalTime)
          : undefined,
        check_value69: Number(alert.breakdownInterval),
      },
    ],
  }
}

const parseAlertLog = (
  alertsLog: FetchAlertLog.ApiOutput['ct_fleet_get_alerts_log'],
) => {
  if (!alertsLog) {
    return []
  }

  return alertsLog.reduce<
    Array<{
      type: 'alert'
      eventTs: {
        date: string
        formatted: string
        formattedDate: string
        formattedTime: string
      }
      notificationContact: string
      contactType: string
      notificationMsg: string | null
      registration: string
      vehicleId: string
      triggers: readonly [string]
      groupDescription: string
      triggerDescription: string
      status: string
      statusDescription: string
      isFacility: boolean
      generatedUUID: string
    }>
  >((acc, rawAlert) => {
    const alertType = findAlertByTrigger(
      rawAlert.notification_trigger_type_id as FixMeAny,
    )

    const isFacility = alertType?.groupType === AlertGroupType.VISION_FACILITIES

    const triggerDescription = alertType
      ? alertType.name.replace('alert.', '')
      : 'Unknown Trigger'

    let groupDescription = ''
    if (rawAlert.group_description) {
      groupDescription =
        rawAlert.group_description === triggerDescription
          ? ''
          : rawAlert.group_description.replace('$$GROUP$$', '')
    }

    const alert = {
      type: 'alert' as const,
      eventTs: connectedCtIntl.formatDateWithHourMinute(rawAlert.event_ts),
      notificationContact: rawAlert.notification_contact,
      contactType: rawAlert.notification_contact_type_id,
      notificationMsg: rawAlert.notification_msg,
      registration: rawAlert.registration,
      vehicleId: rawAlert.vehicle_id,
      triggers: [rawAlert.notification_trigger_type_id] as const,
      groupDescription,
      triggerDescription,
      status: rawAlert.status,
      statusDescription: rawAlert.status_description || '',
      isFacility,
      generatedUUID: namespacedUUIDv4('alert'),
    }

    // Ignore duplicated alerts
    if (
      findIndex(acc, {
        eventTs: alert.eventTs,
        groupDescription: alert.groupDescription,
        triggerDescription: alert.triggerDescription,
        registration: alert.registration,
        contactType: alert.contactType,
        statusDescription: alert.statusDescription,
      }) === -1
    ) {
      acc.push(alert)
    }

    return acc
  }, [])
}

const parseVehicles = (vehicles: Array<{ vehicle_id: string }>): Array<VehicleId> =>
  uniq(vehicles.map((vehicle) => vehicle.vehicle_id as VehicleId))

const parseVehiclesGroups = (
  vehiclesGroups: Array<{
    vehicle_id: string
    name: string
    group_vehicle_id: VehicleGroupId
  }>,
) => uniq(vehiclesGroups.map((vehicleGroup) => vehicleGroup.group_vehicle_id))

const alertsApi = {
  fetchAlertTypes({ updateCache = false }) {
    return apiCaller('ct_fleet_get_alerts_types', undefined, {
      updateCache,
    }).then(
      (res: OLD_FetchAlertTypes.ApiOutput): OLD_FetchAlertTypes.Return =>
        parseAlertTypes(res),
    )
  },
  fetchActiveAlerts() {
    return apiCaller('fetch_notification_configuration').then(
      (res: OLD_FetchActiveAlerts.ApiOutput): OLD_FetchActiveAlerts.Return =>
        res.map((a) => parseActiveAlert(a)),
    )
  },
  createAlert(alert: Alert, { distanceInMiles }: { distanceInMiles: boolean }) {
    const normalizedAlert = normalizeAlert(alert, { distanceInMiles })

    if (
      normalizedAlert.notifications[0].triggers.length > 0 ||
      (normalizedAlert.notifications[0].trigger_type_id_values?.length ?? 0) > 0
    ) {
      return apiCaller('ct_fleet_add_alerts_notification', {
        notifications: normalizedAlert.notifications[0],
      })
    } else {
      throw new Error('There was an error finding alert triggers')
    }
  },
  updateAlert(alert: Alert, { distanceInMiles }: { distanceInMiles: boolean }) {
    const normalizedAlert = normalizeAlert(alert, { distanceInMiles })

    if (
      normalizedAlert.notifications[0].triggers.length > 0 ||
      (normalizedAlert.notifications[0].trigger_type_id_values?.length ?? 0) > 0
    ) {
      return apiCaller('ct_fleet_update_alerts_notification', normalizedAlert)
    } else {
      throw new Error('There was an error finding alert triggers')
    }
  },
  deleteActiveAlert({ alertId }: { alertId: string }) {
    return apiCallerNoX('ct_fleet_delete_alerts_notification', {
      alertId,
    })
  },
  fetchAlertDetail({ id, distanceInMiles }: { id: string; distanceInMiles: boolean }) {
    return apiCallerNoX<Ct_fleet_get_alerts_details.ApiOutput>(
      'ct_fleet_get_alerts_details',
      {
        alertId: [id],
      },
    ).then((res) =>
      parseAlertDetailsFromApi(
        res.ct_fleet_get_alerts_details,
        res.ct_fleet_get_alerts_types,
        { distanceInMiles },
      ),
    )
  },
  fetchAlertLog(startDate: string | Date, endDate: FixMeAny, vehicleId = 'All') {
    const params = {
      start_ts: startDate,
      end_ts: endDate,
      vehicle_id: vehicleId,
    }
    return apiCaller('ct_fleet_get_alerts_log', { data: params }).then(
      (res: FetchAlertLog.ApiOutput) => ({
        hasMoreAlertsToFetch: res.ct_fleet_has_more_than_returned,
        nextStartDate: res.ct_fleet_alert_next_page_from_date,
        alerts: parseAlertLog(res.ct_fleet_get_alerts_log),
      }),
    )
  },
}

export default alertsApi

export type FetchActiveAlertsResolved = PromiseResolvedType<
  typeof alertsApi.fetchActiveAlerts
>

export type FetchAlertLogResolved = PromiseResolvedType<typeof alertsApi.fetchAlertLog>

const normalizeLineContactFormValue = (value: LineContactFormValue) => value.join(';')

export const normalizedLineContactFormValueToLineContactFormValue = (
  normalizedLineContact: NormalizedLineContactFormValue,
) => normalizedLineContact.split(';') as LineContactFormValue

export type NormalizedLineContactFormValue = Tagged<
  ReturnType<typeof normalizeLineContactFormValue>,
  'NormalizedLineContactFormValue'
>

const normalizeTelegramContactFormValue = (value: TelegramContactFormValue) =>
  value.join(';')

export const normalizedTelegramContactFormValueToTelegramContactFormValue = (
  normalizedTelegramContact: NormalizedTelegramContactFormValue,
) => normalizedTelegramContact.split(';') as TelegramContactFormValue

export type NormalizedTelegramContactFormValue = Tagged<
  ReturnType<typeof normalizeTelegramContactFormValue>,
  'NormalizedTelegramContactFormValue'
>

export type FetchAlertDetailResolved = PromiseResolvedType<
  typeof alertsApi.fetchAlertDetail
>

export type SevenBoolArray = [
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
]

export type Geofence = {
  id: GeofenceId
  value: GeofenceId
  key: GeofenceId
  label: string
  name: string
  isGroup: false
  address: PositionDescription
}
