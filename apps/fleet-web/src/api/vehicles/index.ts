import { isNil, mapValues, uniqBy } from 'lodash'
import download from 'downloadjs'
import { DateTime } from 'luxon'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { LiteralUnion } from 'type-fest'

import {
  parsePermissions,
  PermissionLookup,
  type PermissionLookupValueOfType,
} from 'api/admin'
import {
  bookingVehicleCategoryIdSchema,
  companyDepartmentIdSchema,
  vehicleGroupIdSchema,
  type LocationId,
  type Settings,
  type VehicleId,
  type VehicleType,
} from 'api/types'
import {
  normalizeGpsFixType,
  normalizeUnsafePositionDescription,
  parseApiOutputVehicleId,
  parseBEBoolean,
  parseCustomFieldsData,
  parseStringifiedCustomFieldPattern,
  type CustomFieldPatternObject,
} from 'api/utils'
import type { FetchTrailersQuery } from 'src/modules/app/GlobalModals/VehicleDetails/Trailers/useTrailerVehicleLinkQuery'
import { bookingStartStopMethodSchema } from 'src/modules/carpool/Settings/BookingManagement/api/types'
import {
  vehicleStatusClassNameSchema,
  type EventStatusClassName,
} from 'src/modules/map-view/map/types'
import type {
  FixMeAny,
  PromiseResolvedType,
  TripsDownloadFileExtension,
} from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'
import { makeHash, safeGetter, toImmutable } from 'src/util-functions/functional-utils'
import { normalizeCoordinate } from 'src/util-functions/map-utils'
import {
  dateToTimeMS,
  formatDateNoTZ,
  getTodayStartEnd,
} from 'src/util-functions/moment-helper'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'
import { isNilOrEmptyString } from 'src/util-functions/string-utils'
import {
  dateTimeToDateString,
  utcTimestampDateRange,
  utcTimestampToMs,
} from 'src/util-functions/time-utils'
import { isTrue } from 'src/util-functions/validation'
import { safeParseFromZodSchema } from 'src/util-functions/zod-utils'

import { getDateDiffByDays } from 'cartrack-utils'
import apiCaller, { apiCallerNoX } from '../api-caller'
import parseColor from '../colors'
import { mifleetApiCaller } from '../mifleet/api-caller'
import {
  vehicleSpeedSourceSchema,
  type Ct_fleet_client_start_inhibit_allowed,
  type Ct_fleet_get_start_inhibit_data,
  type Ct_fleet_start_inhibit_button,
  type Ct_fleet_start_inhibit_with_validation_code,
  type FetchDashboardVehicleList,
  type FetchOdometerUpdateStatus,
  type FetchVehicleCarpoolBookingTripsUI,
  type FetchVehicleCompareTrips,
  type FetchVehicleCurrentGeofences,
  type FetchVehicleDetails,
  type FetchVehicles,
  type FetchVehicleSummaryTripsUI,
  type FetchVehicleTripHistoryForPastDays,
} from './types'
import { filterSpecialLicensesIdForVehicle } from './utils'

let specialInstructionsUtil = false

function parseVehiclePositionRelatedData(
  vehicle: Record<string, FixMeAny> & { vehicle_type: VehicleType },
) {
  const ignition = Boolean(vehicle.ignition) && Boolean(vehicle.ignition - 1)
  const speed = Number(vehicle.speed)
  const lastTimestamp = utcTimestampToMs(vehicle.event_ts)
  const lastIgnition = utcTimestampToMs(vehicle.last_ignition)

  return {
    ignition,
    speed,
    lastTimestamp,
    latitude: normalizeCoordinate(vehicle.latitude),
    longitude: normalizeCoordinate(vehicle.longitude),
    iconColor: vehicle.colour_code && parseColor(vehicle.colour_code, true),
    type: vehicle.vehicle_type,
    registration: vehicle.registration,
    bearing: vehicle.bearing,
    lastIgnition,
  }
}

const immobiliseVehicleStatusMap = {
  'DISABLE START INHIBIT': 'isImmobilised',
  'Waiting for Validation SMS': 'otp_validation',
  'Validation not received': 'otp_isExpired',
  'ENABLE START INHIBIT': 'isNotImmobilised',
  'Waiting for Immobilisation state': 'validating',
  'Testing the unit': 'validating',
  'Attempt to change immolisation failed, Timeout': 'invalid',
  'Failed to set to the State Requested!': 'invalid',
  'invalid code': 'otp_invalid',
  'No Answer from unit Test Procedure': 'invalid',
  'Attempt to change immolisation failed': 'invalid',
  'System is currently under Control': 'invalid',
} as const

function parseVehicleCompareEvents(
  rawEvents: FetchVehicleCompareTrips.ApiOutput[string]['ct_fleet_get_vehicle_timeline_events']['events'],
) {
  return rawEvents.map((evt) => {
    const lat = normalizeCoordinate(evt.lat)
    const lng = normalizeCoordinate(evt.lng)

    return {
      bearing: Number(evt.bearing),
      clock: Number(evt.clock) * 1000,
      fuelLevel: Number(evt.fuelLevel),
      fuelPercent: evt.fuelPercent,
      fuelUsed: Number(evt.fuelUsed),
      id: evt.id || (evt as FixMeAny).seq_no || (Math.random() * 10000).toString(),
      lat,
      latitude: lat,
      lng,
      longitude: lng,
      manifoldPressure: Number(evt.manifoldPressure),
      odometer: evt.odometer ? Number(evt.odometer) / 1000 : evt.odometer,
      oilPressure: Number(evt.oilPressure),
      oilTemp: Number(evt.oilTemp),
      position: normalizeUnsafePositionDescription(evt.position),
      gpsFixType: normalizeGpsFixType(evt.gpsFixType),
      roadSpeed: Number(evt.roadSpeed),
      rpm: Number(evt.rpm),
      speed: Number(evt.speed),
      statusClassName: evt.statusClassName,
      temp1: evt.temp1,
      temp2: evt.temp2,
      temp3: evt.temp3,
      temp4: evt.temp4,
      time: dateToTimeMS(evt.eventTimestamp),
      unitTemp: Number(evt.unitTemp),
      waterTemp: Number(evt.waterTemp),
      xAccl: evt.xAccl,
      yAccl: evt.yAccl,
      zAccl: evt.zAccl,
    }
  })
}

function parseVehicleTripHistory(
  rawTripHistory: FetchVehicleTripHistoryForPastDays.ApiOutput['ct_fleet_get_vehicle_trips_summary'],
  rawEventSummary: FetchVehicleTripHistoryForPastDays.ApiOutput['event_summary_badges'],
) {
  if (!rawTripHistory) return []
  return rawTripHistory.map((raw) => ({
    id: raw.trip_id,
    startLocation: normalizeUnsafePositionDescription(raw.start_location),
    endLocation: normalizeUnsafePositionDescription(raw.end_location),
    startGpsFixType: normalizeGpsFixType(raw.start_gps_fix_type),
    endGpsFixType: normalizeGpsFixType(raw.end_gps_fix_type),
    distance: raw.trip_distance / 1000,
    startTime: new Date(raw.start_timestamp),
    endTime: new Date(raw.end_timestamp),
    driverFullName: raw.driver_name,
    type: raw.client_trip_type_id,
    notes: raw.client_trip_description,
    extraNotes: raw.client_trip_additional_description,
    driverId: raw.client_driver_id,
    linkageType: raw.linkage_type_enum,
    avatar: raw.logo_image_base64,
    harshCounts: {
      speedingCount: Number(raw.events_speeding),
      brakingCount: Number(raw.events_braking),
      cornerCount: Number(raw.events_corner),
      accelCount: Number(raw.events_acceleration),
    },
    summaryEvents: rawEventSummary,
    flagged: raw.trip_flagged === '1',
    motionTrip: raw.trip_start_type_id === '1',
  }))
}

export function parseVehicleListVehicle(
  vehicle: FetchVehicles.Vehicle,
  { daysUntilLostVisibility }: { daysUntilLostVisibility: number },
) {
  const {
    ignition,
    iconColor,
    speed,
    type,
    lastTimestamp,
    lastIgnition,
    longitude,
    latitude,
  } = parseVehiclePositionRelatedData(vehicle)

  const descriptions = [
    vehicle.client_vehicle_description,
    vehicle.client_vehicle_description1,
    vehicle.client_vehicle_description2,
  ].slice(
    0,
    // eslint-disable-next-line no-nested-ternary
    vehicle.client_vehicle_description2
      ? 3
      : vehicle.client_vehicle_description1
        ? 2
        : 1,
  )

  const hardwareTypes = vehicle.terminal_description
    ? makeHash(vehicle.terminal_description.map((s) => s.replaceAll('"', '')))
    : {}

  const roadSpeed = Number(vehicle.road_speed)

  // Based on daysUntilLostVisibility app setting
  const wasActiveInLastXDays = (() => {
    if (daysUntilLostVisibility === 0) {
      return true
    }

    return vehicle.event_ts
      ? Math.abs(
          DateTime.fromJSDate(new Date(vehicle.event_ts)).diffNow().as('days'),
        ) <= daysUntilLostVisibility
      : false
  })()

  return {
    currentStatusSinceDate: vehicle.current_status_since_ts
      ? new Date(vehicle.current_status_since_ts)
      : null,
    active:
      vehicle.out_special_instructions !== '' && specialInstructionsUtil
        ? safeGetter(
            () =>
              (vehicle.out_special_instructions as FixMeAny).slice(0, 15) !==
              '$$deactivated$$',
            false,
          )
        : true,
    bearing: Number(vehicle.bearing),
    code: vehicle.licence_code,
    color: vehicle.colour,
    defaultDriver:
      vehicle.default_driver === '0                                   '
        ? ''
        : vehicle.default_driver,
    defaultTimeZone: vehicle.default_timezone,
    descriptions,
    description: vehicle.client_vehicle_description || '',
    description1: vehicle.client_vehicle_description1 || '',
    description2: vehicle.client_vehicle_description2 || '',
    /* This field is here to prevent vehicle details page from crashing. Due to the sub optimal code it's done there, the form always expects a "customFields" array to be present.
    Even though it doesn't make sense in the context of this parser (for vehicle_list_v3) */
    customFields: [],
    driverName: vehicle.driver_name,
    engineNumber: vehicle.enginenr,
    expirationDate:
      vehicle.licence_expiry_date && utcTimestampToMs(vehicle.licence_expiry_date),
    hardwareTypes,
    homeGeofence: vehicle.home_geofence,
    iconColor,
    id: parseApiOutputVehicleId(vehicle.vehicle_id),
    ignition,
    issueDate:
      vehicle.licence_issued_date && utcTimestampToMs(vehicle.licence_issued_date),
    lastTimestamp,
    lastIgnition,
    latitude,
    longitude,
    make: vehicle.manufacturer,
    maxSpeed: vehicle.max_speed,
    model: vehicle.model,
    year: vehicle.modelyear,
    monthlyMileageLimit: vehicle.monthly_mileage_limit,
    name: vehicle.vehicle_name,
    odometer: vehicle.odometer ? Number(vehicle.odometer) / 1000 : vehicle.odometer,
    overspeedThreshold: Number(vehicle.overspeed_threshold),
    positionDescription: normalizeUnsafePositionDescription(
      vehicle.position_description,
    ),
    privacyModeEnabled:
      isTrue(vehicle.privacy_total) || isTrue(vehicle.privacy_location),
    rating: Number(vehicle.driver_behaviour) || 0,
    registration: vehicle.registration,
    specialInstructions: vehicle.out_special_instructions || '',
    speed,
    speedSource: safeParseFromZodSchema(
      vehicleSpeedSourceSchema,
      vehicle.speed_source,
      {
        defaultValue: () => null,
      },
    ),
    startInhibitAllowed: isTrue(vehicle.start_inhibit_allowed),
    statusClassName: safeParseFromZodSchema(
      vehicleStatusClassNameSchema,
      vehicle.statusClassName,
      { defaultValue: () => 'no-signal' satisfies EventStatusClassName },
    ) as LiteralUnion<EventStatusClassName, string>,
    terminalSerial: vehicle.terminal_serial,
    tollingTagId: vehicle.tolling_tag_id,
    type,
    VIN: vehicle.chassisnr,
    useSVREvents: hardwareTypes.Fleet
      ? false
      : isTrue(vehicle.can_poll_svr) || hardwareTypes['SVR Units'],
    can_poll_svr: isTrue(vehicle.can_poll_svr),
    // Defects
    newDefectsCount: Number(vehicle.out_new_defects),
    repairedDefectsCount: Number(vehicle.out_repaired_defects),
    inspectedDefectsCount: Number(vehicle.out_inspected_defects),
    time: dateToTimeMS(vehicle.event_ts),
    wasActiveInLastXDays,
    roadSpeed: Number.isNaN(roadSpeed) ? 0 : roadSpeed,
    taxiSensorNo: vehicle.taxiSensorNo,
    eventDate: vehicle.has_open_case ? vehicle.open_case_date : vehicle.event_ts,
    rpm: isNilOrEmptyString(vehicle.rpm) ? null : Number(vehicle.rpm),
    waterTemp: isNilOrEmptyString(vehicle.water_temp)
      ? null
      : Number(vehicle.water_temp),
    oilTemp: isNilOrEmptyString(vehicle.oil_temp) ? null : Number(vehicle.oil_temp),
    unitRawClock: vehicle.clock,
    refrigerator: vehicle.refrigerator,
    taximeterStatus: vehicle.taximeterStatus,
    alertsActions: vehicle.alertsActions,
    diagnosticErrorCount: isNil(vehicle.diagnostic_error_count)
      ? 0
      : Number(vehicle.diagnostic_error_count),
    isCamera: isTrue(vehicle.is_camera),
    cameraOnline: isTrue(vehicle.camera_online),
    isWifi: isTrue(vehicle.is_wifi),
    runningStatus: vehicle.running_status,
    isSingleTrip: vehicle.is_single_trip,
    cameraType: vehicle.camera_type,
    gpsFixType: normalizeGpsFixType(vehicle.gps_fix_type),
    carpoolStatus: vehicle.carpool_status,
    carpoolEnabled: parseBEBoolean(vehicle.is_pool_active, { fallback: false }),
    subUserVisibility: Boolean(vehicle.subuser_visibility),
    hasOpenControlroomCase: Boolean(vehicle.has_open_case),
    departmentIds: new Set(
      Array_filterMap(
        isNilOrEmptyString(vehicle.department_ids) ? [] : vehicle.department_ids,
        (rawId, { RemoveSymbol }) => {
          const result = companyDepartmentIdSchema.safeParse(rawId)
          if (!result.success) {
            return RemoveSymbol
          }

          return result.data
        },
      ),
    ),
    carpoolStartStopBookingMethod: safeParseFromZodSchema(
      bookingStartStopMethodSchema,
      vehicle.carpool_booking_method,
      {
        defaultValue: () => null,
      },
    ),
    bookingCategoryId: safeParseFromZodSchema(
      bookingVehicleCategoryIdSchema,
      vehicle.vehicle_category_id,
      { defaultValue: () => null },
    ),

    vehicleClassId: vehicle.vehicle_class?.id ?? null,
    specialLicenses:
      vehicle.specialLicenses?.filter((license) =>
        filterSpecialLicensesIdForVehicle(license.id),
      ) ?? [],
    commonPool: parseBEBoolean(vehicle.is_common_pool, { fallback: false }),
    hasMikey: parseBEBoolean(vehicle.has_mikey, { fallback: false }),
    inMaintenance: parseBEBoolean(vehicle.in_maintenance, { fallback: false }),
    terminalMsisdn: vehicle.terminal_msisdn,
  }
}

function parseVehicle(
  vehicle: Record<string, any> & {
    vehicle_type: VehicleType
  } & Pick<
      FetchVehicleDetails.Vehicle,
      | 'third_party_terminal_serial'
      | 'has_mifleet'
      | 'vehicleOverspeedThreshold'
      | 'department_ids'
      | 'required_special_licenses'
    >,
  customData: Array<CustomFieldPatternObject>,
) {
  const {
    ignition,
    iconColor,
    speed,
    type,
    lastTimestamp,
    lastIgnition,
    longitude,
    latitude,
  } = parseVehiclePositionRelatedData(vehicle)

  const descriptions = [
    vehicle.client_vehicle_description,
    vehicle.client_vehicle_description1,
    vehicle.client_vehicle_description2,
  ].slice(
    0,
    // eslint-disable-next-line no-nested-ternary
    vehicle.client_vehicle_description2
      ? 3
      : vehicle.client_vehicle_description1
        ? 2
        : 1,
  )

  const hardwareTypes = vehicle.terminal_description
    ? makeHash(vehicle.terminal_description.map((s: FixMeAny) => s.replaceAll('"', '')))
    : {}

  const roadSpeed = Number(vehicle.road_speed)
  return {
    active:
      vehicle.out_special_instructions !== '' && specialInstructionsUtil
        ? safeGetter(
            () => vehicle.out_special_instructions.slice(0, 15) !== '$$deactivated$$',
            false,
          )
        : true,
    bearing: Number(vehicle.bearing),
    code: vehicle.licence_code,
    color: vehicle.colour,
    defaultDriver:
      vehicle.default_driver === '0                                   '
        ? ''
        : vehicle.default_driver,
    defaultTimeZone: vehicle.default_timezone,
    descriptions,
    description: vehicle.client_vehicle_description || '',
    description1: vehicle.client_vehicle_description1 || '',
    description2: vehicle.client_vehicle_description2 || '',
    customFields: parseCustomFieldsData(vehicle, customData),
    driverName: vehicle.driver_name as string | null,
    engineNumber: vehicle.enginenr,
    expirationDate:
      vehicle.licence_expiry_date && utcTimestampToMs(vehicle.licence_expiry_date),
    fuelCapacity: vehicle.fuel_capacity,
    fuelTargetConsumptionCombined: vehicle.fuel_target_consumption,
    fuelAverageConsumptionCombined: vehicle.fuel_avg_consumption_combined,
    fuelTypeId: vehicle.vehicle_fuel_type_id,
    engineTypeId: vehicle.vehicle_engine_type_id,
    auxFuelCapacity: vehicle.fuel_capacity_aux,
    tankShape: vehicle.tank_type,
    hardwareTypes,
    homeGeofence: vehicle.home_geofence,
    iconColor,
    id: vehicle.vehicle_id,
    ignition,
    issueDate:
      vehicle.licence_issued_date && utcTimestampToMs(vehicle.licence_issued_date),
    lastTimestamp,
    lastIgnition,
    latitude,
    longitude,
    make: vehicle.manufacturer,
    maxSpeed: vehicle.max_speed,
    model: vehicle.model,
    year: vehicle.modelyear,
    monthlyMileageLimit: vehicle.monthly_mileage_limit,
    name: vehicle.vehicle_name,
    nextServiceDate:
      vehicle.service_due_date && utcTimestampToMs(vehicle.service_due_date),
    nextServiceOdo: vehicle.service_due_odo && Number(vehicle.service_due_odo),
    nextServiceType: vehicle.service_due_type,
    odometer: vehicle.odometer
      ? Math.round(Number(vehicle.odometer) / 1000)
      : vehicle.odometer,
    overspeedThreshold: Number(vehicle.overspeed_threshold),
    positionDescription: vehicle.position_description
      ? vehicle.position_description.replace(', United States', '')
      : '',
    privacyModeEnabled:
      isTrue(vehicle.privacy_total) ||
      isTrue(vehicle.privacy_location) ||
      isTrue(vehicle.privacy_mode_enabled),
    rating: Number(vehicle.driver_behaviour) || 0,
    registration: vehicle.registration,
    specialInstructions: vehicle.out_special_instructions || '',
    speed,
    startInhibitAllowed: isTrue(vehicle.start_inhibit_allowed),
    statusClassName: vehicle.statusClassName,
    terminalSerial: vehicle.terminal_serial,
    thirdPartyTerminalSerial:
      vehicle.third_party_terminal_serial === '' ||
      isNil(vehicle.third_party_terminal_serial)
        ? null
        : vehicle.third_party_terminal_serial,
    tollingTagId: vehicle.tolling_tag_id,
    type,
    VIN: vehicle.chassisnr,
    useSVREvents: hardwareTypes.Fleet
      ? false
      : isTrue(vehicle.can_poll_svr) || hardwareTypes['SVR Units'],
    can_poll_svr: isTrue(vehicle.can_poll_svr),
    // Defects
    newDefectsCount: Number(vehicle.out_new_defects),
    repairedDefectsCount: Number(vehicle.out_repaired_defects),
    inspectedDefectsCount: Number(vehicle.out_inspected_defects),
    time: dateToTimeMS(vehicle.event_ts),
    roadSpeed: Number.isNaN(roadSpeed) ? 0 : roadSpeed,
    taxiSensorNo: vehicle.taxiSensorNo,
    eventDate: vehicle.has_open_case ? vehicle.open_case_date : vehicle.event_ts,
    rpm: vehicle.rpm,
    waterTemp: vehicle.water_temp,
    oilTemp: vehicle.oil_temp,
    unitRawClock: vehicle.clock,
    refrigerator: vehicle.refrigerator,
    taximeterStatus: vehicle.taximeterStatus,
    alertsActions: vehicle.alertsActions,
    diagnosticErrorCount: isNil(vehicle.diagnostic_error_count)
      ? 0
      : Number(vehicle.diagnostic_error_count),
    isCamera: isTrue(vehicle.is_camera),
    cameraOnline: isTrue(vehicle.camera_online),
    isWifi: isTrue(vehicle.is_wifi),
    runningStatus: vehicle.running_status,
    isSingleTrip: vehicle.is_single_trip,
    cameraType: vehicle.camera_type,
    bookingVehicleTypeId: vehicle.booking_vehicle_type_id,
    defaultSiteLocationId: vehicle.default_site_location_id,
    driverLicenseTypeId: vehicle.driver_license_type_id,
    departmentIds: isNilOrEmptyString(vehicle.department_ids)
      ? null
      : Array_filterMap(vehicle.department_ids, (rawId, { RemoveSymbol }) => {
          const result = companyDepartmentIdSchema.safeParse(rawId)
          if (!result.success) {
            return RemoveSymbol
          }

          return result.data
        }),
    specialLicenseTypes: (vehicle.required_special_licenses ?? []).filter((license) =>
      filterSpecialLicensesIdForVehicle(license),
    ),
    bookingAllocationPriority: vehicle.booking_allocation_priority,
    carpool_status: vehicle.carpool_status,
    hasMifleet: vehicle.has_mifleet,
    gearboxType: vehicle.vehicle_gearbox_type_id,
    workCounterType: vehicle.vehicle_work_counter_type_id,
    engineCapacity: vehicle.engine_capacity,
    costCentre: vehicle.cost_centre_id,
    privacyMode: vehicle.privacy_mode_enabled,
    vehicleOverspeedThreshold: vehicle.vehicleOverspeedThreshold
      ? vehicle.vehicleOverspeedThreshold.map((t) => ({
          terminalSerial: t.terminal_serial,
          overspeedBuzzer: t.overspeed_buzzer,
          threshold1: t.threshold_1,
          threshold2: t.threshold_2,
        }))
      : null,
  }
}

function parseMifleetVehicleDetails(vehicleSpecifications: FixMeAny) {
  return isNil(vehicleSpecifications)
    ? {}
    : {
        details: {
          detailId: vehicleSpecifications.details_id,
          gearboxType: vehicleSpecifications.vehicle_gearbox_type_id,
          fuelType: vehicleSpecifications.vehicle_fuel_type_id,
          workCounterType: vehicleSpecifications.vehicle_work_counter_type_id,
          costCentre: vehicleSpecifications.cost_centre_id,
          costCentreName: vehicleSpecifications.cost_centre,
          engineCapacity: vehicleSpecifications.engine_capacity,
          fuelTankCapacity: vehicleSpecifications.fuel_tank_capacity,
        },
        types: {
          gearboxTypes: vehicleSpecifications.gearboxTypes,
          fuelTypes: vehicleSpecifications.fuelTypes,
          workCounterTypes: vehicleSpecifications.workCounterTypes,
          costCentres: vehicleSpecifications.costCentre,
        },
      }
}

function parseVehicles(
  rawVehicles: Array<FetchVehicles.Vehicle>,
  rawGroups: FetchVehicles.ApiOutput['ct_fleet_get_vehiclelist_vgroup'],
  daysUntilLostVisibility: number,
) {
  let hardwareTypes = {}
  const vehicles = uniqBy(rawVehicles, (v) => v.vehicle_id).map((v) => {
    const parsed = parseVehicleListVehicle(v, {
      daysUntilLostVisibility,
    })
    hardwareTypes = { ...hardwareTypes, ...parsed.hardwareTypes }
    return parsed
  })

  const vehicleIdsSet = new Set(vehicles.map((v) => v.id))

  const groups = Array_filterMap(
    Object.keys(rawGroups || {}),
    (rawGroupId, { RemoveSymbol }) => {
      const parseResult = vehicleGroupIdSchema.safeParse(rawGroupId)
      if (!parseResult.success) {
        return RemoveSymbol
      }

      const groupId = parseResult.data
      const {
        name = '',
        description = '',
        vehicles: vehicleIds,
        permission_id,
      } = rawGroups[groupId]

      // Vehicle group should only include vehicles from the vehicle list
      const actualVehicleIds = (vehicleIds ?? []).filter((v) => vehicleIdsSet.has(v))

      const permissionId = match<typeof permission_id, PermissionLookupValueOfType>(
        permission_id,
      )
        .with(
          '0',
          '1',
          '2',
          '3',
          '4',
          '5',
          '6',
          '7',
          (value) => Number(value) as PermissionLookupValueOfType,
        )
        // As in the old logic, if permission_id does not belong to any PermissionLookupValueOfType, it is the main user.
        .otherwise(() => PermissionLookup['view_edit_remove'])

      return {
        groupId,
        id: groupId,
        name,
        description,
        itemIds: actualVehicleIds,
        itemIdsSet: new Set(actualVehicleIds),
        permissionId: permissionId,
        permissions: parsePermissions(permissionId),
      }
    },
  )

  return toImmutable({
    vehicles,
    groups,
    hardwareTypes: Object.keys(hardwareTypes),
  })
}

function parseVehicleTypes(
  rawVehicleTypes: FetchVehicles.ApiOutput['ct_fleet_get_vehicle_types'],
) {
  const vehicleTypes = rawVehicleTypes.map((type) => ({
    id: type.type_id,
    description: type.type_description,
  }))
  return { vehicleTypes }
}

function parseToInteger(value: string | null): number | null {
  if (!value) return null

  const parsedValue = Number.parseInt(value, 10)

  return Number.isNaN(parsedValue) ? null : parsedValue
}

function parseSensors(rawSensors: Array<FixMeAny>) {
  return rawSensors
    .map((s) => ({
      activityLevel: s.active_level,
      alarmPriorityId: s.alarm_priority_id,
      analogOffset: s.analog_offset,
      diagnosticSensor: isTrue(s.diagnostic_sensor),
      id: s.vehicle_sensors_id,
      isAnalog: isTrue(s.type_analog_digital),
      isTimeBased: s.sensor_type !== 'CEMENT MAG INV',
      maxTrigger: s.max_trigger,
      minTrigger: s.min_trigger,
      maxLevel: parseToInteger(s.max_level),
      minLevel: parseToInteger(s.min_level),
      multiplier: s.multiplier,
      name: s.sensor_name,
      priority: s.alarm_priority,
      sensorNumber: s.sensor_no,
      sensorType: s.sensor_type,
      singleSided: isTrue(s.single_sided),
      sensorTypeId: s.sensor_type_id,
      validGeofence: s.valid_geofence_id,
      invalidGeofence: s.invalid_geofence_id,
    }))
    .sort((a, b) => a.sensorNumber - b.sensorNumber)
}

function parseOdometerUpdateStatus(result: FetchOdometerUpdateStatus.Response) {
  return {
    odometerUpdateStatus: result,
  }
}

function normalizeMifleetVehicle(vehicle: Record<string, any>, isNew: boolean) {
  const normalizedVehicle = {
    vehicle_id: vehicle.id,
    vehicle_gearbox_type_id: vehicle.gearboxType,
    vehicle_fuel_type_id: vehicle.fuelType,
    vehicle_work_counter_type_id: vehicle.workCounterType,
    cost_centre_id: vehicle.costCentre,
    engine_capacity: vehicle.engineCapacity,
    fuel_tank_capacity: vehicle.fuelTankCapacity,
    is_new: isNew,
  }

  return isNew
    ? normalizedVehicle
    : {
        details_id: vehicle.detailId,
        ...normalizedVehicle,
      }
}

function normalizeGroup(g: Record<string, any>) {
  return {
    group_vehicle_id: g.id,
    name: g.name,
  }
}

function getNumFromRobot(
  robot: FetchDashboardVehicleList.VehicleTrafficLight | null,
): 0 | 1 | 2 | 3 {
  switch (robot?.trim()) {
    case 'R': {
      return 1
    }
    case 'Y': {
      return 2
    }
    case 'G': {
      return 3
    }
    default: {
      return 0
    }
  }
}

const parseDashboardVehicleList = ({
  dashBoardVehicleList,
  sensorByName,
}: FetchDashboardVehicleList.ApiOutput) =>
  toImmutable({
    dashboardVehicles: dashBoardVehicleList.map((item) => ({
      chassisNr: item.chassisnr,
      clock: item.clock,
      colour: item.colour,
      defaultDriver: item.default_driver,
      driverName: item.client_driver_description,
      driverId: item.driver_id,
      engineNr: item.enginenr,
      eventTs: item.event_ts,
      fuelLevel: item.out_fuel_level
        ? Math.round(Number(item.out_fuel_level) * 10) / 10
        : '',
      fuelUsed: Number(item.fuel_used),
      gpsFixType: Number(item.gps_fix_type) === 3 ? 'Locked' : 'None',
      homeGeofence: item.home_geofence,
      id: parseApiOutputVehicleId(item.vehicle_id),
      longitude: Number(item.longitude).toFixed(6) as `${number}`,
      latitude: Number(item.latitude).toFixed(6) as `${number}`,
      manufacturer: item.manufacturer,
      model: item.model,
      name: item.vehicle_name,
      odometer: Number(item.odometer),
      positionDescription: item.position_description,
      registration: item.registration,
      score: getNumFromRobot(item.vehicle_traffic_light),
      speed: item.speed,
      status: !item.ignition ? '0' : item.ignition,
      temp1: item.temp1,
      temp2: item.temp2,
      temp3: item.temp3,
      temp4: item.temp4,
      vehicleDescription1: ctIntl.formatMessage({
        id: item.client_vehicle_description ?? '',
      }),
      vehicleDescription2: item.client_vehicle_description1,
      vehicleDescription3: item.client_vehicle_description2,
      vehicleGeofence: !isNil(item.vehicle_geofence)
        ? item.vehicle_geofence.replaceAll(',', ', ')
        : null,
      sensors: item.sensors.map((sensor) => {
        const base = {
          eventTime: sensor.event_time,
          legend: sensor.legend,
          sensorNo: sensor.sensor_no,
          sensorName: sensor.sensor_name,
          sensorTypeId: sensor.sensor_type_id,
          vehicleSensorsId: sensor.vehicle_sensors_id,
        }

        return sensor.sensorType === 'ANALOG'
          ? ({
              ...base,
              sensorType: 'ANALOG',
              value: sensor.value,
              [sensor.sensor_type]: sensor.value,
            } as const)
          : ({
              ...base,
              sensorType: 'DIGITAL',
              legendValue: sensor.legendValue,
              [sensor.sensor_type]: sensor.legendValue,
            } as const)
      }),
    })),
    sensorsByName: sensorByName.map(({ name, sensorTypeId }) => {
      const sensorTypeIdArray =
        typeof sensorTypeId === 'string' ? [sensorTypeId] : (sensorTypeId ?? [])
      return {
        name,
        sensorTypeId: new Set(sensorTypeIdArray),
      }
    }),
  })

function parseVehicleDbits(bits: Array<FixMeAny>) {
  return bits.map((b) => ({
    bitPosition: b.dbit_pos,
    onEvent: isTrue(b.dbit_on_event),
    typeId: b.dbit_type_id,
    name: b.dbit_name,
    description: b.dbit_desc,
    sensorTypeId: b.sensor_type_id,
    sensorInverted: isTrue(b.sensor_type_inverted),
    sensorDescription: b.sensor_description,
    vehicleId: b.vehicle_id,
  }))
}

function parseDbitResources(sensorTypes: Array<FixMeAny>, dbitTypes: Array<FixMeAny>) {
  return {
    sensorTypes: sensorTypes.map((t) => ({
      name: t.sensor_description,
      label: t.sensor_description,
      value: t.sensor_type_id,
    })),
    dbitTypes: dbitTypes.map((t) => ({
      name: t.dbit_name,
      label: t.dbit_name,
      value: t.dbit_type_id,
    })),
  }
}

function parseImmobiliseVehicleData(dataArr: Ct_fleet_get_start_inhibit_data) {
  if (!dataArr || dataArr === 'f' || dataArr.length === 0) {
    return false as const
  }
  const data = dataArr[0]
  if (data === 'f') {
    return false as const
  }

  const statusKey = R.keys(immobiliseVehicleStatusMap).find((s) =>
    data.out_status?.includes(s),
  )

  return toImmutable({
    outButtonEnabled: data.out_button_enabled,
    outInputEnabled: data.out_input_enabled,
    outPrevious: data.out_previous,
    outStatus: data.out_status,
    outIsImmobilise: isTrue(data.out_start_ihhibit_active),
    keyStatus: statusKey ? immobiliseVehicleStatusMap[statusKey] : undefined,
  })
}

function parseVehicleLinkedTrailers(response: FixMeAny) {
  const mapTrailers = (t: FetchTrailersQuery.ApiOutput) =>
    t.map((innerTrailer) => ({
      clientTrailerId: innerTrailer.client_trailer_id,
      userId: innerTrailer.user_id,
      registration: innerTrailer.registration,
      description: innerTrailer.description,
      id: innerTrailer.client_trailer_id,
    }))

  return {
    linkedTrailers: mapTrailers(response.ct_fleet_fetch_vehicle_linked_trailers || []),
    availableTrailers: mapTrailers(response.ct_fleet_fetch_trailers || []),
  }
}

const vehiclesAPI = {
  fetchDashboardVehicleList() {
    return apiCallerNoX<FetchDashboardVehicleList.ApiOutput>(
      'ct_fleet_get_dashboard_vehicle_list',
    ).then((list) => parseDashboardVehicleList(list))
  },

  updateMifleetVehicleDetails(vehicle: FixMeAny) {
    return mifleetApiCaller(
      'vehicles/vehicleDetailsJSON.php?action=update',
      'POST',
      normalizeMifleetVehicle(vehicle, false),
    )
  },

  createMifleetVehicleDetails(vehicle: FixMeAny) {
    return mifleetApiCaller(
      'vehicles/vehicleDetailsJSON.php?action=create',
      'POST',
      normalizeMifleetVehicle(vehicle, true),
    )
  },

  updateVehicleLink(vehicleId: VehicleId, expireTs: Date) {
    return apiCaller('ct_fleet_update_client_vehicle_link', {
      vehicleId,
      expireTs,
    })
  },

  updateVehicleGroup(group: FixMeAny) {
    return apiCaller('ct_fleet_rename_client_group_name', {
      groupData: normalizeGroup(group),
      type: 'vehicle',
    })
  },

  updateVehicleGroupItems(groupId: FixMeAny, itemIds: FixMeAny) {
    return apiCaller('ct_fleet_client_save_vehicle_group', {
      groupVehicleId: groupId,
      VehicleIds: itemIds,
    })
  },

  updateVehicleTripFlag(id: VehicleId, startTs: Date, isFlagged: boolean) {
    return apiCaller('ct_fleet_set_tripflag', {
      vehicle_id: id,
      start_ts: startTs,
      is_flagged: isFlagged,
    })
  },

  deleteVehicleGroup(groupId: FixMeAny) {
    return apiCallerNoX('ct_fleet_delete_client_group_vehicle_driver', {
      type: 'vehicle',
      groupVehicleDriverIds: [groupId],
    })
  },

  createVehicleGroup(name: FixMeAny) {
    return apiCaller('ct_fleet_create_client_vehicle_driver_group', {
      vehicleGroupData: {
        name,
        group_driver_id: '',
        group_vehicle_id: '',
        group_geofence_id: '',
        group_status_id: '',
        description: '',
      },
      type: 'vehicle',
    }).then((res: FixMeAny) => res.groupData[0].group_vehicle_id)
  },

  fetchVehicles({ daysUntilLostVisibility }: { daysUntilLostVisibility: number }) {
    const settings = JSON.parse(
      localStorage.getItem('userSettings') || '{}',
    ) as Settings.UserSettingsRaw

    if (settings.specialInstructionsUtil !== undefined) {
      specialInstructionsUtil = settings.specialInstructionsUtil as boolean
      localStorage.setItem(
        'specialInstructionsUtil',
        settings.specialInstructionsUtil as string,
      )
    } else {
      specialInstructionsUtil =
        localStorage.getItem('specialInstructionsUtil') === 'true'
    }
    return apiCallerNoX<FetchVehicles.ApiOutput>('ct_fleet_get_vehiclelist_v3').then(
      (res) => ({
        ...parseVehicles(
          res.ct_fleet_get_vehiclelist,
          res.ct_fleet_get_vehiclelist_vgroup,
          daysUntilLostVisibility,
        ),
        ...parseVehicleTypes(res.ct_fleet_get_vehicle_types),
      }),
    )
  },

  fetchVehiclePositions(vehicleIds: Array<VehicleId>, areSharedVehicles = false) {
    return apiCaller(
      'ct_fleet_get_vehicle_positions',
      {
        vehicleIds,
      },
      { noX: true },
    ).then((res: FixMeAny) =>
      mapValues(res.ct_fleet_get_vehicle_positions, (p) =>
        areSharedVehicles // For now, only shared vehicles obtain events data from this endpoint
          ? parseVehiclePositionRelatedData(p)
          : {
              latitude: normalizeCoordinate(p.latitude),
              longitude: normalizeCoordinate(p.longitude),
            },
      ),
    )
  },

  fetchVehicleCompareTrips(
    trips: Array<{
      vehicle_id: string | number
      start_ts: string
      end_ts: string
    }>,
  ) {
    return apiCaller('ct_fleet_get_compare_timeline_data', {
      request_array: trips,
      isSRV: false,
    }).then((data: FetchVehicleCompareTrips.ApiOutput) =>
      Object.values(data).map((trip) => ({
        events: parseVehicleCompareEvents(
          trip.ct_fleet_get_vehicle_timeline_events.events,
        ),
        timeline: trip.ct_fleet_get_timeline_data.timeline,
        stats: trip.ct_fleet_get_timeline_data.totals,
        dayStart: trip.ct_fleet_get_timeline_data.dayStart,
        dayEnd: trip.ct_fleet_get_timeline_data.dayEnd,
      })),
    )
  },

  fetchVehicleDetails(vehicleId?: string) {
    return apiCaller('ct_fleet_get_vehicle_details', {
      vehicle_id: vehicleId,
    }).then(
      (res: FetchVehicleDetails.ApiOutput) =>
        res.ct_fleet_get_vehicle_details[0] && {
          fleetVehicle: parseVehicle(
            res.ct_fleet_get_vehicle_details[0],
            parseStringifiedCustomFieldPattern(res.custom_data),
          ),
          vehicleCostCentres: res.cost_centres,
          vehicleWorkCounterTypes: res.vehicle_work_counter_type,
          vehicleGearboxTypes: res.vehicle_gearbox_types,
          vehicleTankShapes: res.ct_fleet_get_tank_types,
          vehicleEngineTypes: res.vehicle_engine_types,
          vehicleFuelTypes: res.vehicle_fuel_types,
          consumptionUnits: res.consumption_units,
          siteLocations: (res.site_locations || []).map((loc) => ({
            ...loc,
            label: loc.site_location_name,
            value: loc.site_location_id.toString() as LocationId,
          })),
        },
    )
  },

  fetchMifleetVehicleDetails(vehicleId: FixMeAny) {
    return mifleetApiCaller(
      `vehicles/vehicleDetailsJSON.php?action=read&vehicleId=${vehicleId}`,
    ).then((res: FixMeAny) =>
      res.objectList && res.objectList.length > 0
        ? parseMifleetVehicleDetails(res.objectList[0])
        : null,
    )
  },

  fetchVehicleTripHistoryForPastDays(vehicleId: FixMeAny, days: FixMeAny) {
    const date = new Date()
    // Increment the date because the parser needs it to be one ahead in order to capture today's trip activity

    const { startMs } = utcTimestampDateRange(date, -days + 1)

    return apiCaller('ct_fleet_get_vehicle_trips_summary', {
      vehicle_id: vehicleId,
      date_range_start: dateTimeToDateString(startMs),
      date_range_end: dateTimeToDateString(date),
    }).then((res: FetchVehicleTripHistoryForPastDays.ApiOutput) =>
      parseVehicleTripHistory(
        res.ct_fleet_get_vehicle_trips_summary,
        res.event_summary_badges,
      ),
    )
  },

  fetchVehicleSummaryTripsUI(
    vehicleId: VehicleId,
    startDate: Date,
    endDate: Date,
    isSingleTrip: boolean,
  ) {
    return apiCaller(
      'ct_fleet_get_vehicle_trips_summary_panel',
      {
        vehicle_id: vehicleId,
        date_range_start: DateTime.fromJSDate(startDate).startOf('day').toISO(),
        date_range_end: DateTime.fromJSDate(endDate).endOf('day').toISO(),
        is_single_trip: isSingleTrip,
      },
      { noX: true },
    ).then((res: FetchVehicleSummaryTripsUI.ApiOutput) =>
      toImmutable(
        res.map(({ date, drivingDistance, trips, ...rest }) => ({
          ...rest,
          drivingDistance: Number(drivingDistance),
          date: new Date(date),
          trips: trips ?? [],
        })),
      ),
    )
  },

  fetchVehicleCarpoolBookingTripsUI({
    vehicleId,
    startDate,
    endDate,
  }: {
    vehicleId: VehicleId
    startDate: Date
    endDate: Date
  }) {
    return apiCaller(
      'ct_fleet_get_vehicle_carpool_booking',
      {
        vehicle_id: vehicleId,
        date_range_start: formatDateNoTZ(startDate, 'YYYY-MM-DD'),
        date_range_end: formatDateNoTZ(endDate, 'YYYY-MM-DD'),
      },
      { noX: true },
    ).then((res: FetchVehicleCarpoolBookingTripsUI.ApiOutput) => res)
  },

  fetchVehiclePrebuiltTrips(
    vehicleId: FixMeAny,
    startDate: FixMeAny,
    endDate: FixMeAny /* , type */,
  ) {
    const { startDate: sDate, endDate: eDate } = getTodayStartEnd(startDate, endDate)

    return apiCaller('ct_fleet_get_vehicle_trips_summary', {
      vehicle_id: vehicleId,
      date_range_start: sDate,
      date_range_end: eDate,
      exclude_incomplete_trips: true,
    }).then((res: FixMeAny) => ({
      trips: parseVehicleTripHistory(
        res.ct_fleet_get_vehicle_trips_summary,
        res.event_summary_badges,
      ),
      tripsStartInMs: dateToTimeMS(sDate),
      tripsNDays: getDateDiffByDays(sDate, eDate) + 1,
    }))
  },

  fetchVehicleSensors(vehicleId: FixMeAny) {
    return apiCaller('ct_fleet_get_vehicle_sensors', {
      vehicle_id: vehicleId,
    }).then((res: FixMeAny) => parseSensors(res.ct_fleet_get_vehicle_sensors))
  },

  fetchVehicleCurrentGeofences(vehicleIds: Array<string>) {
    return apiCaller('ct_fleet_get_vehicle_geofences', {
      vehicleIds,
    }).then(
      (res: FetchVehicleCurrentGeofences.ApiOutput) =>
        res.ct_fleet_get_vehicle_geofences,
    )
  },

  fetchVehicleRawData(vehicleId: FixMeAny, startTs: FixMeAny, endTs: FixMeAny) {
    return apiCaller('ct_fetch_vehicle_debug_data', {
      vehicleId,
      startTs,
      endTs,
    }).then((res: FixMeAny) => res.ct_fetch_vehicle_debug_data)
  },

  downloadTripSpecial({
    type,
    id,
    registration,
    startTime,
    endTime,
    zeroKmTrips,
    flaggedTripsOnly,
  }: {
    type: TripsDownloadFileExtension
    id: string
    startTime: Date
    endTime: Date
    registration: string
    zeroKmTrips?: boolean
    flaggedTripsOnly?: boolean
  }) {
    const isXLS = type === 'xls'
    const startDateTime = DateTime.fromJSDate(startTime)
    const endDateTime = DateTime.fromJSDate(endTime)

    // The BE currently only accepts the date in the format 'yyyy-MM-dd HH:mm:ss'. Otherwise we would send the timezone, as usual.
    const startTs = startDateTime.toFormat('yyyy-MM-dd HH:mm:ss')
    const endTs = endDateTime.toFormat('yyyy-MM-dd HH:mm:ss')
    const args = isXLS
      ? {
          id,
          startTs,
          endTs,
          zeroKmTrips,
          flaggedTripsOnly,
        }
      : { id, registration, startTs, endTs, zeroKmTrips, flaggedTripsOnly }

    const formatDownloadNameDates = (): string => {
      if (startDateTime.hasSame(endDateTime, 'day')) {
        return `${startDateTime.toFormat('D')}__${startDateTime.toFormat(
          't',
        )}-${endDateTime.toFormat('t')}`
      }
      if (
        startDateTime.toMillis() === startDateTime.startOf('day').toMillis() &&
        endDateTime.toMillis() === endDateTime.endOf('day').toMillis()
      ) {
        return `${startDateTime.toFormat('D')}_${endDateTime.toFormat('D')}`
      }

      return `${startDateTime.toFormat('F')}__${endDateTime.toFormat('F')}`
    }

    return apiCaller(
      isXLS ? 'ct_fleet_get_triplist_export' : 'ct_fleet_download_' + type,
      args,
      { noParse: true },
    )
      .then((res: FixMeAny) => res.blob())
      .then((result: Blob) => {
        download(result, `${registration} ${formatDownloadNameDates()}.${type}`)
        return 'Download Successful'
      })
  },

  fetchSensorTypes() {
    return apiCaller('ct_fleet_get_sensorType').then((res: FixMeAny) =>
      res.ct_fleet_get_sensorType.map((t: FixMeAny) => ({
        label: t.sensor_description,
        name: t.sensor_description,
        value: t.sensor_type_id,
      })),
    )
  },

  fetchAlarmPriorities() {
    return apiCaller('ct_fleet_get_alarmPriority').then((result: FixMeAny) =>
      result.ct_fleet_get_alarmPriority.map((p: FixMeAny) => ({
        label: p.description,
        name: p.description,
        value: p.alarm_priority_id,
      })),
    )
  },

  fetchVehicleDbits(vehicleId: FixMeAny) {
    return apiCaller('ct_fleet_get_vehicle_dbits', {
      vehicleId,
    }).then((res: FixMeAny) =>
      parseVehicleDbits(res.ct_fleet_get_vehicle_dbits_vehicle_dbit_list),
    )
  },

  fetchDbitResources() {
    return apiCaller('ct_fleet_get_vehicle_dbits_data').then((res: FixMeAny) =>
      parseDbitResources(
        res.ct_fleet_get_vehicle_dbits_sensor_types || [],
        res.ct_fleet_get_vehicle_dbits_dbit_types || [],
      ),
    )
  },

  fetchImmobiliseVehicleData(vehicleId: VehicleId) {
    return apiCaller('ct_fleet_get_start_inhibit_data', {
      vehicleId,
    }).then((res: FixMeAny) =>
      parseImmobiliseVehicleData(res.ct_fleet_get_start_inhibit_data),
    )
  },

  async immobiliseVehicleButton(vehicleId: VehicleId): Promise<
    | {
        authenticationType: 'otp'
        immobiliseVehicleData: ParsedImmobiliseVehicleData
      }
    | {
        authenticationType: 'password'
      }
  > {
    const res = await apiCallerNoX<Ct_fleet_start_inhibit_button.ApiOutput>(
      'ct_fleet_start_inhibit_button',
      { vehicleId },
    )

    switch (res.authentication_type) {
      case 'otp': {
        return {
          authenticationType: 'otp',
          immobiliseVehicleData: parseImmobiliseVehicleData(
            res.ct_fleet_get_start_inhibit_data,
          ),
        }
      }
      case 'password': {
        return {
          authenticationType: 'password',
        }
      }
    }
  },

  async immobiliseVehicleWithValidationCode({
    otpCodeOrPassword,
    vehicleId,
  }: {
    vehicleId: VehicleId
    otpCodeOrPassword: string
  }): Promise<
    | {
        authenticationType: 'otp'
        immobiliseVehicleData: ParsedImmobiliseVehicleData
      }
    | {
        authenticationType: 'password'
        authenticationStatus: 'success'
        immobiliseVehicleData: ParsedImmobiliseVehicleData
      }
    | {
        authenticationType: 'password'
        authenticationStatus: 'failure'
      }
  > {
    const res =
      await apiCallerNoX<Ct_fleet_start_inhibit_with_validation_code.ApiOutput>(
        'ct_fleet_start_inhibit_with_validation_code',
        {
          vehicleId,
          pin: otpCodeOrPassword,
        },
      )

    switch (res.authentication_type) {
      case 'otp': {
        return {
          authenticationType: 'otp',
          immobiliseVehicleData: parseImmobiliseVehicleData(
            res.ct_fleet_get_start_inhibit_data,
          ),
        }
      }
      case 'password': {
        if (res.status === 'success') {
          return {
            authenticationType: 'password',
            authenticationStatus: 'success',
            immobiliseVehicleData: parseImmobiliseVehicleData(
              res.ct_fleet_get_start_inhibit_data,
            ),
          }
        }
        return {
          authenticationType: 'password',
          authenticationStatus: 'failure',
        }
      }
    }
  },

  immobiliseVehicleAllowed(vehicleId: VehicleId) {
    return apiCallerNoX<Ct_fleet_client_start_inhibit_allowed.ApiOutput>(
      'ct_fleet_client_start_inhibit_allowed',
      {
        vehicleId,
      },
    ).then((res) => parseImmobiliseVehicleData(res.ct_fleet_get_start_inhibit_data))
  },

  fetchOdometerUpdateStatus(vehicleId: VehicleId) {
    return apiCallerNoX<FetchOdometerUpdateStatus.Response>(
      'ct_fetch_odometer_update_status',
      {
        vehicle_id: vehicleId,
      },
    ).then(parseOdometerUpdateStatus)
  },

  fetchListTrailers() {
    return apiCaller('ct_fleet_fetch_trailers').then((res) =>
      parseVehicleLinkedTrailers({
        ct_fleet_fetch_trailers: res.ct_fleet_fetch_vehicle_linked_trailers,
      }),
    )
  },

  fetchVehicleTrailers(vehicleId: VehicleId) {
    return apiCaller('ct_fleet_fetch_trailers_for_vehicle', {
      vehicle_id: vehicleId,
    }).then((res) => parseVehicleLinkedTrailers(res))
  },
}

export default vehiclesAPI

export type FetchVehicleSensorsResolved = PromiseResolvedType<
  typeof vehiclesAPI.fetchVehicleSensors
>

export type FetchVehicleCurrentGeofencesResolved = PromiseResolvedType<
  typeof vehiclesAPI.fetchVehicleCurrentGeofences
>

export type FetchVehiclesResolved = PromiseResolvedType<
  typeof vehiclesAPI.fetchVehicles
>

export type FetchVehicleTripHistoryForPastDaysResolved = PromiseResolvedType<
  typeof vehiclesAPI.fetchVehicleTripHistoryForPastDays
>

export type FetchVehicleCompareTripsResolved = PromiseResolvedType<
  typeof vehiclesAPI.fetchVehicleCompareTrips
>

export type FetchDashboardVehiclesResolved = PromiseResolvedType<
  typeof vehiclesAPI.fetchDashboardVehicleList
>
export type FetchVehicleDetailsResolved = PromiseResolvedType<
  typeof vehiclesAPI.fetchVehicleDetails
>

export type VehicleDetailsFleetVehicle = FetchVehicleDetailsResolved['fleetVehicle']

export type FetchVehicleCarpoolBookingTripsUIResolved = PromiseResolvedType<
  typeof vehiclesAPI.fetchVehicleCarpoolBookingTripsUI
>

export type FetchImmobiliseVehicleDataResolved = PromiseResolvedType<
  typeof vehiclesAPI.fetchImmobiliseVehicleData
>

export type ImmobiliseVehicleButtonResolved = PromiseResolvedType<
  typeof vehiclesAPI.immobiliseVehicleButton
>

export type ParsedImmobiliseVehicleData = ReturnType<typeof parseImmobiliseVehicleData>
